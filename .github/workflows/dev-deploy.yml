# 파일 위치: .github/workflows/dev-deploy.yml
# 목적: CI/CD 파이프라인 구성 - 시맨틱 버저닝, 빌드, 배포 자동화
# 주요 기능:
# - GitHub App 인증을 통한 레포지토리 간 자동화
# - 시맨틱 버저닝을 통한 버전 관리
# - AWS ECR을 통한 컨테이너 이미지 관리
# - 쿠버네티스 매니페스트 자동 업데이트
# - Git 태그 → 소스코드(build.gradle.kts) 레벨 -> Docker 이미지로 이어지는 일관된 버전 관리 체계가 구축
#   . 모든 계층에서 동일한 버전 번호를 사용하여 추적성 확보.
# - 브랜치별 독립적 버전 관리 (develop -> dev-v, stage -> stage-v)

name: CI/CD Pipeline with Semantic Versioning

# 워크플로우 트리거 설정
on:
  push:
    # develop 또는 stage 브랜치에 push될 때만 실행
    branches: [ "develop", "stage" ]
    paths-ignore:
      - '**.md'
      - '.github/ISSUE_TEMPLATE/**'

# GitHub Actions에 필요한 권한 설정
permissions:
  id-token: write    # OIDC 토큰을 사용하기 위한 권한
  contents: write    # 태그 생성을 위한 권한

# 환경 변수 설정
env:
  GRADLE_OPTS: "-Dorg.gradle.daemon=false -Dorg.gradle.parallel=true -Dorg.gradle.caching=true"
  DOCKER_BUILDKIT: "1"
  TZ: "Asia/Seoul"

jobs:
  # 환경 설정 및 버전 관리 job
  setup-and-versioning:
    runs-on: ubuntu-latest
    outputs:
      new_version: ${{ steps.semver.outputs.new_version }}
      branch_name: ${{ steps.branch-name.outputs.branch_name }}
      version_number: ${{ steps.semver.outputs.version_number }}
      environment: ${{ steps.set-env.outputs.environment }}
      tag_prefix: ${{ steps.set-env.outputs.tag_prefix }}
    steps:
      # 코드 체크아웃
      - name: Checkout code
        uses: actions/checkout@v3
        with:
          fetch-depth: 0    # 전체 히스토리 필요 (태그 정보를 위해)

      # 브랜치 이름 저장
      - name: Get branch name
        id: branch-name
        run: |
          BRANCH_NAME=${GITHUB_REF#refs/heads/}
          echo "branch_name=${BRANCH_NAME}" >> "$GITHUB_OUTPUT"
          echo "Current branch: $BRANCH_NAME"

      # 환경 설정 - 브랜치별 환경 변수를 모두 여기서 설정
      - name: Set environment variables
        id: set-env
        run: |
          BRANCH_NAME="${{ steps.branch-name.outputs.branch_name }}"
          
          # 브랜치별 환경 설정
          case "$BRANCH_NAME" in
            "develop")
              echo "environment=dev" >> "$GITHUB_OUTPUT"
              echo "tag_prefix=dev-v" >> "$GITHUB_OUTPUT"
              ;;
            "stage")
              echo "environment=stage" >> "$GITHUB_OUTPUT"
              echo "tag_prefix=stage-v" >> "$GITHUB_OUTPUT"
              ;;
            *)
              echo "배포되지 않는 브랜치"
              exit 1
          esac
          
          echo "Selected environment: ${{ steps.set-env.outputs.environment }}"
          echo "Using tag prefix: ${{ steps.set-env.outputs.tag_prefix }}"

      # 시맨틱 버전 생성
      - name: Generate Semantic Version
        id: semver
        shell: bash
        run: |
          # 환경 변수에서 태그 접두사 가져오기
          TAG_PREFIX="${{ steps.set-env.outputs.tag_prefix }}"
          echo "Using prefix: $TAG_PREFIX"
          
          # 마지막 태그 가져오기 (같은 접두사를 가진 태그만)
          LAST_TAG=$(git tag -l "${TAG_PREFIX}*" | sort -V | tail -n 1)
          if [ -z "$LAST_TAG" ]; then
            # 해당 접두사의 태그가 없으면 기본값 설정
            LAST_TAG="${TAG_PREFIX}0.0.0"
            echo "No previous tag with prefix ${TAG_PREFIX} found. Starting with: $LAST_TAG"
          else
            echo "Last tag with prefix ${TAG_PREFIX}: $LAST_TAG"
          fi
          
          # 접두사 제거하고 버전 번호만 추출
          VERSION=${LAST_TAG#${TAG_PREFIX}}
          
          # 버전 컴포넌트 추출
          IFS='.' read -r MAJOR MINOR PATCH <<< "$VERSION"
          
          # 숫자가 아닌 경우 기본값 설정
          if ! [[ "$MAJOR" =~ ^[0-9]+$ ]]; then MAJOR=0; fi
          if ! [[ "$MINOR" =~ ^[0-9]+$ ]]; then MINOR=0; fi
          if ! [[ "$PATCH" =~ ^[0-9]+$ ]]; then PATCH=0; fi
          
          # 패치 버전 증가
          NEW_PATCH=$((PATCH + 1))
          
          # 새 버전 형식 설정 (메이저.마이너는 유지하고 패치 버전만 증가)
          NEW_VERSION="${TAG_PREFIX}${MAJOR}.${MINOR}.${NEW_PATCH}"
          VERSION_NUMBER="${MAJOR}.${MINOR}.${NEW_PATCH}"
          
          echo "new_version=${NEW_VERSION}" >> "$GITHUB_OUTPUT"
          echo "version_number=${VERSION_NUMBER}" >> "$GITHUB_OUTPUT"

  # 빌드 및 배포 job
  build-and-push:
    needs: setup-and-versioning
    runs-on: ubuntu-latest
    steps:
      # Git 사용자 설정 - 모든 Git 관련 작업에 공통으로 사용
      - name: Configure Git
        run: |
          git config --global user.name 'GitHub Actions Bot'
          git config --global user.email 'github-actions[bot]@users.noreply.github.com'

      # 메인 레포지토리 체크아웃
      - name: Checkout code
        uses: actions/checkout@v3

      # JDK 캐싱 설정
      - name: Cache JDK
        uses: actions/cache@v4
        id: jdk-cache
        with:
          path: ${{ runner.temp }}/jdk-cache
          key: jdk-${{ runner.os }}-17-temurin
          restore-keys: |
            jdk-${{ runner.os }}-17-

      # Gradle 캐시 키 생성 (버전 정보 제외)
      - name: Generate cache key from build.gradle.kts without version
        id: gradle-cache-key
        run: |
          # version = "x.y.z" 패턴을 제외하고 build.gradle.kts 해시 계산
          FILTERED_CONTENT=$(grep -v 'version = "[0-9]*\.[0-9]*\.[0-9]*"' build.gradle.kts)
          HASH=$(echo "$FILTERED_CONTENT" | sha256sum | cut -d ' ' -f 1)
          echo "hash=$HASH" >> $GITHUB_OUTPUT

      # JDK 설정 (Gradle 캐싱 없이)
      - name: Set up JDK 17
        uses: actions/setup-java@v3
        with:
          java-version: '17'
          distribution: 'temurin'
          java-package: jdk
          jdkFile: ${{ steps.jdk-cache.outputs.cache-hit == 'true' && runner.temp }}/jdk-cache || ''

      # Gradle 종속성 수동 캐싱
      - name: Cache Gradle dependencies
        uses: actions/cache@v4
        with:
          path: |
            ~/.gradle/caches
            ~/.gradle/wrapper
          key: gradle-deps-${{ runner.os }}-${{ steps.gradle-cache-key.outputs.hash }}
          restore-keys: |
            gradle-deps-${{ runner.os }}-

      # Gradle 환경 변수 설정
      - name: Configure Gradle
        run: |
          mkdir -p ~/.gradle
          echo "org.gradle.caching=true" >> ~/.gradle/gradle.properties
          echo "org.gradle.parallel=true" >> ~/.gradle/gradle.properties
          echo "org.gradle.daemon=false" >> ~/.gradle/gradle.properties
          echo "org.gradle.workers.max=4" >> ~/.gradle/gradle.properties
          echo "org.gradle.jvmargs=-Duser.timezone=Asia/Seoul" >> ~/.gradle/gradle.properties

      - name: Update Gradle Version
        run: |
          VERSION_NUMBER="${{ needs.setup-and-versioning.outputs.version_number }}"
          if [ ! -f "build.gradle.kts" ]; then
            echo "build.gradle.kts 파일을 찾을 수 없습니다."
            exit 1
          fi
          if grep -q 'version = "' build.gradle.kts; then
            sed -i "s/version = \".*\"/version = \"${VERSION_NUMBER}\"/" build.gradle.kts
          else
            sed -i "1i version = \"${VERSION_NUMBER}\"" build.gradle.kts
          fi
          echo "Gradle 버전이 ${VERSION_NUMBER}로 업데이트되었습니다."

      - name: Build with Gradle
        run: |
          chmod +x ./gradlew
          TZ=Asia/Seoul ./gradlew bootJar -x test
        env:
          GITHUB_ACTOR: ${{ secrets.ORG_GITHUB_ACTOR }}
          GITHUB_TOKEN: ${{ secrets.ORG_GITHUB_PAT }}
          GITHUB_REPOSITORY: ${{ github.repository }}
          DATABASE_URL: ${{ needs.setup-and-versioning.outputs.environment == 'dev' && secrets.DEV_DATABASE_URL || needs.setup-and-versioning.outputs.environment == 'stage' && secrets.STAGE_DATABASE_URL || secrets.DATABASE_URL }}
          DATABASE_USERNAME: ${{ needs.setup-and-versioning.outputs.environment == 'dev' && secrets.DEV_DATABASE_USERNAME || needs.setup-and-versioning.outputs.environment == 'stage' && secrets.STAGE_DATABASE_USERNAME || secrets.DATABASE_USERNAME }}
          DATABASE_PASSWORD: ${{ needs.setup-and-versioning.outputs.environment == 'dev' && secrets.DEV_DATABASE_PASSWORD || needs.setup-and-versioning.outputs.environment == 'stage' && secrets.STAGE_DATABASE_PASSWORD || secrets.DATABASE_PASSWORD }}
          AWS_REGION: ${{ secrets.AWS_REGION }}

      # AWS 자격 증명 설정
      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v3
        with:
          role-to-assume: >-
            arn:aws:iam::${{
              needs.setup-and-versioning.outputs.branch_name == 'develop' && secrets.AWS_ACCOUNT_ID_DEV ||
              needs.setup-and-versioning.outputs.branch_name == 'stage' && secrets.AWS_ACCOUNT_ID_STAGE ||
              secrets.AWS_ACCOUNT_ID
            }}:role/github-actions-role
          aws-region: ${{ secrets.AWS_REGION }}

      # Amazon ECR 로그인
      - name: Login to Amazon ECR
        id: login-ecr
        uses: aws-actions/amazon-ecr-login@v1
        with:
          registry-type: private
          mask-password: true

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3

      # Docker 이미지 빌드 및 ECR 푸시
      - name: Build, tag, and push image to Amazon ECR
        uses: docker/build-push-action@v4
        with:
          context: .
          push: true
          tags: |
            ${{ steps.login-ecr.outputs.registry }}/${{ github.event.repository.name }}:${{ needs.setup-and-versioning.outputs.new_version }}
            ${{ steps.login-ecr.outputs.registry }}/${{ github.event.repository.name }}:latest
          build-args: |
            TZ=Asia/Seoul
            DATABASE_URL=${{ needs.setup-and-versioning.outputs.environment == 'dev' && secrets.DEV_DATABASE_URL || needs.setup-and-versioning.outputs.environment == 'stage' && secrets.STAGE_DATABASE_URL || secrets.DATABASE_URL }}
            DATABASE_USERNAME=${{ needs.setup-and-versioning.outputs.environment == 'dev' && secrets.DEV_DATABASE_USERNAME || needs.setup-and-versioning.outputs.environment == 'stage' && secrets.STAGE_DATABASE_USERNAME || secrets.DATABASE_USERNAME }}
            DATABASE_PASSWORD=${{ needs.setup-and-versioning.outputs.environment == 'dev' && secrets.DEV_DATABASE_PASSWORD || needs.setup-and-versioning.outputs.environment == 'stage' && secrets.STAGE_DATABASE_PASSWORD || secrets.DATABASE_PASSWORD }}
            AWS_REGION=${{ secrets.AWS_REGION }}
          cache-from: type=gha
          cache-to: type=gha,mode=max

  # k8s 매니페스트 업데이트 전용 job
  update-k8s-manifests:
    needs: [ setup-and-versioning, build-and-push ]
    runs-on: ubuntu-latest
    # k8s 매니페스트 업데이트에만 동시성 제한 적용
    concurrency:
      group: k8s-manifest-update
      cancel-in-progress: false
    steps:
      # Git 사용자 설정
      - name: Configure Git
        run: |
          git config --global user.name 'GitHub Actions Bot'
          git config --global user.email 'github-actions[bot]@users.noreply.github.com'

      # GitHub App 인증 토큰 생성
      - name: Generate GitHub App Token
        id: github-app-token
        uses: tibdex/github-app-token@v2
        with:
          app_id: ${{ secrets.APP_ID }}
          private_key: ${{ secrets.APP_PRIVATE_KEY }}

      # k8s 매니페스트 레포지토리 체크아웃
      - name: Checkout k8s manifests repository
        uses: actions/checkout@v3
        with:
          repository: komca-dev-org/komca-k8s-config
          ref: main
          token: ${{ steps.github-app-token.outputs.token }}
          # 필요한 파일만 가져오기 위한 sparse checkout 설정
          sparse-checkout: |
            apps/${{ github.event.repository.name }}/overlays/${{ needs.setup-and-versioning.outputs.environment }}
          sparse-checkout-cone-mode: false
          fetch-depth: 1

      # Kustomize 매니페스트 업데이트
      - name: Update Kustomize patch
        env:
          GITHUB_TOKEN: ${{ steps.github-app-token.outputs.token }}
          ENVIRONMENT: ${{ needs.setup-and-versioning.outputs.environment }}
          K8S_REPO: "komca-dev-org/komca-k8s-config"
          K8S_DEPLOY_PATH: "apps/${{ github.event.repository.name }}/overlays/${{ needs.setup-and-versioning.outputs.environment }}/patch-deployment.yaml"
        run: |
          # 서비스 이름은 GitHub 저장소 이름 사용
          SERVICE_NAME="${{ github.event.repository.name }}"
          echo "서비스 이름: $SERVICE_NAME"
          
          # 매니페스트 파일 경로 구성
          MANIFEST_PATH="${{ env.K8S_DEPLOY_PATH }}"
          echo "매니페스트 경로: $MANIFEST_PATH"
          
          # 파일이 존재하는지 확인
          if [ ! -f "$MANIFEST_PATH" ]; then
            echo "경고: 매니페스트 파일을 찾을 수 없습니다: $MANIFEST_PATH"
            find apps -name "patch-deployment.yaml" | sort
            exit 1
          fi
          
          # 새 버전 값 저장
          NEW_VERSION="${{ needs.setup-and-versioning.outputs.new_version }}"
          
          # 5회 시도
          for i in {1..5}; do
            echo "시도 $i/5: 매니페스트 업데이트 중..."
          
            # 전체 리셋 및 원격 저장소에서 새로 가져오기
            git fetch origin main
            git reset --hard origin/main
          
            # 이미지 태그를 새 버전으로 업데이트
            sed -i -E "s|(image:[ ]*.*/$SERVICE_NAME:)[^\"' ]*|\1${NEW_VERSION}|g" "$MANIFEST_PATH"
          
            # 변경 내용 로그
            echo "변경 내용:"
            git diff "$MANIFEST_PATH" || true
          
            # 변경사항이 있으면 커밋
            if ! git diff --quiet "$MANIFEST_PATH"; then
              git add "$MANIFEST_PATH"
              git commit -m "🔄 이미지 업데이트: $SERVICE_NAME -> ${NEW_VERSION}"
          
              # 변경사항 푸시 시도
              if git push origin main; then
                echo "✅ k8s 매니페스트 업데이트 완료: $SERVICE_NAME (${NEW_VERSION})"
                exit 0
              fi
          
              echo "푸시 실패, 재시도 중..."
              sleep 3
            else
              echo "변경 사항 없음: 이미지 경로가 이미 최신 상태입니다."
              exit 0
            fi
          done
          
          echo "최대 재시도 횟수 초과, 업데이트 실패"
          exit 1

  # 태그 생성 job
  create-tag:
    needs: [setup-and-versioning, update-k8s-manifests]
    runs-on: ubuntu-latest
    steps:
      # 코드 체크아웃
      - name: Checkout code
        uses: actions/checkout@v3

      # Git 사용자 설정
      - name: Configure Git
        run: |
          git config --global user.name 'GitHub Actions Bot'
          git config --global user.email 'github-actions[bot]@users.noreply.github.com'

      # 새로운 Git 태그 생성
      - name: Create Git Tag
        run: |
          git tag ${{ needs.setup-and-versioning.outputs.new_version }}
          git push origin ${{ needs.setup-and-versioning.outputs.new_version }}

  update-and-commit-gradle-version:
    needs: [setup-and-versioning, build-and-push, create-tag]
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v3

      - name: Configure Git
        run: |
          git config --global user.name 'GitHub Actions Bot'
          git config --global user.email 'github-actions[bot]@users.noreply.github.com'

      - name: Update and Commit Gradle Version
        run: |
          VERSION_NUMBER="${{ needs.setup-and-versioning.outputs.version_number }}"
          if [ ! -f "build.gradle.kts" ]; then
            echo "build.gradle.kts 파일을 찾을 수 없습니다."
            exit 1
          fi
          if grep -q 'version = "' build.gradle.kts; then
            sed -i "s/version = \".*\"/version = \"${VERSION_NUMBER}\"/" build.gradle.kts
          else
            sed -i "1i version = \"${VERSION_NUMBER}\"" build.gradle.kts
          fi
          if ! git diff --quiet build.gradle.kts; then
            git add build.gradle.kts
            git commit -m "chore: update gradle version to ${VERSION_NUMBER}"
            git push
          else
            echo "build.gradle.kts에 변경사항이 없습니다."
          fi


# 필요한 GitHub Secrets:
# - APP_ID: GitHub App의 ID
# - APP_PRIVATE_KEY: GitHub App의 private key
# - AWS_ACCOUNT_ID_DEV: 개발 환경 AWS 계정 ID
# - AWS_ACCOUNT_ID_STAGE: 스테이징 환경 AWS 계정 ID
# - AWS_ACCOUNT_ID: 기본 AWS 계정 ID (폴백으로 사용)
# - AWS_REGION: AWS 리전 (ap-northeast-2)
# - DATABASE_URL: 기본 데이터베이스 URL (폴백용)
# - DATABASE_USERNAME: 기본 데이터베이스 사용자 이름 (폴백용)
# - DATABASE_PASSWORD: 기본 데이터베이스 비밀번호 (폴백용)
# - DEV_DATABASE_URL: 개발 환경 데이터베이스 URL
# - DEV_DATABASE_USERNAME: 개발 환경 데이터베이스 사용자 이름
# - DEV_DATABASE_PASSWORD: 개발 환경 데이터베이스 비밀번호
# - STAGE_DATABASE_URL: 스테이징 환경 데이터베이스 URL
# - STAGE_DATABASE_USERNAME: 스테이징 환경 데이터베이스 사용자 이름
# - STAGE_DATABASE_PASSWORD: 스테이징 환경 데이터베이스 비밀번호
# - ORG_GITHUB_ACTOR: GitHub 패키지 접근을 위한 사용자 이름
# - ORG_GITHUB_PAT: GitHub 패키지 접근을 위한 개인 액세스 토큰