import java.util.TimeZone

plugins {
    java
    id("org.springframework.boot") version "3.4.2"
    id("io.spring.dependency-management") version "1.1.7"
}

TimeZone.setDefault(TimeZone.getTimeZone("Asia/Seoul"))

springBoot {
    buildInfo()
}

val githubOrg = "komca-dev-org"

group = "kr.or.komca"
version = "0.0.148"

java {
    toolchain {
        languageVersion = JavaLanguageVersion.of(17)
    }
}


configurations {
    compileOnly {
        extendsFrom(configurations.annotationProcessor.get())
    }
    // Spring Boot 의 기본 로깅 제외
    all {
        exclude(group = "org.springframework.boot", module = "spring-boot-starter-logging")
    }
}

dependencies {

    // ********************************* BOM ********************************* //
    implementation(platform("kr.or.komca.common:dependencies-bom:0.6.60"))
    // *********************************************************************** //

    // ********************** 의존성 라이브러리 설정 (BOM 참조) ********************* //
    implementation("kr.or.komca.common:core:0.10.59")
    implementation("kr.or.komca.common:logging")
    implementation("kr.or.komca.common:interfaces")
    // *********************************************************************** //

    // ************************ 로컬 테스트용 라이브러리 설정 ************************ //
//    implementation("kr.or.komca.common:core:0.0.1-SNAPSHOT")
//    implementation("kr.or.komca.common:logging:0.0.1-SNAPSHOT")
//    implementation("kr.or.komca.common:interfaces:0.0.1-SNAPSHOT")
    // *********************************************************************** //

    // ************************ 스프링 의존성 라이브러리 설정 ************************ //
    testImplementation("org.springframework.boot:spring-boot-starter-test")
    testRuntimeOnly("org.junit.platform:junit-platform-launcher")

    // Lombok 테스트 지원
    testCompileOnly("org.projectlombok:lombok")
    testAnnotationProcessor("org.projectlombok:lombok")

    // Spring Security Test
    testImplementation("org.springframework.security:spring-security-test")

    compileOnly("org.projectlombok:lombok")
    runtimeOnly("com.oracle.database.jdbc:ojdbc11")
    annotationProcessor("org.projectlombok:lombok")
    implementation("org.springframework.boot:spring-boot-starter-security")
    implementation("org.springframework.boot:spring-boot-starter-validation")
    implementation("org.springframework.boot:spring-boot-starter-web")
    implementation("org.mybatis.spring.boot:mybatis-spring-boot-starter:3.0.3")
    implementation("org.springframework.boot:spring-boot-starter-actuator")

    // properties
    implementation("org.springframework.boot:spring-boot-configuration-processor")
    annotationProcessor("org.springframework.boot:spring-boot-configuration-processor")

    // Logging - Log4j2만 사용
    implementation("org.springframework.boot:spring-boot-starter-log4j2")

    // Spring AOP
    implementation("org.springframework.boot:spring-boot-starter-aop")

    // JWT
    implementation("io.jsonwebtoken:jjwt-api:0.12.3")
    runtimeOnly("io.jsonwebtoken:jjwt-impl:0.12.3")
    runtimeOnly("io.jsonwebtoken:jjwt-jackson:0.12.3")

    // Swagger
    implementation("org.springdoc:springdoc-openapi-starter-webmvc-ui:2.8.5")

    //  maven.org.xmlunit:xmlunit-core 라이브러리의 보안 취약점 해결
    implementation("org.xmlunit:xmlunit-core:2.10.0")

    implementation("com.github.pagehelper:pagehelper-spring-boot-starter:2.1.0")

    // *********************************************************************** //

}

repositories {
    mavenCentral()
    mavenLocal()    // Local 테스트 용

    // **** 의존성 라이브러리 정보들 입력 **** //
    // 공통으로 사용되는 GitHub Packages 설정
    // ~/.gradle/gradle.properties 파일에 Git Credential 입력
    fun configureGithubCredentials(repository: MavenArtifactRepository) {
        repository.credentials {
            username = System.getenv("GITHUB_ACTOR") ?: project.findProperty("gpr.user") as String? ?: ""
            password = System.getenv("GITHUB_TOKEN") ?: project.findProperty("gpr.key") as String? ?: ""
        }
    }

    // GitHub 저장소 목록 설정
    val githubRepos = listOf(
        "common-interfaces",
        "dependencies-bom",
        "common-auth",
        "common-logging",
    )

    // GitHub 저장소 등록
    githubRepos.forEach { repo ->
        maven {
            name = "GitHubPackages-$repo"
            url = uri("https://maven.pkg.github.com/$githubOrg/$repo")
            configureGithubCredentials(this)
        }
    }
}

tasks.withType<Test> {
    useJUnitPlatform()
}

