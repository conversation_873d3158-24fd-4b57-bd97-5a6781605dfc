FROM eclipse-temurin:17-jre-jammy
WORKDIR /app

# 타임존 설정 추가
ENV TZ=Asia/Seoul
RUN ln -snf /usr/share/zoneinfo/$TZ /etc/localtime && echo $TZ > /etc/timezone

# JAR 파일 복사 (워크플로우에서 빌드된 파일)
COPY build/libs/*.jar app.jar

# 런타임 스테이지 환경 변수
ARG DATABASE_URL
ARG DATABASE_USERNAME
ARG DATABASE_PASSWORD
ARG AWS_REGION

ENV DATABASE_URL=${DATABASE_URL}
ENV DATABASE_USERNAME=${DATABASE_USERNAME}
ENV DATABASE_PASSWORD=${DATABASE_PASSWORD}
ENV AWS_REGION=${AWS_REGION}

EXPOSE 8080
ENTRYPOINT ["java", "-jar", "app.jar"]