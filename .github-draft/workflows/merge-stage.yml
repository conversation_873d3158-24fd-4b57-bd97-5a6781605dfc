# .github/workflows/merge-stage.yml
# release 브랜치가 stage로 머지될 때 실행되는 워크플로우

name: Stage Publish [stage-merge]

# 동시성 제어
concurrency:
  group: ${{ github.workflow }}-${{ github.ref }}
  cancel-in-progress: false  # 기존 워크플로우를 취소하지 않고 대기

on:
  push:
    branches:
      - stage  # 스테이징 환경

permissions:
  id-token: write
  contents: write

jobs:
  # PR 머지 커밋일때만 동작하기 위한 변수 설정
  # 머지 커밋인지 확인하고, PR 소스 브랜치와 현재 브랜치를 가져온다.
  detect_merge:
    name: 머지 커밋 정보 확인
    uses: komca-dev-org/common-workflows/.github/workflows/common-merge-detection.yml@v0.2.0
    secrets:
      ORG_GITHUB_PAT: ${{ secrets.ORG_GITHUB_PAT }}

  set_environment:
    name: 브랜치 환경 파싱
    needs: [ detect_merge ]
    uses: komca-dev-org/common-workflows/.github/workflows/common-set-environment.yml@v0.2.0
    with:
      source_branch: ${{ needs.detect_merge.outputs.source_branch }}
      target_branch: ${{ needs.detect_merge.outputs.target_branch }}

  # 코드 검증
  validate_code:
    name: 코드 검증
    needs: [ detect_merge, set_environment]
    if: success()
    uses: komca-dev-org/common-workflows/.github/workflows/common-validate.yml@v0.2.0
    secrets:
      ORG_GITHUB_ACTOR: ${{ secrets.ORG_GITHUB_ACTOR }}
      ORG_GITHUB_PAT: ${{ secrets.ORG_GITHUB_PAT }}

  # 태그 감지
  version_calculation:
    name: 버전 계산
    needs: [detect_merge, set_environment]
    if: success()
    uses: komca-dev-org/common-workflows/.github/workflows/common-version-calculation.yml@v0.2.0
    with:
      source_branch: ${{ needs.set_environment.outputs.source_branch_parse }}
      target_branch: ${{ needs.set_environment.outputs.target_branch_parse }}
      merge_case: ${{ needs.set_environment.outputs.merge_case }}
    secrets:
      ORG_GITHUB_PAT: ${{ secrets.ORG_GITHUB_PAT }}

  # 빌드 및 배포
  service_publish:
    name: 배포
    needs: [detect_merge, set_environment, validate_code, version_calculation]
    if: success()
    uses: komca-dev-org/common-workflows/.github/workflows/common-service-publish.yml@v0.2.0
    with:
      service_name: ${{ github.event.repository.name }}
      environment: ${{ needs.set_environment.outputs.target_branch_parse }}
      version: ${{ needs.version_calculation.outputs.version }}
      target_tag: ${{ needs.version_calculation.outputs.target_tag }}
      source_tag: ${{ needs.version_calculation.outputs.source_tag }} # 가져올 태그 버전 입력
    secrets:
      AWS_ACCOUNT_ID: ${{ secrets.AWS_ACCOUNT_ID_STAGE }}
      AWS_REGION: ${{ secrets.AWS_REGION }}
      ORG_GITHUB_ACTOR: ${{ secrets.ORG_GITHUB_ACTOR }}
      ORG_GITHUB_PAT: ${{ secrets.ORG_GITHUB_PAT }}
      DATABASE_URL: ${{ secrets.DATABASE_URL }}
      DATABASE_USERNAME: ${{ secrets.DATABASE_USERNAME }}
      DATABASE_PASSWORD: ${{ secrets.DATABASE_PASSWORD }}

  # 태그 생성
  tag_creation:
    name: 태그 생성
    needs: [detect_merge, set_environment, version_calculation, service_publish]
    if: success()
    uses: komca-dev-org/common-workflows/.github/workflows/common-tag-creation.yml@v0.2.0
    with:
      service_name: ${{ github.event.repository.name }}
      tag_name: ${{ needs.version_calculation.outputs.target_tag }}
      pr_number: ${{ needs.detect_merge.outputs.pull_request_number }}
      merge_case: ${{ needs.set_environment.outputs.merge_case }}
    secrets:
      ORG_GITHUB_PAT: ${{ secrets.ORG_GITHUB_PAT }}

  # 배포 알림
  notify_deployment:
    name: 배포 요약
    needs: [set_environment, tag_creation, version_calculation]
    if: always()
    uses: komca-dev-org/common-workflows/.github/workflows/common-notify-deployment-compact.yml@v0.2.0
    with:
      service_name: ${{ github.event.repository.name || '⏭️ Skip' }}
      environment: ${{ needs.set_environment.outputs.target_branch_parse || '⏭️ Skip' }}
      version: ${{ needs.version_calculation.outputs.version || '⏭️ Skip' }}
      tag_name: ${{ needs.version_calculation.outputs.target_tag || '⏭️ Skip' }}
      source_version: ${{ needs.version_calculation.outputs.source_tag || '⏭️ Skip' }}

