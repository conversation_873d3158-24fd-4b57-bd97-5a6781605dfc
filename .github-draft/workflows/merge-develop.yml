# .github/workflows/merge-develop.yml
# 피쳐 브랜치가 develop으로 머지되거나 feature, fix 브랜치에서 직접 푸시될 때 태그를 생성하는 워크플로우
# 목적: 코드 검증, 시맨틱 버전 계산, 태그 생성 (빌드 및 배포는 별도 워크플로우로 진행)

name: Tag Creation [develop-merge]

# 동시성 제어
concurrency:
  group: ${{ github.workflow }}-${{ github.ref }}
  cancel-in-progress: false  # 기존 워크플로우를 취소하지 않고 대기

on:
  push:
    branches:
      - develop

permissions:
  contents: write

jobs:
  # 머지 커밋 확인
  detect_merge:
    name: 머지 커밋 감지
    uses: komca-dev-org/common-workflows/.github/workflows/common-merge-detection.yml@v0.2.0
    secrets:
      ORG_GITHUB_PAT: ${{ secrets.ORG_GITHUB_PAT }}

  # 환경 설정
  set_environment:
    name: 환경설정
    needs: [detect_merge]
    uses: komca-dev-org/common-workflows/.github/workflows/common-set-environment.yml@v0.2.0
    with:
      target_branch: ${{ needs.detect_merge.outputs.target_branch }}
      source_branch: ${{ needs.detect_merge.outputs.source_branch }}

  # 코드 검증
  validate_code:
    name: 코드 검증
    needs: [detect_merge, set_environment]
    uses: komca-dev-org/common-workflows/.github/workflows/common-validate.yml@v0.2.0
    secrets:
      ORG_GITHUB_ACTOR: ${{ secrets.ORG_GITHUB_ACTOR }}
      ORG_GITHUB_PAT: ${{ secrets.ORG_GITHUB_PAT }}

  # 버전 계산
  version_calculation:
    name: 버전 계산
    needs: [detect_merge, set_environment, validate_code]
    uses: komca-dev-org/common-workflows/.github/workflows/common-version-calculation.yml@v0.2.0
    with:
      target_branch: ${{ needs.set_environment.outputs.target_branch_parse }}
      source_branch: ${{ needs.set_environment.outputs.source_branch_parse }}
      merge_case: ${{ needs.set_environment.outputs.merge_case }}
    secrets:
      ORG_GITHUB_PAT: ${{ secrets.ORG_GITHUB_PAT }}

  # 태그 생성
  tag_creation:
    name: 태그 생성
    needs: [detect_merge, version_calculation, set_environment]
    uses: komca-dev-org/common-workflows/.github/workflows/common-tag-creation.yml@v0.2.0
    with:
      service_name: ${{ github.event.repository.name }}
      tag_name: ${{ needs.version_calculation.outputs.target_tag }}
      pr_number: ${{ needs.detect_merge.outputs.pull_request_number }}
      merge_case: ${{ needs.set_environment.outputs.merge_case }}
    secrets:
      ORG_GITHUB_PAT: ${{ secrets.ORG_GITHUB_PAT }}

  notify_deployment:
    name: 요약
    needs: [detect_merge, set_environment, tag_creation, version_calculation]
    uses: komca-dev-org/common-workflows/.github/workflows/common-notify-deployment-compact.yml@v0.2.0
    with:
      service_name: ${{ github.event.repository.name }}
      environment: ${{ needs.set_environment.outputs.target_branch_parse }}
      version: ${{ needs.version_calculation.outputs.version }}
      tag_name: ${{ needs.version_calculation.outputs.target_tag }}
      source_version: ${{ needs.version_calculation.outputs.source_tag }}