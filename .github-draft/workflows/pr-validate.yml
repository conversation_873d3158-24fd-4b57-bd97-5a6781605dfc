# .github/workflows/pr-validate.yml
# PR 검증을 위한 워크플로우 파일

name: PR Validation

on:
  pull_request:
    types: [opened, synchronize, reopened]

permissions:
  contents: read
  pull-requests: write

jobs:
  setup:
    runs-on: ubuntu-latest
    outputs:
      run_test: 'false'
    steps:
      - run: echo "run_test=false" >> $GITHUB_OUTPUT
        id: set_var

  # 환경 설정
  branch_management:
    name: 환경 설정
    uses: komca-dev-org/common-workflows/.github/workflows/common-set-environment.yml@v0.2.0
    with:
      source_branch: ${{ github.head_ref }}
      target_branch: ${{ github.base_ref }}
      is_direct_push: false # PR이 열릴때만 실행되므로 직접 PUSH가 아님

  # PR 브랜치 테스트
  call_validate:
    name: PR 브랜치 테스트
    needs: [setup]
    uses: komca-dev-org/common-workflows/.github/workflows/common-validate.yml@v0.2.0
    if: ${{ needs.setup.outputs.run_test == 'true' }}
    secrets:
      ORG_GITHUB_ACTOR: ${{ secrets.ORG_GITHUB_ACTOR }}
      ORG_GITHUB_PAT: ${{ secrets.ORG_GITHUB_PAT }}

  # 머지 시뮬레이션
  call_merge_simulation:
    name: 머지 시뮬레이션 및 테스트
    needs: [setup]
    uses: komca-dev-org/common-workflows/.github/workflows/common-merge-validate-simulation.yml@v0.2.0
    if: ${{ needs.setup.outputs.run_test == 'true' }}
    with:
      target_branch: ${{ github.base_ref }}
      pr_number: ${{ github.event.pull_request.number }}
    secrets:
      ORG_GITHUB_ACTOR: ${{ secrets.ORG_GITHUB_ACTOR }}
      ORG_GITHUB_PAT: ${{ secrets.ORG_GITHUB_PAT }}

  # JIRA 이슈 확인
  check_jira_issue:
    name: JIRA 이슈 확인
    uses: komca-dev-org/common-workflows/.github/workflows/common-jira-issue-check.yml@v0.2.0
    with:
      source_branch: ${{ github.head_ref }}
      validate_existence: true
    secrets:
      JIRA_BASE_URL: ${{ secrets.JIRA_BASE_URL }}
      JIRA_API_TOKEN: ${{ secrets.JIRA_API_TOKEN }}
      JIRA_USER_EMAIL: ${{ secrets.JIRA_USER_EMAIL }}

  # 버전 미리보기 (주요 환경 브랜치로의 PR 에서만 실행)
  call_version_preview:
    name: 버전 미리보기
    needs: [branch_management]
    if: success() && needs.branch_management.outputs.is_main_branch == 'true'
    uses: komca-dev-org/common-workflows/.github/workflows/common-version-calculation.yml@v0.2.0
    with:
      source_branch: ${{ needs.branch_management.outputs.source_branch_parse }}
      target_branch: ${{ needs.branch_management.outputs.target_branch_parse }}
      merge_case: ${{ needs.branch_management.outputs.merge_case }}
      release_branch_name: ${{ github.base_ref }} # release PR 버전 계산 시 사용
    secrets:
      ORG_GITHUB_PAT: ${{ secrets.ORG_GITHUB_PAT }}

  # PR에 결과 코멘트 추가
  call_add_pr_comment:
    name: PR 결과 코멘트
    needs: [call_validate, call_merge_simulation, call_version_preview, check_jira_issue, branch_management, setup]
    if: always()
    uses: komca-dev-org/common-workflows/.github/workflows/common-pr-comment-generator.yml@v0.2.0
    with:
      test_skip: ${{ needs.setup.outputs.run_test == 'false' }}
      validation_result: ${{ needs.call_validate.result || '⏭️ Skip' }}
      merge_simulation_result: ${{ needs.call_merge_simulation.result || '⏭️ Skip' }}
      calculated_version: ${{ needs.call_version_preview.outputs.version || '⏭️ Skip' }}
      target_tag_name: ${{ needs.call_version_preview.outputs.target_tag || '⏭️ Skip' }}
      source_tag_name: ${{ needs.call_version_preview.outputs.source_tag || '⏭️ Skip' }}
      increment_type: ${{ needs.call_version_preview.outputs.increment_type || '⏭️ Skip' }}
      merge_case: ${{ needs.branch_management.outputs.merge_case }}
      pr_number: ${{ github.event.pull_request.number || '⏭️ Skip' }}
      jira_issue: ${{ needs.check_jira_issue.outputs.jira_issue || '⏭️ Skip' }}
      jira_issue_exists: ${{ needs.check_jira_issue.outputs.issue_exists || '⏭️ Skip' }}
      source_branch_parse: ${{ needs.branch_management.outputs.source_branch_parse || '⏭️ Skip' }}
    secrets:
      ORG_GITHUB_PAT: ${{ secrets.GITHUB_TOKEN }}
      JIRA_BASE_URL: ${{ secrets.JIRA_BASE_URL }}