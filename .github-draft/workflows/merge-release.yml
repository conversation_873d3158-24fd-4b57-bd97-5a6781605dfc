# .github/workflows/merge-release.yml
# develop 브랜치가 refactor/refactor-workflows(prod)로 머지될 때 실행되는 워크플로우

name: Tag Creation [release-push]

# 동시성 제어
concurrency:
  group: ${{ github.workflow }}-${{ github.ref }}
  cancel-in-progress: false  # 기존 워크플로우를 취소하지 않고 대기

on:
  push:
    branches:
      - release/*   # 릴리즈 브랜치

permissions:
  id-token: write
  contents: write

jobs:
  # PR 머지 커밋일때만 동작하기 위한 변수 설정
  # 머지 커밋인지 확인하고, PR 소스 브랜치와 현재 브랜치를 가져온다.
  detect_merge:
    name: 머지 커밋 정보 확인
    uses: komca-dev-org/common-workflows/.github/workflows/common-merge-detection.yml@v0.2.0
    secrets:
      ORG_GITHUB_PAT: ${{ secrets.ORG_GITHUB_PAT }}

  set_environment:
    name: 브랜치 환경 파싱
    needs: [ detect_merge ]
    uses: komca-dev-org/common-workflows/.github/workflows/common-set-environment.yml@v0.2.0
    with:
      source_branch: ${{ needs.detect_merge.outputs.source_branch }}  # 직접 push하는 상황이면 빈 값이 할당됨
      target_branch: ${{ needs.detect_merge.outputs.target_branch }}
      is_direct_push: ${{ needs.detect_merge.outputs.is_merge_commit == 'false' }}

  # 코드 검증
  validate_code:
    name: 코드 검증
    uses: komca-dev-org/common-workflows/.github/workflows/common-validate.yml@v0.2.0
    secrets:
      ORG_GITHUB_ACTOR: ${{ secrets.ORG_GITHUB_ACTOR }}
      ORG_GITHUB_PAT: ${{ secrets.ORG_GITHUB_PAT }}

  # 태그 감지
  version_calculation:
    name: 버전 계산
    needs: [set_environment]
    uses: komca-dev-org/common-workflows/.github/workflows/common-version-calculation.yml@v0.2.0
    with:
      source_branch: ${{ needs.set_environment.outputs.source_branch_parse }}
      target_branch: ${{ needs.set_environment.outputs.target_branch_parse }}
      merge_case: ${{ needs.set_environment.outputs.merge_case }}
      release_branch_name: ${{ github.ref_name }}

    secrets:
      ORG_GITHUB_PAT: ${{ secrets.ORG_GITHUB_PAT }}

  # 태그 생성
  tag_creation:
    name: 태그 생성
    needs: [validate_code, version_calculation, set_environment, detect_merge]
    uses: komca-dev-org/common-workflows/.github/workflows/common-tag-creation.yml@v0.2.0
    with:
      service_name: ${{ github.event.repository.name }}
      tag_name: ${{ needs.version_calculation.outputs.target_tag || ''}}
      pr_number: ${{ needs.detect_merge.outputs.pull_request_number || ''}}
      merge_case: ${{ needs.set_environment.outputs.merge_case || ''}}
    secrets:
      ORG_GITHUB_PAT: ${{ secrets.ORG_GITHUB_PAT }}

  notify_deployment:
    name: 요약
    needs: [ set_environment, tag_creation, version_calculation, validate_code ]
    uses: komca-dev-org/common-workflows/.github/workflows/common-notify-deployment-compact.yml@v0.2.0
    with:
      service_name: ${{ github.event.repository.name }}
      environment: ${{ needs.set_environment.outputs.target_branch_parse }}
      version: ${{ needs.version_calculation.outputs.version }}
      tag_name: ${{ needs.version_calculation.outputs.target_tag }}
      source_version: ${{ needs.version_calculation.outputs.source_tag }}