# .github/workflows/publish-dispatch-develop.yml
# 이미 존재하는 태그를 개발(dev) 환경에 배포하는 워크플로우

name: Develop Publish [dispatch]

# 동시성 제어
concurrency:
  group: ${{ github.workflow }}-${{ github.ref }}
  cancel-in-progress: false  # 기존 워크플로우를 취소하지 않고 대기

on:
  workflow_dispatch:
    inputs:
      tag_name:
        description: '배포할 태그 이름 (예: dev-v1.2.3)'
        required: true
        type: string

permissions:
  id-token: write
  contents: write

jobs:
  validate_tag:
    name: 태그 유효성 검사 (존재 여부 확인)
    uses: komca-dev-org/common-workflows/.github/workflows/common-tag-validation.yml@v0.2.0
    with:
      tag_name: ${{ github.event.inputs.tag_name }}
      tag_prefix: "dev-v"
    secrets:
      ORG_GITHUB_PAT: ${{ secrets.ORG_GITHUB_PAT }}

  # 빌드 및 배포
  service_publish:
    name: 태그 배포 (dev 환경)
    needs: [validate_tag]
    if: needs.validate_tag.outputs.is_valid == 'true'
    uses: komca-dev-org/common-workflows/.github/workflows/common-service-publish.yml@v0.2.0
    with:
      service_name: ${{ github.event.repository.name }}
      environment: "dev"
      target_tag: ${{ github.event.inputs.tag_name }}
      version: ${{ needs.validate_tag.outputs.version }}
      source_tag: ${{ github.event.inputs.tag_name }}
    secrets:
      AWS_ACCOUNT_ID: ${{ secrets.AWS_ACCOUNT_ID_DEV }}
      AWS_REGION: ${{ secrets.AWS_REGION }}
      ORG_GITHUB_ACTOR: ${{ secrets.ORG_GITHUB_ACTOR }}
      ORG_GITHUB_PAT: ${{ secrets.ORG_GITHUB_PAT }}
      DATABASE_URL: ${{ secrets.DATABASE_URL }}
      DATABASE_USERNAME: ${{ secrets.DATABASE_USERNAME }}
      DATABASE_PASSWORD: ${{ secrets.DATABASE_PASSWORD }}

  # 배포 알림
  notify_deployment:
    name: 배포 요약
    needs: [validate_tag, service_publish]
    if: needs.validate_tag.outputs.is_valid == 'true'
    uses: komca-dev-org/common-workflows/.github/workflows/common-notify-deployment-compact.yml@v0.2.0
    with:
      service_name: ${{ github.event.repository.name }}
      environment: "dev"
      version: ${{ needs.validate_tag.outputs.version }}
      tag_name: ${{ github.event.inputs.tag_name }}