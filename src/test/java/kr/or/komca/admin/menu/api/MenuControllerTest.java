package kr.or.komca.admin.menu.api;


import com.fasterxml.jackson.databind.ObjectMapper;
import kr.or.komca.admin.menu.dto.command.request.UpdateMenuRequest;
import kr.or.komca.admin.menu.dto.query.condition.MenuSearchCondition;
import kr.or.komca.admin.menu.dto.query.response.MenuTree;
import kr.or.komca.admin.menu.service.command.MenuCommandService;
import kr.or.komca.admin.menu.service.query.MenuQueryService;
import kr.or.komca.common.utils.core.dto.response.ListResponse;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.test.context.bean.override.mockito.MockitoBean;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;
import org.springframework.web.context.WebApplicationContext;

import java.util.List;


import static org.mockito.BDDMockito.given;
import static org.mockito.Mockito.*;
import static org.springframework.security.test.web.servlet.request.SecurityMockMvcRequestPostProcessors.csrf;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;


@WebMvcTest(MenuController.class)
class MenuControllerTest {

	@Autowired
	private WebApplicationContext context;

	@Autowired
	private ObjectMapper objectMapper;

	@MockitoBean
	private MenuCommandService menuCommandService;

	@MockitoBean
	private MenuQueryService menuQueryService;

	private MockMvc mockMvc;

	@BeforeEach
	void setUp() {
		mockMvc = MockMvcBuilders
				.webAppContextSetup(context)
				.build();
	}

//	@Nested
//	@DisplayName("샘플 조회 API 테스트")
//	class GetMenuTree {
//		@Test
//		@DisplayName("성공")
//		void getList_Success() throws Exception {
//			MenuTree menuTree = MenuTree.builder()
//					.menuNm("Test Menu")
//					.menuCd("00001")
//					.menuRout("Test Rout")
//					.level(0)
//					.parCd(null)
//					.useYn("Y")
//					.children(
//							List.of(
//									MenuTree.builder()
//											.menuNm("Test Menu 2")
//											.menuCd("00002")
//											.menuRout("Test Rout 2")
//											.level(1)
//											.parCd("00001")
//											.useYn("Y")
//											.build(),
//									MenuTree.builder()
//											.menuNm("Test Menu 3")
//											.menuCd("00003")
//											.menuRout("Test Rout 3")
//											.level(1)
//											.parCd("00001")
//											.useYn("Y")
//											.build()
//							)
//					)
//					.build();
//
//
//			ListResponse<MenuTree> listResponse = ListResponse.<MenuTree>builder()
//					.contents(List.of(menuTree))
//					.totalElements(1L)
//					.build();
//
//			given(menuQueryService.getMenuTree(any(MenuSearchCondition.class)))
//					.willReturn(listResponse);
//
//			mockMvc.perform(get("/api/v1/menu/tree")
//							.param("useYn", "Y")
//							.param("menuNm", "Test"))
//					.andExpect(status().isOk())
//					.andExpect(jsonPath("data.contents.length()").value(1))
//					.andExpect(jsonPath("$.data.contents[0].menuNm").value("Test Menu 2"))
//					.andExpect(jsonPath("$.data.contents[0].menuCd").value("00001"));
//
//		}
//	}
//
//	@Nested
//	@DisplayName("샘플 수정 API 테스트")
//	class UpdateMenu {
//		@Test
//		@DisplayName("성공")
//		void success() throws Exception {
//			UpdateMenuRequest request = UpdateMenuRequest.builder()
//					.menuNm("Test Menu Update")
//					.menuRout("Test Rout")
//					.useYn("Y")
//					.sortOrd(1)
//					.build();
//
//
//			kr.or.komca.admin.menu.dto.command.response.UpdateMenu expectedResult = kr.or.komca.admin.menu.dto.command.response.UpdateMenu.builder()
//					.menuNm("Test Menu Update")
//					.menuCd("00001")
//					.menuRout("Test Rout")
//					.level(0)
//					.parCd(null)
//					.useYn("Y")
//					.sortOrd(1)
//					.build();
//
//			given(menuCommandService.updateMenu(anyString(), anyString(), any(UpdateMenuRequest.class)))
//					.willReturn(expectedResult);
//
//			mockMvc.perform(post("/api/v1/menu/00001")
//							.with(csrf())
//							.contentType("application/json")
//							.content(objectMapper.writeValueAsString(request)))
//					.andExpect(status().isOk())
//					.andExpect(jsonPath("$.data.menuNm").value("Test Menu Update"))
//					.andExpect(jsonPath("$.data.menuCd").value("00001"));
//		}
//	}
}