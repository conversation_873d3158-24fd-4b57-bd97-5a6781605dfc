package kr.or.komca.admin.sample.api;

import com.fasterxml.jackson.databind.ObjectMapper;
import kr.or.komca.admin.sample.dto.command.request.SampleCreateRequest;
import kr.or.komca.admin.sample.dto.command.request.SampleDeleteRequest;
import kr.or.komca.admin.sample.dto.command.request.SampleUpdateRequest;
import kr.or.komca.admin.sample.dto.command.response.SampleCreate;
import kr.or.komca.admin.sample.dto.command.response.SampleUpdate;
import kr.or.komca.admin.sample.dto.query.condition.SampleDetailSearch;
import kr.or.komca.admin.sample.dto.query.condition.SampleListSearch;
import kr.or.komca.admin.sample.dto.query.response.SampleDetail;
import kr.or.komca.admin.sample.dto.query.response.SampleList;
import kr.or.komca.admin.sample.service.command.SampleCommandService;
import kr.or.komca.admin.sample.service.query.SampleQueryService;
import kr.or.komca.common.auth.domain.auth.KomcaAuthUser;
import kr.or.komca.common.auth.domain.common.UserContext;
import kr.or.komca.common.auth.support.utils.context.UserContextHolder;
import kr.or.komca.common.utils.core.dto.response.PageListResponse;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.mockito.MockedStatic;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.http.MediaType;
import org.springframework.security.test.context.support.WithMockUser;
import org.springframework.test.context.bean.override.mockito.MockitoBean;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;
import org.springframework.web.context.WebApplicationContext;

import java.time.LocalDateTime;
import java.util.List;

import static org.mockito.BDDMockito.given;
import static org.mockito.Mockito.*;
import static org.springframework.security.test.web.servlet.request.SecurityMockMvcRequestPostProcessors.csrf;
import static org.springframework.security.test.web.servlet.setup.SecurityMockMvcConfigurers.springSecurity;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

@WebMvcTest(SampleController.class)
class SampleControllerTest {

    @Autowired
    private WebApplicationContext context;

    @Autowired
    private ObjectMapper objectMapper;

    @MockitoBean
    private SampleCommandService sampleCommandService;

    @MockitoBean
    private SampleQueryService sampleQueryService;

    private MockMvc mockMvc;
    private UserContext mockUserContext;

    @BeforeEach
    void setUp() {
        mockMvc = MockMvcBuilders
                .webAppContextSetup(context)
                .apply(springSecurity())
                .build();

        KomcaAuthUser komcaAuthUser = mock(KomcaAuthUser.class);
        when(komcaAuthUser.getUserNo()).thenReturn(1L);
        when(komcaAuthUser.getUsername()).thenReturn("testUser");
        when(komcaAuthUser.getEmail()).thenReturn("<EMAIL>");
        mockUserContext = UserContext.from(komcaAuthUser);
    }

    @Nested
    @DisplayName("샘플 생성 API 테스트")
    class CreateSample {
        @Test
        @DisplayName("성공")
        @WithMockUser(username = "testUser", roles = {"ADMIN"})
        void success() throws Exception {
            SampleCreateRequest request = SampleCreateRequest.builder()
                    .title("Test Title")
                    .content("Test Content")
                    .quantity(10)
                    .phoneNumber("010-1234-5678")
                    .email("<EMAIL>")
                    .build();

            SampleCreate response = SampleCreate.builder()
                    .request(request)
                    .userNo(1L)
                    .build();

            try (MockedStatic<UserContextHolder> mockedStatic = mockStatic(UserContextHolder.class)) {
                mockedStatic.when(UserContextHolder::getContext).thenReturn(mockUserContext);

                given(sampleCommandService.createSample(any(SampleCreateRequest.class), eq(response.getUserNo())))
                        .willReturn(response);

                mockMvc.perform(post("/api/v1/sample")
                                .with(csrf())
                                .contentType(MediaType.APPLICATION_JSON)
                                .content(objectMapper.writeValueAsString(request)))
                        .andExpect(status().isCreated())
                        .andExpect(jsonPath("$.data.userNo").value(mockUserContext.getUserNo()))
                        .andExpect(jsonPath("$.data.request.title").value("Test Title"));
            }
        }

        @Test
        @DisplayName("권한 없음")
        @WithMockUser(roles = "GUEST")
        void unauthorized() throws Exception {
            SampleCreateRequest request = SampleCreateRequest.builder()
                    .title("Test Title")
                    .content("Test Content")
                    .quantity(10)
                    .phoneNumber("010-1234-5678")
                    .email("<EMAIL>")
                    .build();

            mockMvc.perform(post("/api/v1/sample")
                            .with(csrf())
                            .contentType(MediaType.APPLICATION_JSON)
                            .content(objectMapper.writeValueAsString(request)))
                    .andExpect(status().isUnauthorized());
        }

        @Test
        @DisplayName("CSRF 토큰 누락")
        @WithMockUser(roles = "ADMIN")
        void missingCsrf() throws Exception {
            SampleCreateRequest request = SampleCreateRequest.builder()
                    .title("Test Title")
                    .content("Test Content")
                    .quantity(10)
                    .phoneNumber("010-1234-5678")
                    .email("<EMAIL>")
                    .build();

            mockMvc.perform(post("/api/v1/sample")
                            .contentType(MediaType.APPLICATION_JSON)
                            .content(objectMapper.writeValueAsString(request)))
                    .andExpect(status().isForbidden());
        }

        @Test
        @DisplayName("유효성 검사 실패")
        @WithMockUser(roles = "ADMIN")
        void validationFail() throws Exception {
            SampleCreateRequest request = SampleCreateRequest.builder()
                    .title("")
                    .content("Test Content")
                    .quantity(-1)
                    .phoneNumber("invalid-phone")
                    .email("invalid-email")
                    .build();

            mockMvc.perform(post("/api/v1/sample")
                            .with(csrf())
                            .contentType(MediaType.APPLICATION_JSON)
                            .content(objectMapper.writeValueAsString(request)))
                    .andExpect(status().isBadRequest());
        }
    }

    @Nested
    @DisplayName("샘플 조회 API 테스트")
    class GetSample {
        @Test
        @DisplayName("목록 조회 성공")
        @WithMockUser(roles = "USER")
        void getList_Success() throws Exception {
            SampleList sampleList = SampleList.builder()
                    .sampleNo(1L)
                    .title("Test Title")
                    .quantity(10)
                    .phoneNumber("010-1234-5678")
                    .email("<EMAIL>")
                    .createdAt(LocalDateTime.now())
                    .updatedAt(LocalDateTime.now())
                    .build();

            PageListResponse<SampleList> PageListResponse = kr.or.komca.common.utils.core.dto.response.PageListResponse.<SampleList>builder()
                    .contents(List.of(sampleList))
                    .totalElements(1L)
                    .totalPages(1)
                    .page(1)
                    .pageSize(10)
                    .build();

            given(sampleQueryService.getSampleList(any(SampleListSearch.class)))
                    .willReturn(PageListResponse);

            mockMvc.perform(get("/api/v1/sample")
                            .param("page", "1")
                            .param("pageSize", "10"))
                    .andExpect(status().isOk())
                    .andExpect(jsonPath("$.data.contents[0].sampleNo").value(1L))
                    .andExpect(jsonPath("$.data.totalElements").value(1L));
        }

        @Test
        @DisplayName("상세 조회 성공")
        @WithMockUser(roles = "USER")
        void getDetail_Success() throws Exception {
            SampleDetail detail = SampleDetail.builder()
                    .sampleNo(1L)
                    .userNo(1L)
                    .title("Test Title")
                    .content("Test Content")
                    .quantity(10)
                    .phoneNumber("010-1234-5678")
                    .email("<EMAIL>")
                    .createdAt(LocalDateTime.now())
                    .updatedAt(LocalDateTime.now())
                    .build();

            given(sampleQueryService.getSampleDetail(any(SampleDetailSearch.class)))
                    .willReturn(detail);

            mockMvc.perform(get("/api/v1/sample/detail")
                            .param("sampleNo", "1"))
                    .andExpect(status().isOk())
                    .andExpect(jsonPath("$.data.sampleNo").value(1L))
                    .andExpect(jsonPath("$.data.title").value("Test Title"));
        }
    }

    @Nested
    @DisplayName("샘플 수정 API 테스트")
    class UpdateSample {
        @Test
        @DisplayName("성공")
        @WithMockUser(roles = "USER")
        void success() throws Exception {
            SampleUpdateRequest request = SampleUpdateRequest.builder()
                    .sampleNo(1L)
                    .title("Updated Title")
                    .content("Updated Content")
                    .quantity(20)
                    .phoneNumber("010-1234-5678")
                    .email("<EMAIL>")
                    .build();

            SampleUpdate response = SampleUpdate.builder()
                    .request(request)
                    .updateCount(1)
                    .build();

            given(sampleCommandService.updateSample(any(SampleUpdateRequest.class)))
                    .willReturn(response);

            mockMvc.perform(post("/api/v1/sample/update")
                            .with(csrf())
                            .contentType(MediaType.APPLICATION_JSON)
                            .content(objectMapper.writeValueAsString(request)))
                    .andExpect(status().isOk())
                    .andExpect(jsonPath("$.data.updateCount").value(1))
                    .andExpect(jsonPath("$.data.request.title").value("Updated Title"));
        }
    }

    @Nested
    @DisplayName("샘플 삭제 API 테스트")
    class DeleteSample {
        @Test
        @DisplayName("Path Variable로 삭제 성공")
        @WithMockUser(roles = "USER")
        void deleteByPathVariable_Success() throws Exception {
            Long sampleNo = 1L;
            doNothing().when(sampleCommandService).deleteSample(any(SampleDeleteRequest.class));

            mockMvc.perform(post("/api/v1/sample/delete/{sampleNo}", sampleNo)
                            .with(csrf()))
                    .andExpect(status().isOk());

            verify(sampleCommandService, times(1)).deleteSample(any(SampleDeleteRequest.class));
        }

        @Test
        @DisplayName("Request Body로 삭제 성공")
        @WithMockUser(roles = "USER")
        void deleteByRequestBody_Success() throws Exception {
            SampleDeleteRequest request = SampleDeleteRequest.builder()
                    .sampleNo(1L)
                    .deleteReason("Test deletion")
                    .build();

            doNothing().when(sampleCommandService).deleteSample(any(SampleDeleteRequest.class));

            mockMvc.perform(post("/api/v1/sample/delete")
                            .with(csrf())
                            .contentType(MediaType.APPLICATION_JSON)
                            .content(objectMapper.writeValueAsString(request)))
                    .andExpect(status().isOk());

            verify(sampleCommandService, times(1)).deleteSample(any(SampleDeleteRequest.class));
        }
    }
}