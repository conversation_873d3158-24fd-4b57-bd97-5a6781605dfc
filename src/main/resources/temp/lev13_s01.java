
package komca.fi.lev;
import java.sql.*;
import java.util.*;
import WIZ.FR.COM.*;
import WIZ.FR.DAO.*;
import WIZ.FR.MSG.*;
import WIZ.FR.UTIL.*;
public class lev13_s01
{
    public lev13_s01()
    {
    }
    //##**$$bscon_sms_select
    /*
    */
    public DOBJ CTLbscon_sms_select(DOBJ dobj)
    {
        WizUtil wutil = new WizUtil(dobj);
        Connection Conn = null;
        try
        {
            Connector connection = new Connector();
            Conn = connection.getConnection("PFDB",dobj);
        }
        catch(Exception e)
        {
            dobj.setRetmsg(e,"DataBase Connection Error");
            e.printStackTrace();
            return dobj;
        }
        try
        {
            dobj  = CALLbscon_sms_select_SEL1(Conn, dobj);           //  �ŷ�ó�˻�
        }
        catch(Exception e)
        {
            e.printStackTrace();
            try
            {
                dobj.setRtncode(-1);
                dobj.setRetmsg(e.getMessage());
            }
            catch(Exception re)
            {
                re.printStackTrace();
            }
        }
        finally
        {
            try
            {
                Conn.close();
            }
            catch(SQLException e)
            {
                dobj.setRtncode(-1);
                dobj.setRetmsg(e,"DataBase Close Error");
                e.printStackTrace();
            }
        }
        return dobj;
    }
    public DOBJ CTLbscon_sms_select( DOBJ dobj, Object Connx) throws Exception
    {
        WizUtil wutil = new WizUtil(dobj);
        Connection Conn = (Connection)Connx;
        dobj  = CALLbscon_sms_select_SEL1(Conn, dobj);           //  �ŷ�ó�˻�
        return dobj;
    }
    // �ŷ�ó�˻�
    // �ŷ�ó�˻�
    public DOBJ CALLbscon_sms_select_SEL1(Connection Conn, DOBJ dobj) throws Exception
    {
        WizUtil wutil = new WizUtil(dobj);
        dobj.setRtnname("SEL1");
        QExecutor qexe = new QExecutor(dobj);
        dobj.setRtnname("SEL1");
        VOBJ dvobj = dobj.getRetObject("S");           //����� ȭ�鿡�� �߻��� Object�Դϴ�.
        SQLObject  sobj = SQLbscon_sms_select_SEL1(dobj, dvobj);
        VOBJ rvobj = qexe.executeQuery(Conn, sobj);
        rvobj.setName("SEL1");
        dobj.setRetObject(rvobj);
        return dobj;
    }
    private SQLObject SQLbscon_sms_select_SEL1(DOBJ dobj, VOBJ dvobj) throws Exception
    {
        WizUtil wutil = new WizUtil(dobj);
        String   REPPRES_NM = dobj.getRetObject("S").getRecord().get("REPPRES_NM");   //��ǥ�� ��
        String   BIOWN_GBN = dobj.getRetObject("S").getRecord().get("BIOWN_GBN");   //����� ����
        String   GBN = dobj.getRetObject("S").getRecord().get("GBN");   //����
        String   BSCONHAN_NM = dobj.getRetObject("S").getRecord().get("BSCONHAN_NM");   //�ŷ�ó�ѱ� ��
        String   MSTR_MDM_CD = dobj.getRetObject("S").getRecord().get("MSTR_MDM_CD");   //�� ��ü �ڵ�
        String   INS_NUM = dobj.getRetObject("S").getRecord().get("INS_NUM");   //��� ��ȣ
        String   CONTRCCLSN_YN = dobj.getRetObject("S").getRecord().get("CONTRCCLSN_YN");   //���ü�� ����
        String   BSCON_CD = dobj.getRetObject("S").getRecord().get("BSCON_CD");   //�ŷ�ó �ڵ�
        SQLObject sobj = new SQLObject();
        String    query="";
        query +=" SELECT BSCON_CD, BSCONHAN_NM, REPPRES_NM, CONTRCCLSN_YN, BIOWN_GBN, INS_NUM, DECODE(BIOWN_GBN,'1',SUBSTR(DECODE(BIOWN_GBN,'1',RPAD(INS_NUM,13,'0'),INS_NUM),1,7)||'******',INS_NUM) INS_NUM2, BANK_CD, ACCN_NUM, USE_YN, (SELECT DISTINCT MAX(LARGECLASS_CD_NM) AS LARGECLASS_CD_NM  ";
        query +=" FROM FIDU.TENV_MDMCD  ";
        query +=" WHERE LARGECLASS_CD = MSTR_MDM_CD) AS MSTR_MDM_CD_NM , TRANSLATE(PHON_NUM,'**********'||PHON_NUM,'**********') AS PHON_NUM ,TRANSLATE(PHON_NUM2,'**********'||PHON_NUM2,'**********') AS PHON_NUM2 ,TRANSLATE(UDTKPRES_CTTAD,'**********'||UDTKPRES_CTTAD,'**********') AS UDTKPRES_CTTAD  ";
        query +=" FROM FIDU.TLEV_BSCON  ";
        query +=" WHERE 1 = 1  ";
        if( !BSCON_CD.equals("") )
        {
            query +=" AND BSCON_CD LIKE :BSCON_CD || '%'  ";
        }
        if( !BSCONHAN_NM.equals("") )
        {
            query +=" AND BSCONHAN_NM LIKE '%' || :BSCONHAN_NM || '%'  ";
        }
        if( !REPPRES_NM.equals("") )
        {
            query +=" AND REPPRES_NM LIKE '%' || :REPPRES_NM || '%'  ";
        }
        if( !CONTRCCLSN_YN.equals("") )
        {
            query +=" AND CONTRCCLSN_YN = :CONTRCCLSN_YN  ";
        }
        if( !BIOWN_GBN.equals("") )
        {
            query +=" AND BIOWN_GBN = :BIOWN_GBN  ";
        }
        if( !MSTR_MDM_CD.equals("") )
        {
            query +=" AND MSTR_MDM_CD = :MSTR_MDM_CD  ";
        }
        if( !INS_NUM.equals("") )
        {
            query +=" AND INS_NUM = :INS_NUM  ";
        }
        if( !GBN.equals("") )
        {
            query +=" AND (TRANSLATE(PHON_NUM,'**********'||PHON_NUM,'**********') IS NOT NULL  ";
            query +=" OR TRANSLATE(PHON_NUM2,'**********'||PHON_NUM2,'**********') IS NOT NULL  ";
            query +=" OR TRANSLATE(UDTKPRES_CTTAD,'**********'||UDTKPRES_CTTAD,'**********') IS NOT NULL )  ";
            query +=" AND :GBN=:GBN  ";
        }
        sobj.setSql(query);
        if(!REPPRES_NM.equals(""))
        {
            sobj.setString("REPPRES_NM", REPPRES_NM);               //��ǥ�� ��
        }
        if(!BIOWN_GBN.equals(""))
        {
            sobj.setString("BIOWN_GBN", BIOWN_GBN);               //����� ����
        }
        if(!GBN.equals(""))
        {
            sobj.setString("GBN", GBN);               //����
        }
        if(!BSCONHAN_NM.equals(""))
        {
            sobj.setString("BSCONHAN_NM", BSCONHAN_NM);               //�ŷ�ó�ѱ� ��
        }
        if(!MSTR_MDM_CD.equals(""))
        {
            sobj.setString("MSTR_MDM_CD", MSTR_MDM_CD);               //�� ��ü �ڵ�
        }
        if(!INS_NUM.equals(""))
        {
            sobj.setString("INS_NUM", INS_NUM);               //��� ��ȣ
        }
        if(!CONTRCCLSN_YN.equals(""))
        {
            sobj.setString("CONTRCCLSN_YN", CONTRCCLSN_YN);               //���ü�� ����
        }
        if(!BSCON_CD.equals(""))
        {
            sobj.setString("BSCON_CD", BSCON_CD);               //�ŷ�ó �ڵ�
        }
        return sobj;
    }
    //##**$$bscon_sms_select
    //##**$$mem_search
    /*
    */
    public DOBJ CTLmem_search(DOBJ dobj)
    {
        WizUtil wutil = new WizUtil(dobj);
        Connection Conn = null;
        try
        {
            Connector connection = new Connector();
            Conn = connection.getConnection("PFDB",dobj);
        }
        catch(Exception e)
        {
            dobj.setRetmsg(e,"DataBase Connection Error");
            e.printStackTrace();
            return dobj;
        }
        try
        {
            if( dobj.getRetObject("S").getRecord().get("KEYWORK").equals("1"))
            {
                dobj  = CALLmem_search_SEL6(Conn, dobj);           //  ȸ��[��ȭKEY]�˻�
                dobj  = CALLmem_search_MRG9( dobj);        //  ȭ����[ȸ������]
            }
            else if( dobj.getRetObject("S").getRecord().get("KEYWORK").equals("2"))
            {
                dobj  = CALLmem_search_SEL8(Conn, dobj);           //  ȸ��[�ֹι�ȣKEY]�˻�
                dobj  = CALLmem_search_MRG9( dobj);        //  ȭ����[ȸ������]
            }
            else if(!dobj.getRetObject("S").getRecord().get("HANMB_NM").equals(""))
            {
                dobj  = CALLmem_search_SEL10(Conn, dobj);           //  �̸�
                dobj  = CALLmem_search_MRG9( dobj);        //  ȭ����[ȸ������]
            }
            else if( 1 == 1)
            {
                dobj  = CALLmem_search_SEL7(Conn, dobj);           //  ȸ�������˻�
                dobj  = CALLmem_search_MRG9( dobj);        //  ȭ����[ȸ������]
            }
        }
        catch(Exception e)
        {
            e.printStackTrace();
            try
            {
                dobj.setRtncode(-1);
                dobj.setRetmsg(e.getMessage());
            }
            catch(Exception re)
            {
                re.printStackTrace();
            }
        }
        finally
        {
            try
            {
                Conn.close();
            }
            catch(SQLException e)
            {
                dobj.setRtncode(-1);
                dobj.setRetmsg(e,"DataBase Close Error");
                e.printStackTrace();
            }
        }
        return dobj;
    }
    public DOBJ CTLmem_search( DOBJ dobj, Object Connx) throws Exception
    {
        WizUtil wutil = new WizUtil(dobj);
        Connection Conn = (Connection)Connx;
        if( dobj.getRetObject("S").getRecord().get("KEYWORK").equals("1"))
        {
            dobj  = CALLmem_search_SEL6(Conn, dobj);           //  ȸ��[��ȭKEY]�˻�
            dobj  = CALLmem_search_MRG9( dobj);        //  ȭ����[ȸ������]
        }
        else if( dobj.getRetObject("S").getRecord().get("KEYWORK").equals("2"))
        {
            dobj  = CALLmem_search_SEL8(Conn, dobj);           //  ȸ��[�ֹι�ȣKEY]�˻�
            dobj  = CALLmem_search_MRG9( dobj);        //  ȭ����[ȸ������]
        }
        else if(!dobj.getRetObject("S").getRecord().get("HANMB_NM").equals(""))
        {
            dobj  = CALLmem_search_SEL10(Conn, dobj);           //  �̸�
            dobj  = CALLmem_search_MRG9( dobj);        //  ȭ����[ȸ������]
        }
        else if( 1 == 1)
        {
            dobj  = CALLmem_search_SEL7(Conn, dobj);           //  ȸ�������˻�
            dobj  = CALLmem_search_MRG9( dobj);        //  ȭ����[ȸ������]
        }
        return dobj;
    }
    // ȸ��[��ȭKEY]�˻�
    public DOBJ CALLmem_search_SEL6(Connection Conn, DOBJ dobj) throws Exception
    {
        WizUtil wutil = new WizUtil(dobj);
        dobj.setRtnname("SEL6");
        QExecutor qexe = new QExecutor(dobj);
        dobj.setRtnname("SEL6");
        VOBJ dvobj = dobj.getRetObject("S");           //����� ȭ�鿡�� �߻��� Object�Դϴ�.
        SQLObject  sobj = SQLmem_search_SEL6(dobj, dvobj);
        VOBJ rvobj = qexe.executeQuery(Conn, sobj);
        rvobj.setName("SEL6");
        dobj.setRetObject(rvobj);
        return dobj;
    }
    private SQLObject SQLmem_search_SEL6(DOBJ dobj, VOBJ dvobj) throws Exception
    {
        WizUtil wutil = new WizUtil(dobj);
        String   MB_CD = dobj.getRetObject("S").getRecord().get("MB_CD");   //ȸ�� �ڵ�
        String   MB_GBN = dobj.getRetObject("S").getRecord().get("MB_GBN");   //ȸ������
        String   TO = dobj.getRetObject("S").getRecord().get("TO");   //��������
        String   TODATE = dobj.getRetObject("S").getRecord().get("TODATE");   //��������
        String   FROMDATE = dobj.getRetObject("S").getRecord().get("FROMDATE");   //��������
        String   TRUST_TERMS_YN = dobj.getRetObject("S").getRecord().get("TRUST_TERMS_YN");   //��Ź�����������
        String   HANMB_NM = dobj.getRetObject("S").getRecord().get("HANMB_NM");   //ȸ�� �ŷ�ó �ڵ�
        String   SECT_CD = dobj.getRetObject("S").getRecord().get("SECT_CD");   //�ι� �ڵ�
        String   VARCHAR_TMP1 = dobj.getRetObject("S").getRecord().get("VARCHAR_TMP1");   //VARCHAR_TMP1
        String   MB_GBN2 = dobj.getRetObject("S").getRecord().get("MB_GBN2");   //ȸ�� ����2
        String   FROM = dobj.getRetObject("S").getRecord().get("FROM");   //��������
        SQLObject sobj = new SQLObject();
        String    query="";
        query +=" SELECT A.MB_CD, A.HANMB_NM, A.ENGSTMB_NM, FIDU.GET_MB_START_DAY( A.MB_CD, 'E') as ENTRN_DAY, FIDU.GET_MB_START_DAY( A.MB_CD, 'T') as TRUST_DAY, FIDU.GET_MB_START_DAY( A.MB_CD, 'T') as TRUSTTRM_START_DAY, TRANSLATE(A.PHON_NUM,'**********'||A.PHON_NUM,'**********') AS PHON_NUM, TRANSLATE(A.CP_NUM,'**********'||A.CP_NUM,'**********') AS CP_NUM, A.INS_NUM, A.QAFCSTOP_START_DAY, A.QAFCSTOP_END_DAY, FIDU.GET_MB_END_DAY( A.MB_CD, 'T') as TRUSTTRM_END_DAY, B.ADDR || B.ADDR_DETED AS ADDR , B.ADDR AS ADDR1, B.ADDR_DETED AS ADDR_DETED, substr( B.POST_NUM , 1,3) || ' - '|| substr( B.POST_NUM, 4 , 3) as POST_NUM, C.SN, A.MB_GBN1, A.MB_GBN2, A.SECT_CD, A.SMS_RECV_YN, A.IPI_NAME_NR , A.SUPPBANK_CD , A.SUPPACCN_NUM , FIDU.GET_HONG_NM(A.MB_CD) AS HONG_NAME , B.POSTSNDBK_YN , FIDU.GET_TRUST_YN(A.MB_CD) TRUST_YN  ";
        query +=" FROM FIDU.TMEM_MB A, (SELECT ADDR,ADDR_DETED,POST_NUM,MB_CD, POSTSNDBK_YN  ";
        query +=" FROM FIDU.TMEM_ADBK  ";
        query +=" WHERE ADDR_GBN = '2')B, (SELECT MB_CD, SN  ";
        query +=" FROM FIDU.TMEM_SN  ";
        query +=" WHERE SN_MNG_NUM = '03')C  ";
        query +=" WHERE A.MB_CD = B.MB_CD(+)  ";
        query +=" AND A.MB_CD = C.MB_CD(+)  ";
        query +=" AND NVL(LENGTH( A.DEL_DATE),0) = 0  ";
        query +=" AND NVL(LENGTH( A.DELPRES_ID),0) = 0  ";
        if( !MB_GBN.equals("") )
        {
            query +=" AND A.MB_GBN1 LIKE :MB_GBN  ";
        }
        if( !TRUST_TERMS_YN.equals("") )
        {
            query +=" AND A.TRUST_TERMS_YN LIKE :TRUST_TERMS_YN  ";
        }
        if( !MB_CD.equals("") )
        {
            query +=" AND A.MB_CD LIKE '%'||:MB_CD||'%'  ";
        }
        if( !HANMB_NM.equals("") )
        {
            query +=" AND ( A.HANMB_NM LIKE '%'||:HANMB_NM||'%'  ";
            query +=" OR C.SN LIKE '%'||:HANMB_NM||'%' )  ";
        }
        if( !SECT_CD.equals("") )
        {
            query +=" AND A.SECT_CD LIKE :SECT_CD  ";
        }
        if( !MB_GBN2.equals("") )
        {
            query +=" AND NVL(A.MB_GBN2,'%') LIKE :MB_GBN2  ";
        }
        query +=" AND REPLACE( A.PHON_NUM , '-', '') LIKE :VARCHAR_TMP1 || '%'  ";
        if( !FROM.equals("")  && !TO.equals("") )
        {
            query +=" AND A.MB_CD IN (  ";
            query +=" SELECT MB_CD  ";
            query +=" FROM FIDU.TMEM_SCSBRE  ";
            query +=" WHERE ENTRN_DAY <= :TO  ";
            query +=" AND SCS_DAY >= :FROM )  ";
        }
        if( !FROMDATE.equals("")  && !TODATE.equals("") )
        {
            query +=" AND A.MB_CD IN (  ";
            query +=" SELECT MB_CD  ";
            query +=" FROM FIDU.TMEM_TRUSTTRM  ";
            query +=" WHERE TRUSTTRM_START_DAY <= :TODATE  ";
            query +=" AND TRUSTTRM_END_DAY >= :FROMDATE)  ";
        }
        query +=" ORDER BY A.MB_CD  ";
        sobj.setSql(query);
        if(!MB_CD.equals(""))
        {
            sobj.setString("MB_CD", MB_CD);               //ȸ�� �ڵ�
        }
        if(!MB_GBN.equals(""))
        {
            sobj.setString("MB_GBN", MB_GBN);               //ȸ������
        }
        if(!TO.equals(""))
        {
            sobj.setString("TO", TO);               //��������
        }
        if(!TODATE.equals(""))
        {
            sobj.setString("TODATE", TODATE);               //��������
        }
        if(!FROMDATE.equals(""))
        {
            sobj.setString("FROMDATE", FROMDATE);               //��������
        }
        if(!TRUST_TERMS_YN.equals(""))
        {
            sobj.setString("TRUST_TERMS_YN", TRUST_TERMS_YN);               //��Ź�����������
        }
        if(!HANMB_NM.equals(""))
        {
            sobj.setString("HANMB_NM", HANMB_NM);               //ȸ�� �ŷ�ó �ڵ�
        }
        if(!SECT_CD.equals(""))
        {
            sobj.setString("SECT_CD", SECT_CD);               //�ι� �ڵ�
        }
        sobj.setString("VARCHAR_TMP1", VARCHAR_TMP1);               //VARCHAR_TMP1
        if(!MB_GBN2.equals(""))
        {
            sobj.setString("MB_GBN2", MB_GBN2);               //ȸ�� ����2
        }
        if(!FROM.equals(""))
        {
            sobj.setString("FROM", FROM);               //��������
        }
        return sobj;
    }
    // ȭ����[ȸ������]
    public DOBJ CALLmem_search_MRG9(DOBJ dobj) throws Exception
    {
        dobj.setRtnname("MRG9");
        WizUtil wutil = new WizUtil(dobj);
        VOBJ       rvobj= null;
        rvobj = wutil.getMergeObject(dobj,"SEL10, SEL6, SEL7, SEL8","");
        rvobj.setName("MRG9") ;
        rvobj.setRetcode(1);
        rvobj.Println("MRG9");
        dobj.setRetObject(rvobj);
        String message ="��ȸ�Ǿ����ϴ�";
        dobj.setRetmsg(message);
        return dobj;
    }
    // ȸ��[�ֹι�ȣKEY]�˻�
    public DOBJ CALLmem_search_SEL8(Connection Conn, DOBJ dobj) throws Exception
    {
        WizUtil wutil = new WizUtil(dobj);
        dobj.setRtnname("SEL8");
        QExecutor qexe = new QExecutor(dobj);
        dobj.setRtnname("SEL8");
        VOBJ dvobj = dobj.getRetObject("S");           //����� ȭ�鿡�� �߻��� Object�Դϴ�.
        SQLObject  sobj = SQLmem_search_SEL8(dobj, dvobj);
        VOBJ rvobj = qexe.executeQuery(Conn, sobj);
        rvobj.setName("SEL8");
        dobj.setRetObject(rvobj);
        return dobj;
    }
    private SQLObject SQLmem_search_SEL8(DOBJ dobj, VOBJ dvobj) throws Exception
    {
        WizUtil wutil = new WizUtil(dobj);
        String   MB_CD = dobj.getRetObject("S").getRecord().get("MB_CD");   //ȸ�� �ڵ�
        String   MB_GBN = dobj.getRetObject("S").getRecord().get("MB_GBN");   //ȸ������
        String   TO = dobj.getRetObject("S").getRecord().get("TO");   //��������
        String   TODATE = dobj.getRetObject("S").getRecord().get("TODATE");   //��������
        String   FROMDATE = dobj.getRetObject("S").getRecord().get("FROMDATE");   //��������
        String   TRUST_TERMS_YN = dobj.getRetObject("S").getRecord().get("TRUST_TERMS_YN");   //��Ź�����������
        String   HANMB_NM = dobj.getRetObject("S").getRecord().get("HANMB_NM");   //ȸ�� �ŷ�ó �ڵ�
        String   SECT_CD = dobj.getRetObject("S").getRecord().get("SECT_CD");   //�ι� �ڵ�
        String   VARCHAR_TMP1 = dobj.getRetObject("S").getRecord().get("VARCHAR_TMP1");   //VARCHAR_TMP1
        String   MB_GBN2 = dobj.getRetObject("S").getRecord().get("MB_GBN2");   //ȸ�� ����2
        String   FROM = dobj.getRetObject("S").getRecord().get("FROM");   //��������
        SQLObject sobj = new SQLObject();
        String    query="";
        query +=" SELECT A.MB_CD, A.HANMB_NM, A.ENGSTMB_NM, FIDU.GET_MB_START_DAY( A.MB_CD, 'E') as ENTRN_DAY, FIDU.GET_MB_START_DAY( A.MB_CD, 'T') as TRUST_DAY, FIDU.GET_MB_START_DAY( A.MB_CD, 'T') as TRUSTTRM_START_DAY, TRANSLATE(A.PHON_NUM,'**********'||A.PHON_NUM,'**********') AS PHON_NUM, TRANSLATE(A.CP_NUM,'**********'||A.CP_NUM,'**********') AS CP_NUM, A.INS_NUM, A.QAFCSTOP_START_DAY, A.QAFCSTOP_END_DAY, FIDU.GET_MB_END_DAY( A.MB_CD, 'T') as TRUSTTRM_END_DAY, B.ADDR || B.ADDR_DETED AS ADDR , B.ADDR AS ADDR1, B.ADDR_DETED AS ADDR_DETED, substr( B.POST_NUM , 1,3) || ' - '|| substr( B.POST_NUM, 4 , 3) as POST_NUM, C.SN, A.MB_GBN1, A.MB_GBN2, A.SECT_CD, A.SMS_RECV_YN, A.IPI_NAME_NR , A.SUPPBANK_CD , A.SUPPACCN_NUM , FIDU.GET_HONG_NM(A.MB_CD) AS HONG_NAME , B.POSTSNDBK_YN , FIDU.GET_TRUST_YN(A.MB_CD) TRUST_YN  ";
        query +=" FROM FIDU.TMEM_MB A, (SELECT ADDR,ADDR_DETED,POST_NUM,MB_CD, POSTSNDBK_YN  ";
        query +=" FROM FIDU.TMEM_ADBK  ";
        query +=" WHERE ADDR_GBN = '2')B, (SELECT MB_CD, SN  ";
        query +=" FROM FIDU.TMEM_SN  ";
        query +=" WHERE SN_MNG_NUM = '03')C  ";
        query +=" WHERE A.MB_CD = B.MB_CD(+)  ";
        query +=" AND A.MB_CD = C.MB_CD(+)  ";
        query +=" AND NVL(LENGTH( A.DEL_DATE),0) = 0  ";
        query +=" AND NVL(LENGTH( A.DELPRES_ID),0) = 0  ";
        if( !MB_GBN.equals("") )
        {
            query +=" AND A.MB_GBN1 LIKE :MB_GBN  ";
        }
        if( !TRUST_TERMS_YN.equals("") )
        {
            query +=" AND A.TRUST_TERMS_YN LIKE :TRUST_TERMS_YN  ";
        }
        if( !MB_CD.equals("") )
        {
            query +=" AND A.MB_CD LIKE '%'||:MB_CD||'%'  ";
        }
        if( !HANMB_NM.equals("") )
        {
            query +=" AND ( A.HANMB_NM LIKE '%'||:HANMB_NM||'%'  ";
            query +=" OR C.SN LIKE '%'||:HANMB_NM||'%' )  ";
        }
        if( !SECT_CD.equals("") )
        {
            query +=" AND A.SECT_CD LIKE :SECT_CD  ";
        }
        if( !MB_GBN2.equals("") )
        {
            query +=" AND NVL(A.MB_GBN2,'%') LIKE :MB_GBN2  ";
        }
        query +=" AND A.INS_NUM LIKE :VARCHAR_TMP1||'%'  ";
        if( !FROM.equals("")  && !TO.equals("") )
        {
            query +=" AND A.MB_CD IN (  ";
            query +=" SELECT MB_CD  ";
            query +=" FROM FIDU.TMEM_SCSBRE  ";
            query +=" WHERE ENTRN_DAY <= :TO  ";
            query +=" AND SCS_DAY >= :FROM )  ";
        }
        if( !FROMDATE.equals("")  && !TODATE.equals("") )
        {
            query +=" AND A.MB_CD IN (  ";
            query +=" SELECT MB_CD  ";
            query +=" FROM FIDU.TMEM_TRUSTTRM  ";
            query +=" WHERE TRUSTTRM_START_DAY <= :TODATE  ";
            query +=" AND TRUSTTRM_END_DAY >= :FROMDATE)  ";
        }
        query +=" ORDER BY A.MB_CD  ";
        sobj.setSql(query);
        if(!MB_CD.equals(""))
        {
            sobj.setString("MB_CD", MB_CD);               //ȸ�� �ڵ�
        }
        if(!MB_GBN.equals(""))
        {
            sobj.setString("MB_GBN", MB_GBN);               //ȸ������
        }
        if(!TO.equals(""))
        {
            sobj.setString("TO", TO);               //��������
        }
        if(!TODATE.equals(""))
        {
            sobj.setString("TODATE", TODATE);               //��������
        }
        if(!FROMDATE.equals(""))
        {
            sobj.setString("FROMDATE", FROMDATE);               //��������
        }
        if(!TRUST_TERMS_YN.equals(""))
        {
            sobj.setString("TRUST_TERMS_YN", TRUST_TERMS_YN);               //��Ź�����������
        }
        if(!HANMB_NM.equals(""))
        {
            sobj.setString("HANMB_NM", HANMB_NM);               //ȸ�� �ŷ�ó �ڵ�
        }
        if(!SECT_CD.equals(""))
        {
            sobj.setString("SECT_CD", SECT_CD);               //�ι� �ڵ�
        }
        sobj.setString("VARCHAR_TMP1", VARCHAR_TMP1);               //VARCHAR_TMP1
        if(!MB_GBN2.equals(""))
        {
            sobj.setString("MB_GBN2", MB_GBN2);               //ȸ�� ����2
        }
        if(!FROM.equals(""))
        {
            sobj.setString("FROM", FROM);               //��������
        }
        return sobj;
    }
    // �̸�
    public DOBJ CALLmem_search_SEL10(Connection Conn, DOBJ dobj) throws Exception
    {
        WizUtil wutil = new WizUtil(dobj);
        dobj.setRtnname("SEL10");
        QExecutor qexe = new QExecutor(dobj);
        dobj.setRtnname("SEL10");
        VOBJ dvobj = dobj.getRetObject("S");           //����� ȭ�鿡�� �߻��� Object�Դϴ�.
        SQLObject  sobj = SQLmem_search_SEL10(dobj, dvobj);
        VOBJ rvobj = qexe.executeQuery(Conn, sobj);
        rvobj.setName("SEL10");
        dobj.setRetObject(rvobj);
        return dobj;
    }
    private SQLObject SQLmem_search_SEL10(DOBJ dobj, VOBJ dvobj) throws Exception
    {
        WizUtil wutil = new WizUtil(dobj);
        String   MB_CD = dobj.getRetObject("S").getRecord().get("MB_CD");   //ȸ�� �ڵ�
        String   MB_GBN = dobj.getRetObject("S").getRecord().get("MB_GBN");   //ȸ������
        String   TO = dobj.getRetObject("S").getRecord().get("TO");   //��������
        String   TODATE = dobj.getRetObject("S").getRecord().get("TODATE");   //��������
        String   FROMDATE = dobj.getRetObject("S").getRecord().get("FROMDATE");   //��������
        String   TRUST_TERMS_YN = dobj.getRetObject("S").getRecord().get("TRUST_TERMS_YN");   //��Ź�����������
        String   HANMB_NM = dobj.getRetObject("S").getRecord().get("HANMB_NM");   //ȸ�� �ŷ�ó �ڵ�
        String   SECT_CD = dobj.getRetObject("S").getRecord().get("SECT_CD");   //�ι� �ڵ�
        String   MB_GBN2 = dobj.getRetObject("S").getRecord().get("MB_GBN2");   //ȸ�� ����2
        String   FROM = dobj.getRetObject("S").getRecord().get("FROM");   //��������
        SQLObject sobj = new SQLObject();
        String    query="";
        query +=" SELECT A.MB_CD, A.HANMB_NM, A.ENGSTMB_NM, FIDU.GET_MB_START_DAY( A.MB_CD, 'E') as ENTRN_DAY, FIDU.GET_MB_START_DAY( A.MB_CD, 'T') as TRUST_DAY, FIDU.GET_MB_START_DAY( A.MB_CD, 'T') as TRUSTTRM_START_DAY, TRANSLATE(A.PHON_NUM,'**********'||A.PHON_NUM,'**********') AS PHON_NUM, TRANSLATE(A.CP_NUM,'**********'||A.CP_NUM,'**********') AS CP_NUM, A.INS_NUM, A.QAFCSTOP_START_DAY, A.QAFCSTOP_END_DAY, FIDU.GET_MB_END_DAY( A.MB_CD, 'T') as TRUSTTRM_END_DAY, B.ADDR || B.ADDR_DETED AS ADDR , B.ADDR AS ADDR1, B.ADDR_DETED AS ADDR_DETED, substr( B.POST_NUM , 1,3) || ' - '|| substr( B.POST_NUM, 4 , 3) as POST_NUM, C.SN, A.MB_GBN1, A.MB_GBN2, A.SECT_CD, A.SMS_RECV_YN, A.IPI_NAME_NR , A.SUPPBANK_CD , A.SUPPACCN_NUM , FIDU.GET_HONG_NM(A.MB_CD) AS HONG_NAME , B.POSTSNDBK_YN , FIDU.GET_TRUST_YN(A.MB_CD) TRUST_YN  ";
        query +=" FROM FIDU.TMEM_MB A, (SELECT ADDR, ADDR_DETED,POST_NUM,MB_CD, POSTSNDBK_YN  ";
        query +=" FROM FIDU.TMEM_ADBK  ";
        query +=" WHERE ADDR_GBN = '2')B, (SELECT MB_CD, SN  ";
        query +=" FROM FIDU.TMEM_SN  ";
        query +=" WHERE SN_MNG_NUM = '03')C  ";
        query +=" WHERE A.MB_CD = B.MB_CD(+)  ";
        query +=" AND A.MB_CD = C.MB_CD(+)  ";
        query +=" AND NVL(LENGTH( A.DEL_DATE),0) = 0  ";
        query +=" AND NVL(LENGTH( A.DELPRES_ID),0) = 0  ";
        if( !MB_CD.equals("") )
        {
            query +=" AND A.MB_CD LIKE '%'||:MB_CD||'%'  ";
        }
        query +=" AND A.HANMB_NM LIKE '%'||:HANMB_NM||'%'  ";
        if( !MB_GBN.equals("") )
        {
            query +=" AND NVL(A.MB_GBN1,'%') LIKE :MB_GBN  ";
        }
        if( !TRUST_TERMS_YN.equals("") )
        {
            query +=" AND A.TRUST_TERMS_YN LIKE :TRUST_TERMS_YN  ";
        }
        if( !SECT_CD.equals("") )
        {
            query +=" AND A.SECT_CD LIKE :SECT_CD  ";
        }
        if( !MB_GBN2.equals("") )
        {
            query +=" AND NVL(A.MB_GBN2,'%') LIKE :MB_GBN2  ";
        }
        if( !FROM.equals("")  && !TO.equals("") )
        {
            query +=" AND A.MB_CD IN (  ";
            query +=" SELECT MB_CD  ";
            query +=" FROM FIDU.TMEM_SCSBRE  ";
            query +=" WHERE ENTRN_DAY <= :TO  ";
            query +=" AND SCS_DAY >= :FROM )  ";
        }
        if( !FROMDATE.equals("")  && !TODATE.equals("") )
        {
            query +=" AND A.MB_CD IN (  ";
            query +=" SELECT MB_CD  ";
            query +=" FROM FIDU.TMEM_TRUSTTRM  ";
            query +=" WHERE TRUSTTRM_START_DAY <= :TODATE  ";
            query +=" AND TRUSTTRM_END_DAY >= :FROMDATE)  ";
        }
        query +=" UNION  ";
        query +=" SELECT A.MB_CD, A.HANMB_NM, A.ENGSTMB_NM, FIDU.GET_MB_START_DAY( A.MB_CD, 'E') as ENTRN_DAY, FIDU.GET_MB_START_DAY( A.MB_CD, 'T') as TRUST_DAY, FIDU.GET_MB_START_DAY( A.MB_CD, 'T') as TRUSTTRM_START_DAY, A.PHON_NUM, A.CP_NUM, A.INS_NUM, A.QAFCSTOP_START_DAY, A.QAFCSTOP_END_DAY, FIDU.GET_MB_END_DAY( A.MB_CD, 'T') as TRUSTTRM_END_DAY, B.ADDR || B.ADDR_DETED AS ADDR , B.ADDR AS ADDR1, B.ADDR_DETED AS ADDR_DETED, substr( B.POST_NUM , 1,3) || ' - '|| substr( B.POST_NUM, 4 , 3) as POST_NUM, C.SN, A.MB_GBN1, A.MB_GBN2, A.SECT_CD, A.SMS_RECV_YN, A.IPI_NAME_NR , A.SUPPBANK_CD , A.SUPPACCN_NUM , FIDU.GET_HONG_NM(A.MB_CD) AS HONG_NAME , B.POSTSNDBK_YN , FIDU.GET_TRUST_YN(A.MB_CD) TRUST_YN  ";
        query +=" FROM FIDU.TMEM_MB A, (SELECT ADDR,ADDR_DETED,POST_NUM,MB_CD, POSTSNDBK_YN  ";
        query +=" FROM FIDU.TMEM_ADBK  ";
        query +=" WHERE ADDR_GBN = '2') B, FIDU.TMEM_SN C  ";
        query +=" WHERE A.MB_CD = B.MB_CD(+)  ";
        query +=" AND A.MB_CD = C.MB_CD(+)  ";
        query +=" AND NVL(LENGTH( A.DEL_DATE),0) = 0  ";
        query +=" AND NVL(LENGTH( A.DELPRES_ID),0) = 0  ";
        if( !MB_CD.equals("") )
        {
            query +=" AND A.MB_CD LIKE '%'||:MB_CD||'%'  ";
        }
        query +=" AND C.SN LIKE '%'||:HANMB_NM||'%'  ";
        if( !MB_GBN.equals("") )
        {
            query +=" AND NVL(A.MB_GBN1,'%') LIKE :MB_GBN  ";
        }
        if( !TRUST_TERMS_YN.equals("") )
        {
            query +=" AND A.TRUST_TERMS_YN LIKE :TRUST_TERMS_YN  ";
        }
        if( !SECT_CD.equals("") )
        {
            query +=" AND A.SECT_CD LIKE :SECT_CD  ";
        }
        if( !MB_GBN2.equals("") )
        {
            query +=" AND NVL(A.MB_GBN2,'%') LIKE :MB_GBN2  ";
        }
        if( !FROM.equals("")  && !TO.equals("") )
        {
            query +=" AND A.MB_CD IN (  ";
            query +=" SELECT MB_CD  ";
            query +=" FROM FIDU.TMEM_SCSBRE  ";
            query +=" WHERE ENTRN_DAY <= :TO  ";
            query +=" AND SCS_DAY >= :FROM )  ";
        }
        if( !FROMDATE.equals("")  && !TODATE.equals("") )
        {
            query +=" AND A.MB_CD IN (  ";
            query +=" SELECT MB_CD  ";
            query +=" FROM FIDU.TMEM_TRUSTTRM  ";
            query +=" WHERE TRUSTTRM_START_DAY <= :TODATE  ";
            query +=" AND TRUSTTRM_END_DAY >= :FROMDATE)  ";
        }
        query +=" ORDER BY 1  ";
        sobj.setSql(query);
        if(!MB_CD.equals(""))
        {
            sobj.setString("MB_CD", MB_CD);               //ȸ�� �ڵ�
        }
        if(!MB_GBN.equals(""))
        {
            sobj.setString("MB_GBN", MB_GBN);               //ȸ������
        }
        if(!TO.equals(""))
        {
            sobj.setString("TO", TO);               //��������
        }
        if(!TODATE.equals(""))
        {
            sobj.setString("TODATE", TODATE);               //��������
        }
        if(!FROMDATE.equals(""))
        {
            sobj.setString("FROMDATE", FROMDATE);               //��������
        }
        if(!TRUST_TERMS_YN.equals(""))
        {
            sobj.setString("TRUST_TERMS_YN", TRUST_TERMS_YN);               //��Ź�����������
        }
        sobj.setString("HANMB_NM", HANMB_NM);               //ȸ�� �ŷ�ó �ڵ�
        if(!SECT_CD.equals(""))
        {
            sobj.setString("SECT_CD", SECT_CD);               //�ι� �ڵ�
        }
        if(!MB_GBN2.equals(""))
        {
            sobj.setString("MB_GBN2", MB_GBN2);               //ȸ�� ����2
        }
        if(!FROM.equals(""))
        {
            sobj.setString("FROM", FROM);               //��������
        }
        return sobj;
    }
    // ȸ�������˻�
    public DOBJ CALLmem_search_SEL7(Connection Conn, DOBJ dobj) throws Exception
    {
        WizUtil wutil = new WizUtil(dobj);
        dobj.setRtnname("SEL7");
        QExecutor qexe = new QExecutor(dobj);
        dobj.setRtnname("SEL7");
        VOBJ dvobj = dobj.getRetObject("S");           //����� ȭ�鿡�� �߻��� Object�Դϴ�.
        SQLObject  sobj = SQLmem_search_SEL7(dobj, dvobj);
        qexe.DispSelectSql(sobj);
        VOBJ rvobj = qexe.executeQuery(Conn, sobj);
        rvobj.setName("SEL7");
        dobj.setRetObject(rvobj);
        return dobj;
    }
    private SQLObject SQLmem_search_SEL7(DOBJ dobj, VOBJ dvobj) throws Exception
    {
        WizUtil wutil = new WizUtil(dobj);
        String   MB_CD = dobj.getRetObject("S").getRecord().get("MB_CD");   //ȸ�� �ڵ�
        String   MB_GBN = dobj.getRetObject("S").getRecord().get("MB_GBN");   //ȸ������
        String   TO = dobj.getRetObject("S").getRecord().get("TO");   //��������
        String   TODATE = dobj.getRetObject("S").getRecord().get("TODATE");   //��������
        String   FROMDATE = dobj.getRetObject("S").getRecord().get("FROMDATE");   //��������
        String   TRUST_TERMS_YN = dobj.getRetObject("S").getRecord().get("TRUST_TERMS_YN");   //��Ź�����������
        String   HANMB_NM = dobj.getRetObject("S").getRecord().get("HANMB_NM");   //ȸ�� �ŷ�ó �ڵ�
        String   SECT_CD = dobj.getRetObject("S").getRecord().get("SECT_CD");   //�ι� �ڵ�
        String   MB_GBN2 = dobj.getRetObject("S").getRecord().get("MB_GBN2");   //ȸ�� ����2
        String   FROM = dobj.getRetObject("S").getRecord().get("FROM");   //��������
        SQLObject sobj = new SQLObject();
        String    query="";
        query +=" SELECT A.MB_CD, A.HANMB_NM, A.ENGSTMB_NM, FIDU.GET_MB_START_DAY( A.MB_CD, 'E') as ENTRN_DAY, TRANSLATE(A.PHON_NUM,'**********'||A.PHON_NUM,'**********') AS PHON_NUM, TRANSLATE(A.CP_NUM,'**********'||A.CP_NUM,'**********') AS CP_NUM, A.INS_NUM, A.QAFCSTOP_START_DAY, A.QAFCSTOP_END_DAY, FIDU.GET_MB_START_DAY( A.MB_CD, 'T') as TRUST_DAY, FIDU.GET_MB_START_DAY( A.MB_CD, 'T') as TRUSTTRM_START_DAY, FIDU.GET_MB_END_DAY( A.MB_CD, 'T') as TRUSTTRM_END_DAY, B.ADDR || B.ADDR_DETED AS ADDR , B.ADDR AS ADDR1, B.ADDR_DETED AS ADDR_DETED, substr( B.POST_NUM , 1,3) || ' - '|| substr( B.POST_NUM, 4 , 3) as POST_NUM, C.SN, A.MB_GBN1, A.MB_GBN2, A.SECT_CD, A.SMS_RECV_YN, A.IPI_NAME_NR , A.SUPPBANK_CD , A.SUPPACCN_NUM , FIDU.GET_HONG_NM(A.MB_CD) AS HONG_NAME , B.POSTSNDBK_YN , FIDU.GET_TRUST_YN(A.MB_CD) TRUST_YN  ";
        query +=" FROM FIDU.TMEM_MB A, (SELECT MB_CD,ADDR,ADDR_DETED,POST_NUM, POSTSNDBK_YN  ";
        query +=" FROM FIDU.TMEM_ADBK  ";
        query +=" WHERE ADDR_GBN = '2')B, (SELECT MB_CD,SN  ";
        query +=" FROM FIDU.TMEM_SN  ";
        query +=" WHERE SN_MNG_NUM = '03')C  ";
        query +=" WHERE A.MB_CD = B.MB_CD(+)  ";
        query +=" AND A.MB_CD = C.MB_CD(+)  ";
        query +=" AND NVL(LENGTH( A.DEL_DATE),0) = 0  ";
        query +=" AND NVL(LENGTH( A.DELPRES_ID),0) = 0  ";
        if( !MB_CD.equals("") )
        {
            query +=" AND A.MB_CD LIKE '%'||:MB_CD||'%'  ";
        }
        if( !HANMB_NM.equals("") )
        {
            query +=" AND ( A.HANMB_NM LIKE '%'||:HANMB_NM||'%'  ";
            query +=" OR C.SN LIKE '%'||:HANMB_NM||'%' )  ";
        }
        if( !MB_GBN.equals("") )
        {
            query +=" AND NVL(A.MB_GBN1,'%') LIKE :MB_GBN  ";
        }
        if( !TRUST_TERMS_YN.equals("") )
        {
            query +=" AND A.TRUST_TERMS_YN LIKE :TRUST_TERMS_YN  ";
        }
        if( !SECT_CD.equals("") )
        {
            query +=" AND A.SECT_CD LIKE :SECT_CD  ";
        }
        if( !MB_GBN2.equals("") )
        {
            query +=" AND NVL(A.MB_GBN2,'%') LIKE :MB_GBN2  ";
        }
        if( !FROM.equals("")  && !TO.equals("") )
        {
            query +=" AND A.MB_CD IN (  ";
            query +=" SELECT MB_CD  ";
            query +=" FROM FIDU.TMEM_SCSBRE  ";
            query +=" WHERE ENTRN_DAY <= :TO  ";
            query +=" AND SCS_DAY >= :FROM )  ";
        }
        if( !FROMDATE.equals("")  && !TODATE.equals("") )
        {
            query +=" AND A.MB_CD IN (  ";
            query +=" SELECT MB_CD  ";
            query +=" FROM FIDU.TMEM_TRUSTTRM  ";
            query +=" WHERE TRUSTTRM_START_DAY <= :TODATE  ";
            query +=" AND TRUSTTRM_END_DAY >= :FROMDATE)  ";
        }
        query +=" ORDER BY A.MB_CD  ";
        sobj.setSql(query);
        if(!MB_CD.equals(""))
        {
            sobj.setString("MB_CD", MB_CD);               //ȸ�� �ڵ�
        }
        if(!MB_GBN.equals(""))
        {
            sobj.setString("MB_GBN", MB_GBN);               //ȸ������
        }
        if(!TO.equals(""))
        {
            sobj.setString("TO", TO);               //��������
        }
        if(!TODATE.equals(""))
        {
            sobj.setString("TODATE", TODATE);               //��������
        }
        if(!FROMDATE.equals(""))
        {
            sobj.setString("FROMDATE", FROMDATE);               //��������
        }
        if(!TRUST_TERMS_YN.equals(""))
        {
            sobj.setString("TRUST_TERMS_YN", TRUST_TERMS_YN);               //��Ź�����������
        }
        if(!HANMB_NM.equals(""))
        {
            sobj.setString("HANMB_NM", HANMB_NM);               //ȸ�� �ŷ�ó �ڵ�
        }
        if(!SECT_CD.equals(""))
        {
            sobj.setString("SECT_CD", SECT_CD);               //�ι� �ڵ�
        }
        if(!MB_GBN2.equals(""))
        {
            sobj.setString("MB_GBN2", MB_GBN2);               //ȸ�� ����2
        }
        if(!FROM.equals(""))
        {
            sobj.setString("FROM", FROM);               //��������
        }
        return sobj;
    }
    //##**$$mem_search
    //##**$$end
}
