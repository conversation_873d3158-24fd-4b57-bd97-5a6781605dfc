<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="kr.or.komca.admin.role.mapper.query.RoleQueryMapper">

    <!-- 역할 목록 조회 쿼리 -->
    <select id="getRoleList" resultType="kr.or.komca.admin.role.dto.query.response.Role">
        SELECT 
            ROLE_CD
            ,ROLE_NM
            ,REMAK
            ,USE_YN
            ,INSPERS_ID
            ,INS_DT
            ,MODPERS_ID
            ,MOD_DT
        FROM 
            TENV_ROLE
        <where>
            DEL_YN = 'N'
            <if test="condition != null">
                <if test="condition.roleNm != null and condition.roleNm != ''">
                    AND ROLE_NM LIKE '%' || #{condition.roleNm} || '%'
                </if>
                <if test="condition.roleCd != null and condition.roleCd != ''">
                    AND role_cd = #{condition.roleCd}
                </if>
                <if test="condition.useYn != null and condition.useYn != ''">
                    AND use_yn = #{condition.useYn}
                </if>
            </if>
        </where>
        -- IBSheet 정렬
        <choose>
            <when test="condition.sortColumn != null and condition.sortColumn != ''">
                ORDER BY
                    ${condition.sortColumn} ${condition.sortOrder}
            </when>
            <otherwise>
                ORDER BY
                    ROLE_CD
            </otherwise>
        </choose>
    </select>
    
    <!-- 역할 코드로 단일 역할 조회 쿼리 -->
    <select id="getRoleByRoleCd" resultType="kr.or.komca.admin.role.dto.query.response.Role">
        SELECT 
            ROLE_CD
            ,ROLE_NM
            ,REMAK
            ,USE_YN
            ,INSPERS_ID
            ,INS_DT
            ,MODPERS_ID
            ,MOD_DT
        FROM 
            TENV_ROLE
        WHERE 
            ROLE_CD = #{roleCd}
            AND DEL_YN = 'N'
    </select>

    <!-- ROLE CD로 메뉴 권한 조회 쿼리 (존재하지 않는 메뉴는 MENU NM이 NULL) -->
    <select id="getRoleMenuList" resultType="kr.or.komca.admin.role.dto.query.response.RoleMenu">
        SELECT A.MENU_CD, TM.MENU_NM, AUTH_SELECT, AUTH_EDIT, AUTH_DOWN, AUTH_MASK, A.USE_YN
        FROM TENV_ROLE_MENU A
        LEFT OUTER JOIN TENV_MENU TM
        ON TM.MENU_CD = A.MENU_CD
        WHERE A.ROLE_CD = #{roleCd}
            AND A.DEL_YN = 'N'
        ORDER BY MENU_CD
    </select>

    <!-- ROLE CD List로 메뉴 권한 리스트 조회 쿼리 (중복 권한 통합 처리) -->
    <select id="getRoleMenuListByRoleCdList" resultType="kr.or.komca.admin.role.dto.query.response.RoleMenu">
        SELECT
            A.menu_cd,
            TM.menu_nm,
            MAX(CASE WHEN A.use_yn = 'Y' THEN A.auth_select ELSE 'N' END) AS auth_select,
            MAX(CASE WHEN A.use_yn = 'Y' THEN A.auth_edit ELSE 'N' END) AS auth_edit,
            MAX(CASE WHEN A.use_yn = 'Y' THEN A.auth_down ELSE 'N' END) AS auth_down,
            MAX(CASE WHEN A.use_yn = 'Y' THEN A.auth_mask ELSE 'N' END) AS auth_mask,
            MAX(A.use_yn) AS use_yn
        FROM tenv_role_menu A
        INNER JOIN TENV_MENU TM ON A.menu_cd = TM.menu_cd
        WHERE A.ROLE_CD IN
        <foreach collection="roleCdList" item="roleCd" open="(" separator="," close=")">
            #{roleCd}
        </foreach>
            AND A.DEL_YN = 'N'
        GROUP BY A.menu_cd, TM.menu_nm
        ORDER BY A.menu_cd
    </select>

    <!--  역할에 존재하는 메뉴 권한인지 확인 (리스트)  -->
    <select id="isExistRoleMenuList" resultType="boolean">
        SELECT
            CASE WHEN COUNT(DISTINCT MENU_CD) = ${menuList.size()}
            THEN 1
            ELSE 0
            END AS result
        FROM TENV_ROLE_MENU
        WHERE ROLE_CD = #{roleCd}
            AND DEL_YN = 'N'
            AND MENU_CD IN
        <foreach item="menuCd" collection="menuList" open="(" separator="," close=")">
            #{menuCd}
        </foreach>
    </select>

    <!--  존재하는 역할인지 확인  -->
    <select id="isExistRole" resultType="boolean">
        SELECT CASE
            WHEN EXISTS (
                    SELECT 1
                    FROM TENV_ROLE
                    WHERE ROLE_CD = #{roleCd}
                    AND DEL_YN = 'N'
                    AND ROWNUM = 1
                )
                THEN 1
                ELSE 0
            END
        FROM DUAL
    </select>

    <!--  존재하는 역할 리스트인지 확인 후 존재하지 않는 요소 반환  -->
    <select id="getInvalidRoleCdList" resultType="string">
        SELECT input_cd.role_cd
        FROM (
            <foreach item="roleCd" collection="roleCdList" separator="UNION ALL">
                SELECT #{roleCd} AS role_cd FROM DUAL
            </foreach>
        ) input_cd
        WHERE NOT EXISTS (
            SELECT 1
            FROM TENV_ROLE r
            WHERE r.ROLE_CD = input_cd.role_cd
            AND r.DEL_YN = 'N'
        )
    </select>

    <!-- 다음 역할 코드 조회 -->
    <select id="getNextRoleCd" resultType="String">
        SELECT LPAD(NVL(MAX(TO_NUMBER(ROLE_CD))+1,1),5,'0') 
        FROM TENV_ROLE
    </select>
</mapper>
