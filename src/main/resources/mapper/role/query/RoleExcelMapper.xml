<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="kr.or.komca.admin.role.mapper.query.RoleExcelMapper">

    <!-- 역할 목록 조회 쿼리 -->
    <select id="getRoleExcelList" resultType="kr.or.komca.admin.role.dto.query.response.RoleExcel">
        SELECT 
            ROLE_CD
            ,ROLE_NM
            ,REMAK
            ,USE_YN
            ,INSPERS_ID
            ,INS_DT
            ,MODPERS_ID
            ,MOD_DT
        FROM 
            TENV_ROLE
        <where>
            DEL_YN = 'N'
            <if test="condition != null">
                <if test="condition.roleNm != null and condition.roleNm != ''">
                    AND ROLE_NM LIKE '%' || #{condition.roleNm} || '%'
                </if>
                <if test="condition.roleCd != null and condition.roleCd != ''">
                    AND role_cd = #{condition.roleCd}
                </if>
                <if test="condition.useYn != null and condition.useYn != ''">
                    AND use_yn = #{condition.useYn}
                </if>
            </if>
        </where>
        -- IBSheet 정렬
        <choose>
            <when test="condition.sortColumn != null and condition.sortColumn != ''">
                ORDER BY
                    ${condition.sortColumn} ${condition.sortOrder}
            </when>
            <otherwise>
                ORDER BY
                    ROLE_CD
            </otherwise>
        </choose>
    </select>
</mapper>
