<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="kr.or.komca.admin.role.mapper.command.RoleCommandMapper">

    <!-- 역할 생성 -->
    <insert id="createRole">
        INSERT INTO TENV_ROLE (
            ROLE_CD
            ,ROLE_NM
            ,REMAK
            ,USE_YN
            ,INSPERS_ID
            ,INS_DT
        ) VALUES (
            (SELECT LPAD(NVL(MAX(TO_NUMBER(ROLE_CD))+1,1),5,'0') FROM TENV_ROLE)
            ,#{command.roleNm}
            ,#{command.remak}
            ,#{command.useYn}
            ,#{inspersId}
            ,SYSDATE
        )
    </insert>

    <!-- 역할 수정 -->
    <update id="updateRole">
        UPDATE TENV_ROLE
        SET 
            ROLE_NM = #{command.roleNm}
            ,REMAK = #{command.remak}
            ,USE_YN = #{command.useYn}
            ,MODPERS_ID = #{modpersId}
            ,MOD_DT = SYSDATE
        WHERE 
            ROLE_CD = #{roleCd}
    </update>


    <!-- 역할 논리삭제 -->
    <update id="deleteRoleLogical">
        UPDATE TENV_ROLE
        SET 
            DEL_YN = 'Y'
            ,DELPERS_ID = #{delpersId}
            ,DEL_DT = SYSDATE
        WHERE 
            ROLE_CD = #{roleCd}
            AND DEL_YN = 'N'
    </update>


    <!-- 해당 역할의 메뉴 권한 삭제 -->
    <delete id="deleteRoleMenu">
        DELETE FROM TENV_ROLE_MENU
        WHERE ROLE_CD = #{roleCd}
        AND MENU_CD IN
        <foreach item="menuCd" collection="menuList" open="(" separator="," close=")">
            #{menuCd}
        </foreach>
    </delete>

    <!-- 해당 역할의 모든 메뉴 권한 논리삭제 (역할 삭제시 사용) -->
    <update id="deleteRoleMenuLogical">
        UPDATE TENV_ROLE_MENU
        SET 
            DEL_YN = 'Y'
            ,DELPERS_ID = #{delpersId}
            ,DEL_DT = SYSDATE
        WHERE 
            ROLE_CD = #{roleCd}
            AND DEL_YN = 'N'
    </update>

    <!-- 역할별 메뉴 권한 저장 -->
    <insert id="saveRoleMenu">
        INSERT ALL
        <foreach collection="command" item="menu">
            INTO TENV_ROLE_MENU (
            ROLE_CD
            ,MENU_CD
            ,AUTH_SELECT
            ,AUTH_EDIT
            ,AUTH_DOWN
            ,AUTH_MASK
            ,INSPERS_ID
            ,INS_DT
            ) VALUES (
                #{roleCd}
                ,#{menu.menuCd}
                ,#{menu.authSelect}
                ,#{menu.authEdit}
                ,#{menu.authDown}
                ,#{menu.authMask}
                ,#{inspersId}
                ,SYSDATE
            )
        </foreach>
        SELECT * FROM DUAL
    </insert>

    <!-- 역할별 메뉴 권한 수정 -->
    <update id="updateRoleMenu">
        MERGE INTO TENV_ROLE_MENU t
        USING (SELECT #{roleCd} AS ROLE_CD, #{command.menuCd} AS MENU_CD FROM DUAL) s
        ON (t.ROLE_CD = s.ROLE_CD AND t.MENU_CD = s.MENU_CD)
        WHEN MATCHED THEN
            UPDATE SET
                t.AUTH_SELECT = #{command.authSelect}
                ,t.AUTH_DOWN = #{command.authDown}
                ,t.AUTH_EDIT = #{command.authEdit}
                ,t.AUTH_MASK = #{command.authMask}
                ,t.USE_YN = #{command.useYn}
                ,t.MODPERS_ID = #{modpersId}
                ,t.MOD_DT = SYSDATE
        WHEN NOT MATCHED THEN
            INSERT (
                ROLE_CD
                ,MENU_CD
                ,AUTH_SELECT
                ,AUTH_EDIT
                ,AUTH_DOWN
                ,AUTH_MASK
                ,USE_YN
                ,INSPERS_ID
                ,INS_DT
            ) VALUES (
                #{roleCd}
                ,#{command.menuCd}
                ,#{command.authSelect}
                ,#{command.authEdit}
                ,#{command.authDown}
                ,#{command.authMask}
                ,#{command.useYn}
                ,#{modpersId}
                ,SYSDATE
            )
    </update>
</mapper>
