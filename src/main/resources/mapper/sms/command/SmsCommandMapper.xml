<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="kr.or.komca.admin.sms.mapper.command.SmsCommandMapper">

    <!-- SMS 발송 이력 저장 -->
    <insert id="insertSmsMessage">
        INSERT INTO TST_SMS_SEND (
            MSG_ID                    -- SMS KEY
            ,USER_ID                  -- 사용자번호  
            ,SMS_MSG                  -- SMS 메시지
            ,SEND_DATE               -- 전송일자
            ,SEND_STATUS             -- 전송상태
            ,DEST_TYPE               -- 수신자타입
            ,SUBJECT                 -- 제목
            ,CALLBACK                -- 발신번호
            ,DEST_COUNT              -- 수신자수
            ,DEST_INFO               -- 수신자정보
            ,CHANNEL                 -- 발송채널
            ,SENDER_TYPE             -- 발신번호구분
            ,TMPL_CD                 -- 템플릿코드
            ,IMMEDIATE_YN            -- 즉시발송여부
            ,INS_DT                  -- 등록일시
            ,INSPERS_ID              -- 등록자ID
        ) VALUES (
            TST_SMS_SEND_SEQ.NEXTVAL
            ,#{recipient.recipientId}
            ,#{command.message}
            ,TO_CHAR(SYSDATE, 'YYYYMMDDHH24MISS')
            ,'PENDING'
            ,#{recipient.recipientType}
            ,#{command.subject}
            ,#{command.callback}
            ,1
            ,#{recipient.phoneNumber}
            ,#{channel}
            ,#{command.senderType}
            ,#{command.templateCode}
            ,#{command.immediateYn}
            ,SYSDATE
            ,#{inspersId}
        )
    </insert>

    <!-- SMS 발송 상태 업데이트 -->
    <update id="updateSmsStatus">
        UPDATE TST_SMS_SEND
        SET SEND_STATUS = #{status}
            ,SEND_RESULT = #{resultMessage}
            ,SEND_PROC_TIME = TO_CHAR(SYSDATE, 'YYYYMMDDHH24MISS')
            ,MOD_DT = SYSDATE
        WHERE MSG_ID = #{msgId}
    </update>

    <!-- 선택된 수신자 임시 저장 -->
    <insert id="insertSelectedRecipient">
        INSERT INTO TST_SMS_SELECTED_RECIPIENT (
            SESSION_ID                -- 세션ID
            ,USER_ID                  -- 사용자ID
            ,RECIPIENT_ID             -- 수신자ID
            ,RECIPIENT_TYPE           -- 수신자타입
            ,RECIPIENT_NAME           -- 수신자명
            ,PHONE_NUMBER             -- 전화번호
            ,PHONE_NUMBER_TYPE        -- 전화번호타입
            ,INS_DT                   -- 등록일시
            ,INSPERS_ID               -- 등록자ID
        ) VALUES (
            #{sessionId}
            ,#{userId}
            ,#{recipient.recipientId}
            ,#{recipient.recipientType}
            ,#{recipient.recipientName}
            ,#{recipient.phoneNumber}
            ,#{recipient.phoneNumberType}
            ,SYSDATE
            ,#{userId}
        )
    </insert>

    <!-- 기존 선택된 수신자 목록 삭제 -->
    <delete id="deleteSelectedRecipients">
        DELETE FROM TST_SMS_SELECTED_RECIPIENT
        WHERE SESSION_ID = #{sessionId}
        AND USER_ID = #{userId}
    </delete>

    <!-- 특정 선택된 수신자 삭제 -->
    <delete id="deleteSelectedRecipient">
        DELETE FROM TST_SMS_SELECTED_RECIPIENT
        WHERE SESSION_ID = #{sessionId}
        AND USER_ID = #{userId}
        AND RECIPIENT_ID = #{recipientId}
        AND RECIPIENT_TYPE = #{recipientType}
    </delete>

    <!-- 선택된 수신자 수 조회 -->
    <select id="countSelectedRecipients" resultType="int">
        SELECT COUNT(*)
        FROM TST_SMS_SELECTED_RECIPIENT
        WHERE SESSION_ID = #{sessionId}
        AND USER_ID = #{userId}
    </select>

</mapper>