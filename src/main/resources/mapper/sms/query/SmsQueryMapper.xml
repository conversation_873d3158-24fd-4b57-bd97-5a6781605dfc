<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="kr.or.komca.admin.sms.mapper.query.SmsQueryMapper">

    <!-- 발신번호 목록 조회 -->
    <select id="getSenderNumbers" resultType="kr.or.komca.admin.sms.dto.query.response.SenderNumber">
        SELECT CODE_CD                 as senderType
               ,CODE_NM                 as senderTypeName
               ,CODE_VAL                as phoneNumber
               ,USE_YN                  as useYn
        FROM TENV_CODE
        WHERE HIGH_CD = 'SMS_SENDER'
          AND USE_YN = 'Y'
        ORDER BY CODE_CD
    </select>

    <!-- 템플릿 목록 조회 (공통코드 기반) -->
    <select id="getTemplates" resultType="kr.or.komca.admin.sms.dto.query.response.TemplateInfo">
        SELECT CODE_CD                 as templateCode
               ,CODE_NM                 as templateName
               ,SUBSTR(CODE_CD, 1, INSTR(CODE_CD, '_') - 1) as category
               ,USE_YN                  as useYn
        FROM TENV_CODE
        WHERE HIGH_CD = '00457'
          AND USE_YN = 'Y'
        ORDER BY CODE_CD
    </select>

    <!-- 템플릿 내용 조회 (DB 기반) -->
    <select id="getTemplateContent" resultType="kr.or.komca.admin.sms.dto.query.response.TemplateContent">
        SELECT t.TMPL_CD               as templateCode
               ,c.CODE_NM               as templateName
               ,t.TMPL_CONTENT          as content
               ,CASE WHEN t.TMPL_VARIABLES IS NOT NULL 
                     THEN t.TMPL_VARIABLES 
                     ELSE '' END        as variables
               ,t.CATEGORY              as category
               ,t.USE_YN                as useYn
        FROM TENV_KAKAO_TEMPLATE t
        INNER JOIN TENV_CODE c ON c.CODE_CD = t.TMPL_CD AND c.HIGH_CD = '00457'
        WHERE t.TMPL_CD = #{templateCode}
          AND t.USE_YN = 'Y'
          AND c.USE_YN = 'Y'
    </select>

    <!-- SMS 발송 활성화 플래그 조회 -->
    <select id="getSmsEnabledFlag" resultType="String">
        SELECT FLAG
        FROM TENV_FLAG
        WHERE FLAG_GBN = 'SMS_SEND_ENABLED'
    </select>

    <!-- 카카오 발송 활성화 플래그 조회 -->
    <select id="getKakaoEnabledFlag" resultType="String">
        SELECT FLAG
        FROM TENV_FLAG
        WHERE FLAG_GBN = 'KAKAO_SEND_ENABLED'
    </select>

    <!-- 수신자 검색 (ASIS lev13_s01.java 로직 변환) -->
    <select id="searchRecipients" resultType="kr.or.komca.admin.sms.dto.query.response.RecipientSearchResult">
        <!-- 거래처 조회 -->
        <if test="condition.recipientType == null or condition.recipientType == 'ALL' or condition.recipientType == 'BSCON'">
        SELECT BSCON_CD                                                                    as recipientId
               ,'BSCON'                                                                    as recipientType
               ,BSCONHAN_NM                                                                as name
               ,REPPRES_NM                                                                 as representativeName
               ,TRANSLATE(PHON_NUM,'0123456789'||PHON_NUM,'0123456789')                   as phoneNumber1
               ,TRANSLATE(PHON_NUM2,'0123456789'||PHON_NUM2,'0123456789')                 as phoneNumber2
               ,'Y'                                                                        as smsReceiveYn
               ,MSTR_MDM_CD                                                                as businessCode
        FROM TLEV_BSCON
        WHERE 1=1
        <if test="condition.searchKeyword != null and condition.searchKeyword != ''">
            AND (BSCONHAN_NM LIKE '%' || #{condition.searchKeyword} || '%'
                 OR REPPRES_NM LIKE '%' || #{condition.searchKeyword} || '%')
        </if>
        <if test="condition.businessType != null and condition.businessType != ''">
            AND MSTR_MDM_CD = #{condition.businessType}
        </if>
        <if test="condition.smsReceiveYn != null and condition.smsReceiveYn != ''">
            AND 'Y' = #{condition.smsReceiveYn}
        </if>
        </if>
        
        <if test="condition.recipientType == null or condition.recipientType == 'ALL'">
        UNION ALL
        </if>
        
        <!-- 회원 조회 -->
        <if test="condition.recipientType == null or condition.recipientType == 'ALL' or condition.recipientType == 'MEMBER'">
        SELECT MB_CD                                                                       as recipientId
               ,'MEMBER'                                                                   as recipientType
               ,HANMB_NM                                                                   as name
               ,null                                                                       as representativeName
               ,TRANSLATE(PHON_NUM,'0123456789'||PHON_NUM,'0123456789')                   as phoneNumber1
               ,TRANSLATE(CP_NUM,'0123456789'||CP_NUM,'0123456789')                       as phoneNumber2
               ,NVL(SMS_RECV_YN, 'Y')                                                      as smsReceiveYn
               ,null                                                                       as businessCode
        FROM TMEM_MB
        WHERE 1=1
          AND NVL(LENGTH(DEL_DATE),0) = 0
          AND NVL(LENGTH(DELPRES_ID),0) = 0
        <if test="condition.searchKeyword != null and condition.searchKeyword != ''">
            AND HANMB_NM LIKE '%' || #{condition.searchKeyword} || '%'
        </if>
        <if test="condition.smsReceiveYn != null and condition.smsReceiveYn != ''">
            AND NVL(SMS_RECV_YN, 'Y') = #{condition.smsReceiveYn}
        </if>
        </if>
        
        <choose>
            <when test="condition.sortColumn != null and condition.sortColumn != '' and condition.sortOrder != null and condition.sortOrder != ''">
                ORDER BY ${condition.sortColumn} ${condition.sortOrder}
            </when>
            <otherwise>
                ORDER BY recipientType, name
            </otherwise>
        </choose>
    </select>

    <!-- 발신번호 존재 여부 확인 -->
    <select id="existsSenderNumber" resultType="boolean">
        SELECT CASE WHEN COUNT(*) > 0 THEN 1 ELSE 0 END
        FROM TENV_CODE
        WHERE HIGH_CD = 'SMS_SENDER'
          AND CODE_CD = #{senderType}
          AND CODE_VAL = #{phoneNumber}
          AND USE_YN = 'Y'
    </select>

    <!-- 템플릿 코드 존재 여부 확인 (Mock) -->
    <select id="existsTemplateCode" resultType="boolean">
        SELECT CASE WHEN #{templateCode} IN ('TEST001', 'TEST002') THEN 1 ELSE 0 END
        FROM DUAL
    </select>

    <!-- 선택된 수신자 목록 조회 (SB_1용) -->
    <select id="getSelectedRecipients" resultType="kr.or.komca.admin.sms.dto.query.response.SelectedRecipientList$SelectedRecipientInfo">
        SELECT RECIPIENT_ID                 as recipientId
               ,RECIPIENT_TYPE              as recipientType
               ,RECIPIENT_NAME              as recipientName
               ,PHONE_NUMBER                as phoneNumber
               ,TO_CHAR(INS_DT, 'YYYY-MM-DD HH24:MI:SS') as selectedTime
        FROM TST_SMS_SELECTED_RECIPIENT
        WHERE SESSION_ID = #{sessionId}
          AND USER_ID = #{userId}
        ORDER BY INS_DT DESC
    </select>

</mapper>