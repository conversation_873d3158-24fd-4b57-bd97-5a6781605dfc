<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="kr.or.komca.admin.sms.mapper.query.SmsQueryMapper">

    <!-- 발신번호 목록 조회 -->
    <select id="getSenderNumbers" resultType="kr.or.komca.admin.sms.dto.query.response.SenderNumber">
        SELECT CD
               ,CD_NM
               ,CD_ETC
               ,USE_YN
        FROM TENV_CD
        WHERE PAR_CD = 'SMS_SENDER'
          AND USE_YN = 'Y'
          AND DEL_YN = 'N'
        ORDER BY CD
    </select>

    <!-- 템플릿 목록 조회 (공통코드 기반) -->
    <select id="getTemplates" resultType="kr.or.komca.admin.sms.dto.query.response.TemplateInfo">
        SELECT CD
               ,CD_NM
               ,SUBSTR(CD, 1, INSTR(CD, '_') - 1) category
               ,USE_YN
        FROM TENV_CD
        WHERE PAR_CD = '00457'
          AND USE_YN = 'Y'
          AND DEL_YN = 'N'
        ORDER BY CD
    </select>

    <!-- 템플릿 내용 조회 (DB 기반) -->
    <select id="getTemplateContent" resultType="kr.or.komca.admin.sms.dto.query.response.TemplateContent">
        SELECT t.TMPL_CD
               ,c.CD_NM
               ,t.TMPL_CONTENT
               ,CASE WHEN t.TMPL_VARIABLES IS NOT NULL
                     THEN t.TMPL_VARIABLES
                     ELSE '' END TMPL_VARIABLES
               ,t.CATEGORY
               ,t.USE_YN
        FROM TENV_KAKAO_TEMPLATE t
        INNER JOIN TENV_CD c ON c.CD = t.TMPL_CD AND c.PAR_CD = '00457'
        WHERE t.TMPL_CD = #{templateCode}
          AND t.USE_YN = 'Y'
          AND c.USE_YN = 'Y'
          AND c.DEL_YN = 'N'
    </select>

    <!-- SMS 발송 활성화 플래그 조회 -->
    <select id="getSmsEnabledFlag" resultType="String">
        SELECT FLAG
        FROM TENV_FLAG
        WHERE FLAG_GBN = 'SMS_SEND_ENABLED'
    </select>

    <!-- 카카오 발송 활성화 플래그 조회 -->
    <select id="getKakaoEnabledFlag" resultType="String">
        SELECT FLAG
        FROM TENV_FLAG
        WHERE FLAG_GBN = 'KAKAO_SEND_ENABLED'
    </select>

    <!-- 수신자 검색 (ASIS lev13_s01.java 로직 변환) -->
    <select id="searchRecipients" resultType="kr.or.komca.admin.sms.dto.query.response.RecipientSearchResult">
        <!-- 거래처 조회 -->
        <if test="condition.recipientType == null or condition.recipientType == 'ALL' or condition.recipientType == 'BSCON'">
        SELECT BSCON_CD
               ,'BSCON' RECIPIENT_TYPE
               ,BSCONHAN_NM
               ,REPPRES_NM
               ,TRANSLATE(PHON_NUM,'0123456789'||PHON_NUM,'0123456789') PHONE_NUMBER1
               ,TRANSLATE(PHON_NUM2,'0123456789'||PHON_NUM2,'0123456789') PHONE_NUMBER2
               ,NVL(SMS_RECV_YN, 'Y') SMS_RECEIVE_YN
               ,MSTR_MDM_CD
        FROM TLEV_BSCON
        WHERE 1=1
        <if test="condition.searchKeyword != null and condition.searchKeyword != ''">
            AND (BSCONHAN_NM LIKE '%' || #{condition.searchKeyword} || '%'
                 OR REPPRES_NM LIKE '%' || #{condition.searchKeyword} || '%')
        </if>
        <if test="condition.businessType != null and condition.businessType != ''">
            AND MSTR_MDM_CD = #{condition.businessType}
        </if>
        <if test="condition.smsReceiveYn != null and condition.smsReceiveYn != ''">
            AND NVL(SMS_RECV_YN, 'Y') = #{condition.smsReceiveYn}
        </if>
        </if>

        <if test="condition.recipientType == null or condition.recipientType == 'ALL'">
        UNION ALL
        </if>

        <!-- 회원 조회 -->
        <if test="condition.recipientType == null or condition.recipientType == 'ALL' or condition.recipientType == 'MEMBER'">
        SELECT MB_CD
               ,'MEMBER' RECIPIENT_TYPE
               ,HANMB_NM
               ,null REPPRES_NM
               ,TRANSLATE(PHON_NUM,'0123456789'||PHON_NUM,'0123456789') PHONE_NUMBER1
               ,TRANSLATE(CP_NUM,'0123456789'||CP_NUM,'0123456789') PHONE_NUMBER2
               ,NVL(SMS_RECV_YN, 'Y') SMS_RECEIVE_YN
               ,null MSTR_MDM_CD
        FROM TMEM_MB
        WHERE 1=1
          AND NVL(LENGTH(DEL_DATE),0) = 0
          AND NVL(LENGTH(DELPRES_ID),0) = 0
        <if test="condition.searchKeyword != null and condition.searchKeyword != ''">
            AND HANMB_NM LIKE '%' || #{condition.searchKeyword} || '%'
        </if>
        <if test="condition.smsReceiveYn != null and condition.smsReceiveYn != ''">
            AND NVL(SMS_RECV_YN, 'Y') = #{condition.smsReceiveYn}
        </if>
        </if>

        <choose>
            <when test="condition.sortColumn != null and condition.sortColumn != '' and condition.sortOrder != null and condition.sortOrder != ''">
                ORDER BY ${condition.sortColumn} ${condition.sortOrder}
            </when>
            <otherwise>
                ORDER BY RECIPIENT_TYPE, BSCONHAN_NM
            </otherwise>
        </choose>
    </select>

    <!-- 발신번호 존재 여부 확인 -->
    <select id="existsSenderNumber" resultType="boolean">
        SELECT CASE WHEN COUNT(*) > 0 THEN 1 ELSE 0 END
        FROM TENV_CD
        WHERE PAR_CD = 'SMS_SENDER'
          AND CD = #{senderType}
          AND CD_ETC = #{phoneNumber}
          AND USE_YN = 'Y'
          AND DEL_YN = 'N'
    </select>

    <!-- 템플릿 코드 존재 여부 확인 (Mock) -->
    <select id="existsTemplateCode" resultType="boolean">
        SELECT CASE WHEN #{templateCode} IN ('TEST001', 'TEST002') THEN 1 ELSE 0 END
        FROM DUAL
    </select>

    <!-- 선택된 수신자 목록 조회 (SB_1용) -->
    <select id="getSelectedRecipients" resultType="kr.or.komca.admin.sms.dto.query.response.SelectedRecipientList$SelectedRecipientInfo">
        SELECT RECIPIENT_ID
               ,RECIPIENT_TYPE
               ,RECIPIENT_NAME
               ,PHONE_NUMBER
               ,PHONE_NUMBER_TYPE
               ,TO_CHAR(INS_DT, 'YYYY-MM-DD HH24:MI:SS') SELECTED_TIME
        FROM TST_SMS_SELECTED_RECIPIENT
        WHERE SESSION_ID = #{sessionId}
          AND USER_ID = #{userId}
        ORDER BY INS_DT DESC
    </select>

</mapper>