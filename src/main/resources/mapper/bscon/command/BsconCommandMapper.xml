<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="kr.or.komca.admin.bscon.mapper.command.BsconCommandMapper">

    <!-- 거래처 생성 쿼리 -->
    <insert id="createBscon">
        INSERT INTO TLEV_BSCON (
            BSCON_CD
            ,BSCON_KO_NM
            ,BSCON_EN_NM
            ,BIZ_GBN
            ,BSCON_GBN
            ,REPPERS
            ,INS_NO
            ,BSCDTN_CTENT
            ,BSTYP_CTENT
            ,USE_YN
            ,LARGE_CLASS_CD
            ,AVE_CLASS_CD
            ,SMALL_CLASS_CD
            ,MDM_CD
            ,MST_MDM_CD
            ,REP_YN
            ,GRP_SEQ
            ,ADDR
            ,ADDR_DTL
            ,ADDR_2
            ,ADDR_DTL_2
            ,ZIP
            ,ZIP2
            ,TEL
            ,TEL2
            ,FAX
            ,<PERSON>MAIL
            ,EMAIL_2
            ,UDTKPERS
            ,UDTKPERS_TEL
            ,REMA<PERSON>
            ,MB_ID
            ,ACCN_NO
            ,DPSTR
            ,BANK_CD
            ,FUND_AMT
            ,IFMNT_MNG_OBJ_YN
            ,POST_RETURN_YN
            ,MNG_RATE
            ,CONTR_GBN
            ,CONTR_CCLS_YN
            ,CONTR_TERM_START_DAY
            ,CONTR_TERM_END_DAY
            ,ABR_DPST_CD
            ,ADD_DTL
            ,ADD_DTL2
            ,INS_DT
            ,INSPERS_ID
        ) VALUES (
            #{bsconCd}
            ,#{command.commonInfo.bsconKoNm}
            ,#{command.commonInfo.bsconEnNm}
            ,#{command.commonInfo.bizGbn}
            ,#{command.commonInfo.bsconGbn}
            ,#{command.info.reppers}
            ,#{command.info.insNo}
            ,#{command.commonInfo.bscdtnCtent}
            ,#{command.commonInfo.bstypCtent}
            ,#{command.info.useYn}
            ,#{command.info.largeClassCd}
            ,#{command.info.aveClassCd}
            ,#{command.info.smallClassCd}
            ,#{command.info.mdmCd}
            ,#{command.info.largeClassCd}
            ,'Y'
            ,#{grpSeq}
            ,#{command.info.addr}
            ,#{command.info.addrDtl}
            ,#{command.info.addr2}
            ,#{command.info.addrDtl2}
            ,#{command.info.zip}
            ,#{command.info.zip2}
            ,#{command.info.tel}
            ,#{command.info.tel2}
            ,#{command.info.fax}
            ,#{command.info.email}
            ,#{command.info.email2}
            ,#{command.info.udtkpers}
            ,#{command.info.udtkpersTel}
            ,#{command.info.remak}
            ,#{command.info.mbId}
            ,#{command.info.accnNo}
            ,#{command.info.dpstr}
            ,#{command.info.bankCd}
            ,#{command.info.fundAmt}
            ,#{command.info.ifmntMngObjYn}
            ,#{command.info.postReturnYn}
            ,#{command.info.mngRate}
            ,#{command.info.contrGbn}
            ,#{command.info.contrCclsYn}
            ,#{command.info.contrTermStartDay}
            ,#{command.info.contrTermEndDay}
            ,#{command.info.abrDpstCd}
            ,#{command.info.addDtl}
            ,#{command.info.addDtl2}
            ,SYSDATE
            ,#{inspersId}
        )
    </insert>

    <!-- 거래처 추가 생성 -->
    <insert id="createGroupBscon">
        INSERT INTO TLEV_BSCON (
            BSCON_CD
            ,BSCON_KO_NM
            ,BSCON_EN_NM
            ,BIZ_GBN
            ,BSCON_GBN
            ,REPPERS
            ,INS_NO
            ,BSCDTN_CTENT
            ,BSTYP_CTENT
            ,USE_YN
            ,LARGE_CLASS_CD
            ,AVE_CLASS_CD
            ,SMALL_CLASS_CD
            ,MDM_CD
            ,MST_MDM_CD
            ,REP_YN
            ,GRP_SEQ
            ,ADDR
            ,ADDR_DTL
            ,ADDR_2
            ,ADDR_DTL_2
            ,ZIP
            ,ZIP2
            ,TEL
            ,TEL2
            ,FAX
            ,EMAIL
            ,EMAIL_2
            ,UDTKPERS
            ,UDTKPERS_TEL
            ,REMAK
            ,MB_ID
            ,ACCN_NO
            ,DPSTR
            ,BANK_CD
            ,FUND_AMT
            ,IFMNT_MNG_OBJ_YN
            ,POST_RETURN_YN
            ,MNG_RATE
            ,CONTR_GBN
            ,CONTR_CCLS_YN
            ,CONTR_TERM_START_DAY
            ,CONTR_TERM_END_DAY
            ,ABR_DPST_CD
            ,ADD_DTL
            ,ADD_DTL2
            ,INS_DT
            ,INSPERS_ID
        )
        SELECT
            #{bsconCd}
            ,BSCON_KO_NM
            ,BSCON_EN_NM
            ,BIZ_GBN
            ,BSCON_GBN
            ,#{command.info.reppers}
            ,#{command.info.insNo}
            ,BSCDTN_CTENT
            ,BSTYP_CTENT
            ,#{command.info.useYn}
            ,#{command.info.largeClassCd}
            ,#{command.info.aveClassCd}
            ,#{command.info.smallClassCd}
            ,#{command.info.mdmCd}
            ,#{command.info.largeClassCd}
            ,#{command.info.repYn}
            ,GRP_SEQ
            ,#{command.info.addr}
            ,#{command.info.addrDtl}
            ,#{command.info.addr2}
            ,#{command.info.addrDtl2}
            ,#{command.info.zip}
            ,#{command.info.zip2}
            ,#{command.info.tel}
            ,#{command.info.tel2}
            ,#{command.info.fax}
            ,#{command.info.email}
            ,#{command.info.email2}
            ,#{command.info.udtkpers}
            ,#{command.info.udtkpersTel}
            ,#{command.info.remak}
            ,#{command.info.mbId}
            ,#{command.info.accnNo}
            ,#{command.info.dpstr}
            ,#{command.info.bankCd}
            ,#{command.info.fundAmt}
            ,#{command.info.ifmntMngObjYn}
            ,#{command.info.postReturnYn}
            ,#{command.info.mngRate}
            ,#{command.info.contrGbn}
            ,#{command.info.contrCclsYn}
            ,#{command.info.contrTermStartDay}
            ,#{command.info.contrTermEndDay}
            ,#{command.info.abrDpstCd}
            ,#{command.info.addDtl}
            ,#{command.info.addDtl2}
            ,SYSDATE
            ,#{inspersId}
        FROM TLEV_BSCON
        WHERE BSCON_CD = #{baseBsconCd}         -- 기준이 되는 거래처코드 (대표 거래처)
    </insert>

    <!-- 개별정보 수정 -->
    <update id="updateBscon">
        UPDATE TLEV_BSCON
        SET REPPERS = #{command.reppers}
            ,INS_NO = #{command.insNo}
            ,LARGE_CLASS_CD = #{command.largeClassCd}
            ,AVE_CLASS_CD = #{command.aveClassCd}
            ,SMALL_CLASS_CD = #{command.smallClassCd}
            ,MDM_CD = #{command.mdmCd}
            ,MST_MDM_CD = #{command.largeClassCd}
            ,USE_YN = #{command.useYn}
            ,ADDR = #{command.addr}
            ,ADDR_DTL = #{command.addrDtl}
            ,ADDR_2 = #{command.addr2}
            ,ADDR_DTL_2 = #{command.addrDtl2}
            ,ZIP = #{command.zip}
            ,ZIP2 = #{command.zip2}
            ,TEL = #{command.tel}
            ,TEL2 = #{command.tel2}
            ,FAX = #{command.fax}
            ,EMAIL = #{command.email}
            ,EMAIL_2 = #{command.email2}
            ,UDTKPERS = #{command.udtkpers}
            ,UDTKPERS_TEL = #{command.udtkpersTel}
            ,REMAK = #{command.remak}
            ,MB_ID = #{command.mbId}
            ,ACCN_NO = #{command.accnNo}
            ,DPSTR = #{command.dpstr}
            ,BANK_CD = #{command.bankCd}
            ,FUND_AMT = #{command.fundAmt}
            ,IFMNT_MNG_OBJ_YN = #{command.ifmntMngObjYn}
            ,POST_RETURN_YN = #{command.postReturnYn}
            ,MNG_RATE = #{command.mngRate}
            ,CONTR_GBN = #{command.contrGbn}
            ,CONTR_CCLS_YN = #{command.contrCclsYn}
            ,CONTR_TERM_START_DAY = #{command.contrTermStartDay}
            ,CONTR_TERM_END_DAY = #{command.contrTermEndDay}
            ,ABR_DPST_CD = #{command.abrDpstCd}
            ,ADD_DTL = #{command.addDtl}
            ,ADD_DTL2 = #{command.addDtl2}
            ,REP_YN = #{command.repYn}
            ,MOD_DT = SYSDATE
            ,MODPERS_ID = #{modpersId}
        WHERE BSCON_CD = #{bsconCd}
    </update>

    <!-- 거래처 논리적 삭제 -->
    <update id="deleteBscon">
        UPDATE TLEV_BSCON
        SET DEL_YN = 'Y'
            ,DEL_DT = SYSDATE
            ,DELPERS_ID = #{delpersId}
        WHERE BSCON_CD = #{bsconCd}
        AND DEL_YN = 'N'
    </update>



    <!-- 거래처 코드로 그룹 시퀀스 조회 -->
    <select id="getGroupSequenceByBsconCd" resultType="Long">
        SELECT GRP_SEQ
        FROM TLEV_BSCON
        WHERE BSCON_CD = #{bsconCd}
    </select>

    <!-- 공통정보 일괄 수정 -->
    <update id="updateGroupCommonInfo">
        UPDATE TLEV_BSCON
        SET BSCON_KO_NM = #{commonInfo.bsconKoNm}
            ,BSCON_EN_NM = #{commonInfo.bsconEnNm}
            ,BIZ_GBN = #{commonInfo.bizGbn}
            ,BSCON_GBN = #{commonInfo.bsconGbn}
            ,BSCDTN_CTENT = #{commonInfo.bscdtnCtent}
            ,BSTYP_CTENT = #{commonInfo.bstypCtent}
            ,MOD_DT = SYSDATE
            ,MODPERS_ID = #{modpersId}
        WHERE GRP_SEQ = #{grpSeq}                       -- 연관번호로 같은 그룹 모든 거래처 수정
    </update>

    <!-- 특정 그룹의 대표 거래처 해제 -->
    <update id="clearGroupRepresentative">
        UPDATE TLEV_BSCON
        SET REP_YN = 'N'
            ,MOD_DT = SYSDATE
            ,MODPERS_ID = #{modpersId}
        WHERE GRP_SEQ = #{grpSeq}
        AND REP_YN = 'Y'
    </update>

</mapper>
