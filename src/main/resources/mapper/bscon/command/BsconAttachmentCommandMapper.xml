<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="kr.or.komca.admin.bscon.mapper.command.BsconAttachmentCommandMapper">

    <!-- 거래처 첨부파일 메타데이터 저장 -->
    <insert id="createBsconAttachmentMetadata">
        INSERT INTO TLEV_BSCON_PROP_DOC (
            BSCON_CD           -- 거래처코드
            ,MNG_NO             -- 관리번호
            ,UPLOAD_DOC_GBN     -- 첨부파일종류
            ,ATTCH_FILE_NM      -- 첨부파일명
            ,SVR_FILE_NM        -- 서버파일명 (S3 Key)
            ,SVR_FILE_ROUT      -- 서버파일경로 (S3 Bucket Path)
            ,ATTCH_FILE_CTENT   -- 첨부파일내용 (S3 사용 시 NULL)
            ,UPLOAD_DAY         -- 업로드일시
            ,INS_DT             -- 등록일시
            ,INSPERS_ID         -- 등록자ID
        ) VALUES (
            #{bsconCd}                          -- 거래처코드
            ,(SELECT NVL(MAX(MNG_NO), 0) + 1    -- 관리번호: 해당 거래처의 최대 관리번호 + 1
              FROM TLEV_BSCON_PROP_DOC
              WHERE BSCON_CD = #{bsconCd})
            ,#{uploadDocGbn}                    -- 첨부파일종류
            ,#{fileName}                        -- 첨부파일명
            ,#{svrFileName}                     -- 서버파일명 (파일명만)
            ,#{svrFileRout}                     -- 서버파일경로 (경로 부분)
            ,NULL                               -- 첨부파일내용 (사용하지 않으므로 NULL)
            ,TO_CHAR(SYSDATE, 'yyyymmdd')       -- 업로드일시
            ,SYSDATE                            -- 등록일시
            ,#{inspersId}                       -- 등록자ID
        )
    </insert>

    <!-- 거래처 첨부파일 메타데이터 삭제 -->
    <delete id="deleteBsconAttachmentMetadata">
        DELETE FROM TLEV_BSCON_PROP_DOC
        WHERE BSCON_CD = #{bsconCd}
        AND MNG_NO = #{mngNo}
    </delete>

</mapper>
