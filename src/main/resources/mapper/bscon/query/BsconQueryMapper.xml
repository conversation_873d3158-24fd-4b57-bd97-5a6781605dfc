<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="kr.or.komca.admin.bscon.mapper.query.BsconQueryMapper">

    <!-- 거래처 목록 조회 쿼리 -->
    <select id="getBsconList" resultType="kr.or.komca.admin.bscon.dto.query.response.Bscon">
        SELECT 
            BSCON_CD
            ,BSCON_KO_NM
            ,BSCON_GBN
            ,REPPERS
            ,TEL
            ,EMAIL
            ,FAX
            ,INSPERS_ID
            ,INS_DT
            ,MODPERS_ID
            ,MOD_DT
            ,INS_NO
            ,MST_MDM_CD
        FROM TLEV_BSCON
        <where>
            AND DEL_YN = 'N'
            <if test="bsconKoNm != null and bsconKoNm != ''">
                AND BSCON_KO_NM LIKE '%' || #{bsconKoNm} || '%'
            </if>
            <if test="reppers != null and reppers != ''">
                AND REPPERS LIKE '%' || #{reppers} || '%'
            </if>
            <if test="tel != null and tel != ''">
                AND tel LIKE '%' || #{tel} || '%'
            </if>
            <if test="addr != null and addr != ''">
                AND ADDR LIKE '%' || #{addr} || '%'
            </if>
            <if test="insNo != null and insNo != ''">
                AND INS_NO LIKE '%' || #{insNo} || '%'
            </if>
            <if test="bsconGbn != null and bsconGbn != ''">
                AND BSCON_GBN = #{bsconGbn}
            </if>
            <if test="mstMdmCd != null and mstMdmCd != ''">
                AND MST_MDM_CD = #{mstMdmCd}
            </if>
        </where>
        <!-- 정렬 -->
        <choose>
            <when test="sortColumn != null and sortColumn != ''">
                ORDER BY ${sortColumn} ${sortOrder}
            </when>
            <otherwise>
                ORDER BY BSCON_CD DESC
            </otherwise>
        </choose>
    </select>

    <!-- 대표 거래처 목록 조회 쿼리 -->
    <select id="getRepresentativeBsconList" resultType="kr.or.komca.admin.bscon.dto.query.response.BsconRep">
        SELECT
            BSCON_CD
            ,BSCON_KO_NM
            ,BSCON_GBN
            ,REPPERS
            ,TEL
            ,FAX
            ,INS_NO
            ,EMAIL
            ,INS_DT
            ,GRP_SEQ
            ,REP_YN
            ,LARGE_CLASS_CD
            ,AVE_CLASS_CD
            ,SMALL_CLASS_CD
            ,MDM_CD
        FROM TLEV_BSCON
        <where>
            AND DEL_YN = 'N'
            AND REP_YN = 'Y'
            <if test="bsconKoNm != null and bsconKoNm != ''">
                AND BSCON_KO_NM LIKE '%' || #{bsconKoNm} || '%'
            </if>
            <if test="reppers != null and reppers != ''">
                AND REPPERS LIKE '%' || #{reppers} || '%'
            </if>
            <if test="tel != null and tel != ''">
                AND TEL LIKE '%' || #{tel} || '%'
            </if>
            <if test="insNo != null and insNo != ''">
                AND INS_NO LIKE '%' || #{insNo} || '%'
            </if>
            <if test="bsconGbn != null and bsconGbn != ''">
                AND BSCON_GBN = #{bsconGbn}
            </if>
            <if test="largeClassCd != null and largeClassCd != ''">
                AND LARGE_CLASS_CD = #{largeClassCd}
            </if>
            <if test="aveClassCd != null and aveClassCd != ''">
                AND AVE_CLASS_CD = #{aveClassCd}
            </if>
            <if test="smallClassCd != null and smallClassCd != ''">
                AND SMALL_CLASS_CD = #{smallClassCd}
            </if>
            <if test="mdmCd != null and mdmCd != ''">
                AND MDM_CD = #{mdmCd}
            </if>
        </where>
        <!-- 정렬 -->
        <choose>
            <when test="sortColumn != null and sortColumn != ''">
                ORDER BY ${sortColumn} ${sortOrder}
            </when>
            <otherwise>
                ORDER BY BSCON_CD DESC
            </otherwise>
        </choose>
    </select>

    <!-- 그룹 내 거래처 상세 조회 쿼리 -->
    <select id="getBsconGroupDetail" resultType="kr.or.komca.admin.bscon.dto.query.response.BsconGroupDetail">
        SELECT
            BSCON_CD
            ,BSCON_KO_NM
            ,BSCON_GBN
            ,REPPERS
            ,TEL
            ,FAX
            ,INS_NO
            ,EMAIL
            ,TO_CHAR(INS_DT, 'YYYY-MM-DD HH24:MI:SS') AS INS_DT
            ,GRP_SEQ
            ,REP_YN
            ,LARGE_CLASS_CD
            ,AVE_CLASS_CD
            ,SMALL_CLASS_CD
            ,MDM_CD
        FROM TLEV_BSCON
        WHERE DEL_YN = 'N'
        AND GRP_SEQ = (
            SELECT GRP_SEQ
            FROM TLEV_BSCON
            WHERE BSCON_CD = #{bsconCd}
            AND DEL_YN = 'N'
        )
        ORDER BY REP_YN DESC, BSCON_CD
    </select>

    <!-- 거래처 단일 조회 쿼리 -->
    <select id="getBsconById" resultType="kr.or.komca.admin.bscon.dto.query.response.Bscon">
        SELECT
            BSCON_CD
            ,BSCON_KO_NM
            ,BSCON_GBN
            ,REPPERS
            ,BIZ_GBN
            ,TEL
            ,EMAIL
            ,FAX
            ,INSPERS_ID
            ,INS_DT
            ,MODPERS_ID
            ,MOD_DT
            ,INS_NO
            ,MST_MDM_CD
        FROM TLEV_BSCON
        WHERE BSCON_CD = #{bsconCd}
        AND DEL_YN = 'N'
    </select>

    <!-- 거래처 상세 조회 (모든 정보) -->
    <select id="getBsconDetailById" resultType="kr.or.komca.admin.bscon.dto.query.response.BsconDetail">
        SELECT
            BSCON_CD
            ,BSCON_KO_NM
            ,BSCON_EN_NM
            ,BIZ_GBN
            ,BSCON_GBN
            ,BSCDTN_CTENT
            ,BSTYP_CTENT
            ,REPPERS
            ,INS_NO
            ,LARGE_CLASS_CD
            ,AVE_CLASS_CD
            ,SMALL_CLASS_CD
            ,MDM_CD
            ,MST_MDM_CD
            ,USE_YN
            ,ADDR
            ,ADDR_DTL
            ,ADDR_2
            ,ADDR_DTL_2
            ,ZIP
            ,ZIP2
            ,TEL
            ,TEL2
            ,FAX
            ,EMAIL
            ,EMAIL_2
            ,UDTKPERS
            ,UDTKPERS_TEL
            ,REMAK
            ,MB_ID
            ,ACCN_NO
            ,DPSTR
            ,BANK_CD
            ,FUND_AMT
            ,IFMNT_MNG_OBJ_YN
            ,POST_RETURN_YN
            ,MNG_RATE
            ,CONTR_GBN
            ,CONTR_CCLS_YN
            ,CONTR_TERM_START_DAY
            ,CONTR_TERM_END_DAY
            ,ABR_DPST_CD
            ,ADD_DTL
            ,ADD_DTL2
            ,REP_YN
            ,GRP_SEQ
            ,INSPERS_ID
            ,TO_CHAR(INS_DT, 'YYYY-MM-DD HH24:MI:SS') AS INS_DT
            ,MODPERS_ID
            ,TO_CHAR(MOD_DT, 'YYYY-MM-DD HH24:MI:SS') AS MOD_DT
        FROM TLEV_BSCON
        WHERE BSCON_CD = #{bsconCd}
        AND DEL_YN = 'N'
    </select>

    <!-- 다음 거래처 코드 조회 -->
    <select id="getNextBsconCode" resultType="String">
        SELECT LPAD(TLEV_BSCONCD_SEQ.NEXTVAL, 5, '0')
        FROM DUAL
    </select>

    <!-- 다음 그룹 시퀀스 조회 -->
    <select id="getNextGroupSequence" resultType="Long">
        SELECT TLEV_BSCON_GRP_SEQ.NEXTVAL
        FROM DUAL
    </select>

    <!-- 거래처 코드로 그룹 시퀀스 조회 -->
    <select id="getGroupSequenceByBsconCd" resultType="Long">
        SELECT GRP_SEQ
        FROM TLEV_BSCON
        WHERE BSCON_CD = #{bsconCd}
        AND DEL_YN = 'N'
    </select>

    <!-- 거래처의 현재 대표 여부 확인 -->
    <select id="getCurrentRepYn" resultType="String">
        SELECT REP_YN
        FROM TLEV_BSCON
        WHERE BSCON_CD = #{bsconCd}
        AND DEL_YN = 'N'
    </select>

    <!-- 거래처 존재 여부 확인 -->
    <select id="existsBscon" resultType="boolean">
        SELECT CASE WHEN COUNT(*) > 0 THEN 1 ELSE 0 END
        FROM TLEV_BSCON
        WHERE BSCON_CD = #{bsconCd}
        AND DEL_YN = 'N'
    </select>

    <!-- 그룹 존재 여부 확인 -->
    <select id="existsGroup" resultType="boolean">
        SELECT CASE WHEN COUNT(*) > 0 THEN 1 ELSE 0 END
        FROM TLEV_BSCON
        WHERE GRP_SEQ = #{grpSeq}
        AND DEL_YN = 'N'
    </select>

</mapper>