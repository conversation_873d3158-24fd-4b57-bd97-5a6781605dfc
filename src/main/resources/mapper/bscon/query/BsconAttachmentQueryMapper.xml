<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="kr.or.komca.admin.bscon.mapper.query.BsconAttachmentQueryMapper">

    <!-- 거래처 첨부파일 목록 조회 -->
    <select id="getBsconAttachmentList" resultType="kr.or.komca.admin.bscon.dto.query.response.BsconAttachment">
        SELECT 
            MNG_NO mngNo                                             -- 관리번호
            ,UPLOAD_DOC_GBN uploadDocGbn                             -- 제출서류구분
            ,UPLOAD_DAY uploadDay                                    -- 제출일자
            ,ATTCH_FILE_NM attchFileNm                               -- 첨부파일명
        FROM TLEV_BSCON_PROP_DOC
        WHERE BSCON_CD = #{condition.bsconCd}
        <if test="condition.sortColumn != null and condition.sortColumn != ''">
            ORDER BY ${condition.sortColumn} ${condition.sortOrder}
        </if>
        <if test="condition.sortColumn == null or condition.sortColumn == ''">
            ORDER BY MNG_NO ASC, INS_DT ASC
        </if>
    </select>

    <!-- 거래처 첨부파일 상세 조회 -->
    <select id="getBsconAttachmentDetail" resultType="kr.or.komca.admin.bscon.dto.query.response.BsconAttachmentDetail">
        SELECT 
            BSCON_CD bsconCd                                          -- 거래처코드
            ,MNG_NO mngNo                                             -- 관리번호
            ,UPLOAD_DOC_GBN uploadDocGbn                              -- 첨부파일종류
            ,ATTCH_FILE_NM attchFileNm                                -- 첨부파일명
            ,SVR_FILE_NM svrFileNm                                    -- 서버파일명
            ,SVR_FILE_ROUT svrFileRout                                -- 서버파일경로
            ,ATTCH_FILE_CTENT attchFileCtent                          -- 첨부파일내용 (BLOB)
            ,UPLOAD_DAY uploadDay                                     -- 업로드일시
            ,INS_DT insDt          -- 등록일시
            ,INSPERS_ID inspersId                                     -- 등록자ID
            ,MOD_DT modDt          -- 수정일시
            ,MODPERS_ID modpersId                                     -- 수정자ID
        FROM TLEV_BSCON_PROP_DOC
        WHERE BSCON_CD = #{bsconCd}
          AND MNG_NO = #{mngNo}
    </select>

    <!-- 첨부파일 존재 여부 확인 -->
    <select id="existsBsconAttachment" resultType="boolean">
        SELECT CASE 
            WHEN COUNT(*) > 0 THEN 1 
            ELSE 0 
        END
        FROM TLEV_BSCON_PROP_DOC
        WHERE BSCON_CD = #{bsconCd}
        AND MNG_NO = #{mngNo}
    </select>

    <!-- 파일 키로 첨부파일 상세 조회 -->
    <select id="getBsconAttachmentByFileKey" resultType="kr.or.komca.admin.bscon.dto.query.response.BsconAttachmentDetail">
        SELECT 
            BSCON_CD bsconCd                                          -- 거래처코드
            ,MNG_NO mngNo                                             -- 관리번호
            ,UPLOAD_DOC_GBN uploadDocGbn                              -- 첨부파일종류
            ,ATTCH_FILE_NM attchFileNm                                -- 첨부파일명
            ,SVR_FILE_NM svrFileNm                                    -- 서버파일명
            ,SVR_FILE_ROUT svrFileRout                                -- 서버파일경로
            ,ATTCH_FILE_CTENT attchFileCtent                          -- 첨부파일내용 (BLOB)
            ,UPLOAD_DAY uploadDay                                     -- 업로드일시
            ,INS_DT insDt          -- 등록일시
            ,INSPERS_ID inspersId                                     -- 등록자ID
            ,MOD_DT modDt          -- 수정일시
            ,MODPERS_ID modpersId                                     -- 수정자ID
        FROM TLEV_BSCON_PROP_DOC
        WHERE BSCON_CD = #{bsconCd}
        AND CONCAT(SVR_FILE_ROUT, SVR_FILE_NM) = #{fileKey}
    </select>


</mapper>
