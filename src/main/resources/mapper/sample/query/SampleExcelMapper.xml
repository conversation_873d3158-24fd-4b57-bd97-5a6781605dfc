<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="kr.or.komca.admin.sample.mapper.query.SampleExcelMapper">
    <!-- 기존 검색 조건 재사용 -->
    <sql id="searchCondition">
        <if test="title != null and title != ''">
            AND s.title LIKE '%' || #{title} || '%'      /* 제목 (최대 100자) */
        </if>
        <if test="email != null and email != ''">
            AND s.email = #{email}                       /* 이메일 주소 */
        </if>
        <if test="quantity != null">
            AND s.quantity = #{quantity}                 /* 수량 */
        </if>
        <if test="startDate != null and startDate != ''">
            and s.created_at >= TO_DATE(#{startDate}, 'YYYYMMDD')  /* 생성일시 */
        </if>
        <if test="endDate != null and endDate != ''">
            and TO_DATE(#{endDate}, 'YYYYMMDD') >= s.created_at    /* 생성일시 */
        </if>
        <if test="userNo != null">
            AND s.user_no = #{userNo}                   /* 사용자 번호 */
        </if>
        <if test="userName != null and userName != ''">
            AND u.username LIKE '%' || #{userName} || '%'  /* 사용자 이름 */
        </if>
    </sql>

    <select id="getSampleExcelList"
            parameterType="kr.or.komca.admin.sample.dto.query.condition.SampleListSearch"
            resultType="kr.or.komca.admin.sample.dto.query.response.SampleExcelList">
        /* 검색 조건에 맞는 리스트 조회 */
        SELECT
        s.sample_no,      /* 샘플 번호 (PK) - IDENTITY 컬럼으로 자동 증가 */
        s.title,          /* 제목 (최대 100자) */
        s.quantity,       /* 수량 */
        s.phone_number,   /* 연락처 (형식: 000-0000-0000) */
        s.email,         /* 이메일 주소 */
        s.created_at,    /* 생성일시 - 기본값: SYSTIMESTAMP */
        s.updated_at,     /* 수정일시 - 기본값: SYSTIMESTAMP */
        u.user_no,        /* 사용자 번호 */
        u.username       /* 사용자 이름 */
        FROM t_sample s
        JOIN t_user u ON s.user_no = u.user_no
        WHERE s.del_yn = 'N'  /* 삭제여부 (Y: 삭제, N: 활성) - 기본값: N */
        <include refid="searchCondition"/>
        <if test="sortColumn != null and sortColumn != ''">
            order by ${sortColumn} ${sortOrder}
        </if>
        offset ${(page-1) * pageSize} rows fetch first ${pageSize} rows only
    </select>

    <select id="getSampleExcelCount"
            parameterType="kr.or.komca.admin.sample.dto.query.condition.SampleListSearch"
            resultType="long">
        /* 리스트 카운트 조회 */
        SELECT
        COUNT(*)
        FROM
        t_sample s
        JOIN t_user u ON s.user_no = u.user_no
        WHERE s.del_yn = 'N'  /* 삭제여부 (Y: 삭제, N: 활성) - 기본값: N */
        <include refid="searchCondition"/>
    </select>
</mapper>