<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="kr.or.komca.admin.sample.mapper.command.SampleCommandMapper">

    <insert id="createSample" parameterType="kr.or.komca.admin.sample.dto.command.request.SampleCreateRequest"
            keyProperty="request.sampleNo" keyColumn="sample_no" useGeneratedKeys="true">
        /* 생성 */
        INSERT INTO t_sample (
            title,           /* 제목 (최대 100자) */
            content,         /* 내용 (최대 4000자) */
            quantity,        /* 수량 */
            phone_number,    /* 연락처 (형식: 000-0000-0000) */
            email,          /* 이메일 주소 */
            created_at,     /* 생성일시 - 기본값: SYSTIMESTAMP */
            del_yn,          /* 삭제여부 (Y: 삭제, N: 활성) - 기본값: N */
            user_no         /* 사용자 번호 (FK) */
        ) VALUES (
                     #{request.title},
                     #{request.content},
                     #{request.quantity},
                     #{request.phoneNumber},
                     #{request.email},
                     SYSTIMESTAMP,
                     'N',
                    #{userNo}
        )
    </insert>

    <update id="updateSample" parameterType="kr.or.komca.admin.sample.dto.command.request.SampleUpdateRequest">
        /* 수정 */
        UPDATE t_sample
        SET
            title = #{title},           /* 제목 (최대 100자) */
            content = #{content},       /* 내용 (최대 4000자) */
            quantity = #{quantity},     /* 수량 */
            phone_number = #{phoneNumber}, /* 연락처 (형식: 000-0000-0000) */
            email = #{email},          /* 이메일 주소 */
            updated_at = SYSTIMESTAMP  /* 수정일시 - 기본값: SYSTIMESTAMP */
        WHERE
            sample_no = #{sampleNo}    /* 샘플 번호 (PK) - IDENTITY 컬럼으로 자동 증가 */
          AND del_yn = 'N'            /* 삭제여부 (Y: 삭제, N: 활성) - 기본값: N */
    </update>

    <update id="deleteSample" parameterType="kr.or.komca.admin.sample.dto.command.request.SampleDeleteRequest">
        /* 삭제 */
        UPDATE t_sample
        SET
            del_yn = 'Y',           /* 삭제여부 (Y: 삭제, N: 활성) - 기본값: N */
            updated_at = SYSTIMESTAMP /* 수정일시 - 기본값: SYSTIMESTAMP */
        WHERE
            sample_no = #{sampleNo}  /* 샘플 번호 (PK) - IDENTITY 컬럼으로 자동 증가 */
          AND del_yn = 'N'          /* 삭제여부 (Y: 삭제, N: 활성) - 기본값: N */
    </update>

</mapper>