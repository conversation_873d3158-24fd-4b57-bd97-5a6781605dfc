<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="kr.or.komca.admin.usermanagement.mapper.command.UserManagementCommandMapper">

    <!-- 사용자 정보 수정 쿼리 -->
    <update id="updateUser">
        UPDATE TENV_MB
        SET USER_NM = #{command.userNm}
            ,USER_GBN = #{command.userGbn}
            ,USE_YN = #{command.useYn}
            ,ACCTN_UNIT = #{command.acctnUnit}
            ,IP = #{command.ip}
            ,IPPBX_USER_ID = #{command.ippbxUserId}
            ,IPPBX_INNER_TEL = #{command.ippbxInnerTel}
            ,MOD_DT = SYSDATE
            ,MODPERS_ID = #{modpersId}
        WHERE USER_ID = #{userId}
    </update>

    <!--  사용자 역할 생성 쿼리  -->
    <insert id="createUserRole">
        INSERT ALL
        <foreach collection="command.create" item="roleCd">
            INTO tenv_mb_role (user_id, role_cd, use_yn, inspers_id, ins_dt)
            VALUES (#{userId}, #{roleCd}, 'Y', #{inspersId}, SYSDATE)
        </foreach>
        SELECT * FROM DUAL
    </insert>

    <!--  사용자 역할 삭제 쿼리  -->
    <delete id="deleteUserRole">
        DELETE FROM TENV_MB_ROLE
        WHERE USER_ID = #{userId}
        AND ROLE_CD IN
        <foreach collection="command.delete" item="roleCd" open="(" separator="," close=")">
            #{roleCd}
        </foreach>
    </delete>

    <insert id="createUserTempAuth">
        insert ALL
        <foreach collection="command" item="add">
            INTO tenv_staff_menu (
                USER_ID
                ,MENU_CD
                ,AUTH_SELECT
                ,AUTH_EDIT
                ,AUTH_DOWN
                ,AUTH_MASK
                ,INSPERS_ID
                ,INS_DT
                ,AUTH_START_DAY
                ,AUTH_END_DAY
            )
            VALUES (
                #{userId}
                ,#{add.menuCd}
                ,#{add.authSelect}
                ,#{add.authEdit}
                ,#{add.authDown}
                ,#{add.authMask}
                ,#{inspersId}
                ,SYSDATE
                ,#{add.authStartDay}
                ,#{add.authEndDay}
            )
        </foreach>
        SELECT * FROM DUAL
    </insert>

    <update id="updateUserTempAuth">
        UPDATE tenv_staff_menu
        SET AUTH_SELECT =
                CASE
                    <foreach collection="command" item="update">
                        WHEN MENU_CD = #{update.menuCd} THEN #{update.authSelect}
                    </foreach>
                    ELSE AUTH_SELECT
                END,
            AUTH_EDIT =
                CASE
                    <foreach collection="command" item="update">
                        WHEN MENU_CD = #{update.menuCd} THEN #{update.authEdit}
                    </foreach>
                    ELSE AUTH_EDIT
                END,
            AUTH_DOWN =
                CASE
                    <foreach collection="command" item="update">
                        WHEN MENU_CD = #{update.menuCd} THEN #{update.authDown}
                    </foreach>
                    ELSE AUTH_DOWN
                END,
            AUTH_MASK =
                CASE
                    <foreach collection="command" item="update">
                        WHEN MENU_CD = #{update.menuCd} THEN #{update.authMask}
                    </foreach>
                    ELSE AUTH_MASK
                END,
            MODPERS_ID = #{modpersId}
            ,MOD_DT = SYSDATE
            ,AUTH_START_DAY =
                CASE
                    <foreach collection="command" item="update">
                        WHEN MENU_CD = #{update.menuCd} THEN #{update.authStartDay}
                    </foreach>
                    ELSE AUTH_START_DAY
                END,
            AUTH_END_DAY =
                CASE
                    <foreach collection="command" item="update">
                        WHEN MENU_CD = #{update.menuCd} THEN #{update.authEndDay}
                    </foreach>
                    ELSE AUTH_END_DAY
            END
        WHERE USER_ID = #{userId}
        AND MENU_CD IN (
            <foreach collection="command" item="update" separator=",">
                #{update.menuCd}
            </foreach>
        )
    </update>

    <delete id="deleteUserTempAuth">
        DELETE FROM tenv_staff_menu
        WHERE
            user_id = #{userId}
            AND menu_cd IN
        <foreach collection="command" item="menuCd" open="(" separator="," close=")">
            #{menuCd}
        </foreach>
    </delete>

    <!-- 사용자 매체 코드 권한 추가 -->
    <insert id="createUserMdmAuth">
        insert INTO tenv_user_mdm_auth (
                SEQ
                ,USER_ID
                ,LARGE_CLASS_CD
                ,AVE_CLASS_CD
                ,SMALL_CLASS_CD
                ,MDM_CD
                ,SVC_CD
                ,USE_YN
                ,INSPERS_ID
                ,INS_DT
            )
            VALUES (
                TENV_USER_MDM_AUTH_SEQ.nextval
                ,#{userId}
                ,#{command.largeClassCd}
                ,#{command.aveClassCd}
                ,#{command.smallClassCd}
                ,#{command.mdmCd}
                ,#{command.svcCd}
                ,#{command.useYn}
                ,#{inspersId}
                ,SYSDATE
            )
    </insert>

    <!-- 사용자 매체 코드 권한 수정 -->
    <update id="updateUserMdmAuth">
        UPDATE tenv_user_mdm_auth
        SET LARGE_CLASS_CD =
                CASE
                    <foreach collection="command" item="update">
                        WHEN SEQ = #{update.seq} THEN #{update.largeClassCd}
                    </foreach>
                    ELSE LARGE_CLASS_CD
                END,
            AVE_CLASS_CD =
                CASE
                    <foreach collection="command" item="update">
                        WHEN SEQ = #{update.seq} THEN #{update.aveClassCd}
                    </foreach>
                    ELSE AVE_CLASS_CD
                END,
            SMALL_CLASS_CD =
                CASE
                    <foreach collection="command" item="update">
                        WHEN SEQ = #{update.seq} THEN #{update.smallClassCd}
                    </foreach>
                    ELSE SMALL_CLASS_CD
                END,
            MDM_CD =
                CASE
                    <foreach collection="command" item="update">
                        WHEN SEQ = #{update.seq} THEN #{update.mdmCd}
                    </foreach>
                    ELSE MDM_CD
                END,
            SVC_CD =
                CASE
                    <foreach collection="command" item="update">
                        WHEN SEQ = #{update.seq} THEN #{update.svcCd}
                    </foreach>
                    ELSE SVC_CD
                END,
            USE_YN =
                CASE
                    <foreach collection="command" item="update">
                        WHEN SEQ = #{update.seq} THEN #{update.useYn}
                    </foreach>
                    ELSE USE_YN
                END,
            MODPERS_ID = #{modpersId}
            ,MOD_DT = SYSDATE
        WHERE USER_ID = #{userId}
        AND SEQ IN (
            <foreach collection="command" item="update" separator=",">
                #{update.seq}
            </foreach>
        )
    </update>
</mapper>