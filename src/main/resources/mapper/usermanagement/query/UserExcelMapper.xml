<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="kr.or.komca.admin.usermanagement.mapper.query.UserExcelMapper">

    <!-- 사용자 조회 쿼리 -->
    <select id="getUserExcelList" resultType="kr.or.komca.admin.usermanagement.dto.query.response.UserExcel">
        SELECT
            USER_ID,
            ROLE_CD,
            USER_NM,
            USER_GBN,
            USE_YN,
            STAFF_NO,
            ACCTN_UNIT,
            IP,
            IPPBX_USER_ID,
            IPPBX_INNER_TEL,
            MOD_DT,
            MODPERS_ID
        FROM TENV_MB A
        <where>
            <if test="condition != null">
                <if test="condition.useYn != null and condition.useYn != ''">
                    A.USE_YN = #{condition.useYn}
                </if>
                <if test="condition.userNm != null and condition.userNm!= ''">
                    AND A.USER_NM LIKE '%' || #{condition.userNm} || '%'
                </if>
                <if test="condition.userId != null and condition.userId!= ''">
                    AND A.USER_ID LIKE '%' || #{condition.userId} || '%'
                </if>
                <if test="condition.userGbn != null and condition.userGbn!= ''">
                    AND A.USER_GBN LIKE '%' || #{condition.userGbn} || '%'
                </if>
                <if test="condition.staffNo != null and condition.staffNo!= ''">
                    AND A.STAFF_NO LIKE '%' || #{condition.staffNo} || '%'
                </if>
            </if>
        </where>
        <choose>
            <when test="condition.sortColumn != null and condition.sortOrder != ''">
                ORDER BY ${condition.sortColumn} ${condition.sortOrder}
            </when>
            <otherwise>
                ORDER BY USER_GBN, USER_ID
            </otherwise>
        </choose>

    </select>


</mapper>