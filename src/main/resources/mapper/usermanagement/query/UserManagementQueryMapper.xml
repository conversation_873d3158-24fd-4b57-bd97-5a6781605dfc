<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="kr.or.komca.admin.usermanagement.mapper.query.UserManagementQueryMapper">


    <!--  id로 유저 단일 조회 쿼리  -->
    <select id="getUserById" resultType="kr.or.komca.admin.usermanagement.dto.query.response.UserManagement">
        SELECT
            USER_ID
            ,USER_NM
            ,USER_GBN
            ,USE_YN
            ,STAFF_NO
            ,ACCTN_UNIT
            ,IP
            ,IPPBX_USER_ID
            ,IPPBX_INNER_TEL
            ,MOD_DT
            ,MODPERS_ID
        FROM TENV_MB A
        WHERE
            A.USER_ID = #{userId}
        ORDER BY USER_GBN, USER_ID
    </select>

    <!-- 사용자 조회 쿼리 -->
    <select id="getUserList" resultType="kr.or.komca.admin.usermanagement.dto.query.response.UserManagement">
        SELECT
        USER_ID
        ,USER_NM
        ,USER_GBN
        ,USE_YN
        ,STAFF_NO
        ,ACCTN_UNIT
        ,IP
        ,IPPBX_USER_ID
        ,IPPBX_INNER_TEL
        ,MOD_DT
        ,MODPERS_ID
        FROM TENV_MB A
        <where>
            <if test="condition != null">
                <if test="condition.useYn != null and condition.useYn != ''">
                    A.USE_YN = #{condition.useYn}
                </if>
                <if test="condition.userNm != null and condition.userNm!= ''">
                    AND A.USER_NM LIKE '%' || #{condition.userNm} || '%'
                </if>
                <if test="condition.userId != null and condition.userId!= ''">
                    AND A.USER_ID LIKE '%' || #{condition.userId} || '%'
                </if>
                <if test="condition.userGbn != null and condition.userGbn!= ''">
                    AND A.USER_GBN LIKE '%' || #{condition.userGbn} || '%'
                </if>
                <if test="condition.staffNo != null and condition.staffNo!= ''">
                    AND A.STAFF_NO LIKE '%' || #{condition.staffNo} || '%'
                </if>
            </if>
        </where>
        <choose>
            <when test="condition.sortColumn != null and condition.sortOrder != ''">
                ORDER BY ${condition.sortColumn} ${condition.sortOrder}
            </when>
            <otherwise>
                ORDER BY USER_GBN, USER_ID
            </otherwise>
        </choose>

    </select>

    <!-- 코드 존재 여부 확인 -->
    <select id="isExistUser" resultType="boolean">
        SELECT CASE
            WHEN EXISTS (SELECT 1 FROM TENV_MB WHERE USER_ID = #{userId} AND ROWNUM = 1)
                THEN 1
                ELSE 0
            END
        FROM DUAL
    </select>

    <!-- 사용자의 역할 목록 조회 -->
    <select id="getRoleListByUserId">
        SELECT
            mr.ROLE_CD
            ,tr.ROLE_NM
            ,tr.REMAK
            ,tr.USE_YN
        FROM TENV_MB_ROLE mr
        LEFT JOIN TENV_ROLE tr ON mr.ROLE_CD = tr.ROLE_CD
        WHERE mr.USER_ID = #{userId}
        ORDER BY mr.ROLE_CD
    </select>
    
    <!-- 사용자 ID에 따른 역할 코드 목록 조회 -->
    <select id="getRoleCdListByUserId">
        SELECT 
            mr.ROLE_CD
        FROM 
            TENV_MB_ROLE mr
        INNER JOIN 
            TENV_ROLE tr ON tr.ROLE_CD = mr.ROLE_CD
        WHERE 
            mr.USER_ID = #{userId}
            AND tr.USE_YN = 'Y'
        ORDER BY 
            mr.ROLE_CD
    </select>

    <!--  사용자 ID에 따른 임시 메뉴 권한 목록 조회  -->
    <select id="getTempAuthByUserId">
        SELECT
            tsm.menu_cd
            ,tm.menu_nm
            ,tsm.auth_select
            ,tsm.auth_down
            ,tsm.auth_edit
            ,tsm.auth_mask
            ,tsm.auth_start_day
            ,tsm.auth_end_day
        FROM tenv_staff_menu tsm
        LEFT OUTER JOIN tenv_menu tm ON tm.menu_cd = tsm.menu_cd
        WHERE tsm.user_id = #{userId}
        ORDER BY tsm.menu_cd
    </select>

    <!--  사용자 ID에 따른 매체 코드 권한 목록 조회  -->
    <select id="getMdmAuthByUserId" resultType="kr.or.komca.admin.usermanagement.dto.query.response.UserMdmAuth">
        SELECT
            seq
            ,large_class_cd
            ,ave_class_cd
            ,small_class_cd
            ,mdm_cd
            ,svc_cd
            ,use_yn
            ,inspers_id
            ,ins_dt
            ,modpers_id
            ,mod_dt
        FROM tenv_user_mdm_auth
        WHERE user_id = #{userId}
        <choose>
            <when test="condition.sortColumn != null and condition.sortColumn != ''">
                ORDER BY ${condition.sortColumn} ${condition.sortOrder}
            </when>
            <otherwise>
                ORDER BY ins_dt
            </otherwise>
        </choose>
    </select>

    <!-- 사용자의 특정 메뉴 임시권한 중 존재하지 않는 메뉴 코드 목록 조회 -->
    <select id="getInvalidUserTempAuthList" resultType="string">
        SELECT menu_cd
        FROM (
            <foreach collection="menuCdList" item="menuCd" separator="UNION ALL">
                SELECT #{menuCd} as menu_cd FROM DUAL
            </foreach>
        ) input_menu
        WHERE NOT EXISTS (
            SELECT 1
            FROM tenv_staff_menu tsm
            WHERE tsm.user_id = #{userId}
            AND tsm.menu_cd = input_menu.menu_cd
        )
    </select>

    <!-- 사용자의 특정 메뉴 임시권한 중 이미 존재하는 메뉴 코드 목록 조회 -->
    <select id="getExistingUserTempAuthList" resultType="string">
        SELECT menu_cd
        FROM (
            <foreach collection="menuCdList" item="menuCd" separator="UNION ALL">
                SELECT #{menuCd} as menu_cd FROM DUAL
            </foreach>
        ) input_menu
        WHERE EXISTS (
            SELECT 1
            FROM tenv_staff_menu tsm
            WHERE tsm.user_id = #{userId}
            AND tsm.menu_cd = input_menu.menu_cd
        )
    </select>

    <!-- 사용자의 특정 역할 중 존재하지 않는 역할 코드 목록 조회 -->
    <select id="getInvalidUserRoleList" resultType="string">
        SELECT role_cd
        FROM (
            <foreach collection="roleCdList" item="roleCd" separator="UNION ALL">
                SELECT #{roleCd} as role_cd FROM DUAL
            </foreach>
        ) input_role
        WHERE NOT EXISTS (
            SELECT 1
            FROM tenv_mb_role tmr
            WHERE tmr.user_id = #{userId}
            AND tmr.role_cd = input_role.role_cd
        )
    </select>

    <!-- 사용자의 특정 역할 중 이미 존재하는 역할 코드 목록 조회 -->
    <select id="getExistingUserRoleList" resultType="string">
        SELECT role_cd
        FROM (
            <foreach collection="roleCdList" item="roleCd" separator="UNION ALL">
                SELECT #{roleCd} as role_cd FROM DUAL
            </foreach>
        ) input_role
        WHERE EXISTS (
            SELECT 1
            FROM tenv_mb_role tmr
            WHERE tmr.user_id = #{userId}
            AND tmr.role_cd = input_role.role_cd
        )
    </select>

    <!-- 사용자의 매체 코드 권한 중복 여부 확인 -->
    <select id="existsUserMdmAuth" resultType="boolean">
        SELECT CASE
            WHEN EXISTS (
                SELECT 1
                FROM tenv_user_mdm_auth
                WHERE user_id = #{userId}
                AND large_class_cd = #{largeClassCd}
                AND (
                    (#{aveClassCd} IS NULL AND ave_class_cd IS NULL) OR
                    (#{aveClassCd} IS NOT NULL AND ave_class_cd = #{aveClassCd})
                )
                AND (
                    (#{smallClassCd} IS NULL AND small_class_cd IS NULL) OR
                    (#{smallClassCd} IS NOT NULL AND small_class_cd = #{smallClassCd})
                )
                AND (
                    (#{mdmCd} IS NULL AND mdm_cd IS NULL) OR
                    (#{mdmCd} IS NOT NULL AND mdm_cd = #{mdmCd})
                )
                AND (
                    (#{svcCd} IS NULL AND svc_cd IS NULL) OR
                    (#{svcCd} IS NOT NULL AND svc_cd = #{svcCd})
                )
                AND ROWNUM = 1
            )
                THEN 1
                ELSE 0
            END
        FROM DUAL
    </select>
</mapper>