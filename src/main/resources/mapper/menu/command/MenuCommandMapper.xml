<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="kr.or.komca.admin.menu.mapper.command.MenuCommandMapper">

    <!-- 메뉴 생성 -->
    <insert id="createMenu">
        INSERT INTO TENV_MENU (
            MENU_CD
            ,MENU_NM
            ,MENU_ROUT
            ,PAR_CD
            ,SORT_ORD
            ,USE_YN
            ,REMAK
            ,INS_DT
            ,INSPERS_ID
            ,ROUT_GBN
        ) VALUES (
            #{command.menuCd}
            ,#{command.menuNm}
            ,#{command.menuRout}
            ,#{command.parCd}
            ,#{command.sortOrd}
            ,#{command.useYn}
            ,#{command.remak}
            ,SYSDATE
            ,#{inspersId}
            ,ROUT_GBN
        )
    </insert>


    <!-- 수정 시 sortOrder 컬럼 변경 X -->
    <!-- SORT_ORD = #{command.sortOrd, jdbcType=INTEGER},-->
    <!-- 메뉴 수정 -->
    <update id="updateMenu">
        UPDATE TENV_MENU
        SET
            <if test="command.menuNm != null">
                MENU_NM = #{command.menuNm},
            </if>
            <if test="command.menuRout != null">
                MENU_ROUT = #{command.menuRout},
            </if>
            <if test="command.useYn != null">
                USE_YN = #{command.useYn},
            </if>
            <if test="command.remak != null">
                REMAK = #{command.remak},
            </if>
            <if test="command.routGbn != null">
                ROUT_GBN = #{command.routGbn},
            </if>
            MOD_DT = SYSDATE,                                  -- 최종 수정 날짜 (현재 시각 기준)
            MODPERS_ID = #{modpersId}
        WHERE
            MENU_CD = #{menuCd}
    </update>

    <!-- 메뉴 순서 변경 -->
    <update id="updateMenuOrder">
        UPDATE TENV_MENU
        SET SORT_ORD = #{order.sortOrd}
            ,MOD_DT = SYSDATE
        WHERE MENU_CD = #{order.menuCd}
    </update>
</mapper>