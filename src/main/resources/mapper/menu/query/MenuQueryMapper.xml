<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="kr.or.komca.admin.menu.mapper.query.MenuQueryMapper">

    <resultMap id="menuSummaryResultMap" type="kr.or.komca.admin.menu.dto.query.response.MenuSummary">
        <result property="level" column="MENU_LEVEL"/>
    </resultMap>

    <!--  MenuTree 객체 ResultMap  -->
    <resultMap id="menuTreeResultMap" type="kr.or.komca.admin.menu.dto.query.response.MenuTree">
        <result property="level" column="MENU_LEVEL"/>
    </resultMap>

    <!-- DetailResponse ResultMap (inner class 포함) -->
    <resultMap id="menuDetailResultMap" type="kr.or.komca.admin.menu.dto.query.response.MenuDetail">
        <id property="menuCd" column="MENU_CD"/>
        <result property="menuNm" column="MENU_NM"/>
        <result property="menuRout" column="MENU_ROUT"/>
        <result property="parCd" column="PAR_CD"/>
        <result property="routGbn" column="ROUT_GBN"/>
        <result property="sortOrd" column="SORT_ORD"/>
        <result property="useYn" column="USE_YN"/>
        <result property="level" column="MENU_LEVEL"/>
        <result property="hasChildren" column="HAS_CHILDREN"/>

        <!-- AuditInfo inner class 매핑 -->
        <association property="audit" javaType="kr.or.komca.admin.menu.dto.query.response.MenuDetail$AuditInfo">
            <result property="insDt" column="INS_DT"/>
            <result property="inspersId" column="INSPERS_ID"/>
            <result property="modDt" column="MOD_DT"/>
            <result property="modpersId" column="MODPERS_ID"/>
        </association>

        <!-- 부모 메뉴 정보 -->
        <association property="parent" column="PAR_CD" select="getParentMenuInfo"/>

        <!-- 자식 메뉴 요약 정보 -->
        <association property="childrenSummary" column="MENU_CD" select="getChildrenSummary"/>

        <!-- 경로 정보 -->
        <collection property="path" ofType="string" column="MENU_CD" select="getMenuPathNamesList"/>
    </resultMap>

    <!--  AvailableMenu 객체 ResultMap  -->
    <resultMap id="availableMenuResultMap" type="kr.or.komca.admin.menu.dto.query.response.AvailableMenu">
        <result property="level" column="MENU_LEVEL"/>
    </resultMap>

    <!-- 부모 메뉴 정보 조회 ResultMap (ParentMenu inner class 매핑) -->
    <resultMap id="parentMenuResultMap" type="kr.or.komca.admin.menu.dto.query.response.MenuDetail$ParentMenu">
        <result property="level" column="MENU_LEVEL"/>
    </resultMap>

    <!-- 자식 메뉴 요약 ResultMap (ChildMenuItem + ChildrenSummary inner class 매핑) -->
    <resultMap id="childMenuItemResultMap" type="kr.or.komca.admin.menu.dto.query.response.MenuDetail$ChildMenuItem">
        <id property="menuCd" column="MENU_CD"/>
        <result property="menuNm" column="MENU_NM"/>
        <result property="useYn" column="USE_YN"/>
    </resultMap>

    <!-- ChildrenSummary 객체 ResultMap -->
    <resultMap id="childrenSummaryResultMap" type="kr.or.komca.admin.menu.dto.query.response.MenuDetail$ChildrenSummary">
        <result property="count" column="ITEM_COUNT"/>
        <collection property="items" ofType="kr.or.komca.admin.menu.dto.query.response.MenuDetail$ChildMenuItem"
                    resultMap="childMenuItemResultMap"/>
    </resultMap>

    <!--  MenuPath 객체 ResultMap  -->
    <resultMap id="menuPathResultMap" type="kr.or.komca.admin.menu.dto.query.response.MenuPath">
        <id property="menuCd" column="MENU_CD"/>
        <collection property="path" ofType="kr.or.komca.admin.menu.dto.query.response.MenuPathItem">
            <id property="menuCd" column="PATH_MENU_CD"/>
            <result property="menuNm" column="PATH_MENU_NM"/>
            <result property="level" column="PATH_MENU_LEVEL"/>
        </collection>
    </resultMap>

    <!--  검색 조건  -->
    <sql id="menuConditions">
        <if test="condition != null">
            <if test="condition.useYn != null and condition.useYn != ''">
                AND use_yn = #{condition.useYn}
            </if>
            <if test="condition.menuNm != null and condition.menuNm != ''">
                AND menu_nm LIKE '%' || #{condition.menuNm} || '%'
            </if>
        </if>
    </sql>

    <!-- 메뉴 경로 이름 목록 조회 (문자열 배열로) -->
    <select id="getMenuPathNamesList" resultType="string">
        SELECT MENU_NM
        FROM TENV_MENU
        START WITH MENU_CD = #{menuCd}
        CONNECT BY MENU_CD = PRIOR PAR_CD
        ORDER BY LEVEL DESC
    </select>

    <!-- 부모 메뉴 정보 조회 -->
    <select id="getParentMenuInfo" resultMap="parentMenuResultMap">
        SELECT
            MENU_CD
            ,MENU_NM
            ,(
                SELECT LEVEL - 1
                FROM TENV_MENU
                WHERE MENU_CD = #{parCd}
                START WITH PAR_CD IS NULL
                CONNECT BY PRIOR MENU_CD = PAR_CD
            ) AS MENU_LEVEL
        FROM TENV_MENU
        WHERE MENU_CD = #{parCd}
    </select>

    <!-- 자식 메뉴 요약 정보 -->
    <select id="getChildrenSummary" resultMap="childrenSummaryResultMap">
        SELECT
            COUNT(*) OVER() AS ITEM_COUNT
            ,MENU_CD
            ,MENU_NM
            ,USE_YN
        FROM TENV_MENU
        WHERE PAR_CD = #{menuCd}
        ORDER BY SORT_ORD
    </select>

    <!-- Id로 메뉴 상세 정보 조회 -->
    <select id="getMenuDetailById" resultMap="menuDetailResultMap">
        SELECT
            MENU_CD
            ,MENU_NM
            ,MENU_ROUT
            ,PAR_CD
            ,PAR_GRP
            ,SORT_ORD
            ,USE_YN
            ,REMAK
            ,MENU_IMG_ID
            ,MENU_IMG_LENGTH
            ,INS_DT
            ,INSPERS_ID
            ,MOD_DT
            ,MODPERS_ID
            ,ROUT_GBN
            ,(
                SELECT LEVEL - 1
                FROM TENV_MENU
                WHERE MENU_CD = #{menuCd}
                START WITH PAR_CD IS NULL
                CONNECT BY PRIOR MENU_CD = PAR_CD
            ) AS MENU_LEVEL
            ,CASE WHEN EXISTS (
                SELECT 1
                FROM TENV_MENU
                WHERE PAR_CD = #{menuCd}
            ) THEN 1 ELSE 0 END AS HAS_CHILDREN
        FROM TENV_MENU
        WHERE MENU_CD = #{menuCd}
    </select>

    <!--  상세 메뉴 리스트 조회 (계층 구조 X)  -->
    <select id="getMenuDetailList" resultMap="menuDetailResultMap">
        SELECT
            COUNT(*) OVER() AS TOTAL_COUNT
            ,MENU_CD
            ,MENU_NM
            ,MENU_ROUT
            ,PAR_CD
            ,SORT_ORD
            ,USE_YN
            ,REMAK
            ,MENU_IMG_ID
            ,MENU_IMG_LENGTH
            ,PAR_GRP
            ,INS_DT
            ,INSPERS_ID
            ,MOD_DT
            ,MODPERS_ID
            ,ROUT_GBN
            ,(
                SELECT LEVEL - 1
                FROM TENV_MENU t
                WHERE t.MENU_CD = TENV_MENU.MENU_CD
                START WITH PAR_CD IS NULL
                CONNECT BY PRIOR MENU_CD = PAR_CD
            ) AS MENU_LEVEL
            ,CASE WHEN EXISTS (
                SELECT 1
                FROM TENV_MENU t
                WHERE t.PAR_CD = TENV_MENU.MENU_CD
            ) THEN 1 ELSE 0 END AS HAS_CHILDREN
        FROM TENV_MENU
        <where>
            <include refid="menuConditions"/>
        </where>
        ORDER BY MENU_CD
    </select>

    <!--  하위 메뉴 조회  -->
    <select id="getSubMenuList" resultMap="menuTreeResultMap">
        SELECT
            COUNT(*) OVER() AS TOTAL_COUNT
            ,MENU_CD
            ,MENU_NM
            ,MENU_ROUT
            ,PAR_CD
            ,SORT_ORD
            ,USE_YN
            ,REMAK
            ,MENU_IMG_ID
            ,MENU_IMG_LENGTH
            ,PAR_GRP
            ,ROUT_GBN
            ,(
                SELECT LEVEL - 1
                FROM TENV_MENU t
                WHERE t.MENU_CD = TENV_MENU.MENU_CD
                START WITH PAR_CD IS NULL
                CONNECT BY PRIOR MENU_CD = PAR_CD
            ) AS MENU_LEVEL
        FROM TENV_MENU
        <where>
            PAR_CD = #{menuCd}
            <include refid="menuConditions"/>
        </where>
        ORDER BY SORT_ORD, INS_DT
    </select>

    <!--  메뉴 트리 조회 (서비스에서 계층 구조로 파싱) -->
    <select id="getMenuTree" resultMap="menuTreeResultMap">
        WITH MATCHED_MENUS AS (
            -- 조건에 맞는 메뉴 찾기
            SELECT MENU_CD
            FROM TENV_MENU
            <where>
                <if test="condition != null">
                    <if test="condition.useYn != null and condition.useYn != ''">
                        use_yn = #{condition.useYn}
                    </if>
                    <if test="condition.menuNm != null and condition.menuNm != ''">
                        AND menu_nm LIKE '%' || #{condition.menuNm} || '%'
                    </if>
                </if>
            </where>
        ),
        PARENT_MENUS AS (
            -- 조건에 맞는 메뉴의 상위 계층 찾기
            SELECT DISTINCT P.MENU_CD
            FROM TENV_MENU P
            JOIN (
                SELECT
                    M.MENU_CD
                    ,M.PAR_CD
                    ,LEVEL AS LVL
                FROM TENV_MENU M
                START WITH M.MENU_CD IN (SELECT MENU_CD FROM MATCHED_MENUS)
                CONNECT BY PRIOR M.PAR_CD = M.MENU_CD
                <if test="condition.useYn != null and condition.useYn != ''">
                    AND PRIOR M.use_yn = #{condition.useYn}
                </if>
            ) C ON P.MENU_CD = C.MENU_CD
            <where>
                <if test="condition.useYn != null and condition.useYn != ''">
                    P.use_yn = #{condition.useYn}
                </if>
            </where>
            UNION
            SELECT MENU_CD FROM MATCHED_MENUS
        ),
        HIERARCHICAL_DATA AS (
            -- 최종 계층 구조 구성
            SELECT
                MENU_CD
                ,MENU_NM
                ,ROUT_GBN
                ,MENU_ROUT
                ,PAR_CD
                ,SORT_ORD
                ,USE_YN
                ,REMAK
                ,LEVEL - 1 AS MENU_LEVEL
            FROM TENV_MENU
            WHERE MENU_CD IN (SELECT MENU_CD FROM PARENT_MENUS)
                <if test="condition.useYn != null and condition.useYn != ''">
                    AND use_yn = #{condition.useYn}
                </if>
            START WITH PAR_CD IS NULL
            CONNECT BY PRIOR MENU_CD = PAR_CD
            <if test="condition.useYn != null and condition.useYn != ''">
                AND use_yn = #{condition.useYn}
            </if>
            ORDER SIBLINGS BY SORT_ORD, INS_DT
        )
        SELECT
            MENU_CD
            ,MENU_NM
            ,MENU_ROUT
            ,PAR_CD
            ,SORT_ORD
            ,USE_YN
            ,REMAK
            ,ROUT_GBN
            ,MENU_LEVEL
        FROM HIERARCHICAL_DATA
    </select>

    <!-- 메뉴 경로 조회 -->
    <select id="getMenuPath" resultMap="menuPathResultMap">
        WITH HIERARCHICAL_MENUS AS (
            SELECT
                LEVEL AS LVL
                ,MENU_CD
                ,MENU_NM
                ,(MAX(LEVEL) OVER () - LEVEL) AS MENU_LEVEL
            FROM TENV_MENU
            START WITH MENU_CD = #{menuCd}
            CONNECT BY MENU_CD = PRIOR PAR_CD
            ORDER BY LVL DESC
        )
        SELECT
            #{menuCd} AS MENU_CD
            ,MENU_CD AS PATH_MENU_CD
            ,MENU_NM AS PATH_MENU_NM
            ,MENU_LEVEL AS PATH_MENU_LEVEL
        FROM HIERARCHICAL_MENUS
    </select>

    <!-- 메뉴의 하위 메뉴 개수 조회 -->
    <select id="countChildMenus" resultType="int">
        SELECT COUNT(*)
        FROM TENV_MENU
        WHERE PAR_CD = #{menuCd}
    </select>

    <!-- 메뉴 레벨 조회 -->
    <select id="getMenuLevel" resultType="int">
        SELECT LEVEL - 1 AS MENU_LEVEL
        FROM TENV_MENU
        WHERE MENU_CD = #{menuCd}
        START WITH PAR_CD IS NULL
        CONNECT BY PRIOR MENU_CD = PAR_CD
    </select>

    <!-- 다음 메뉴 코드 조회 -->
    <select id="getNextMenuCd" resultType="string">
        SELECT LPAD(NVL(MAX(TO_NUMBER(MENU_CD)) + 1, 1), 5, '0')
        FROM TENV_MENU
    </select>

    <!-- 코드 존재 여부 확인 -->
    <select id="isExistMenu" resultType="boolean">
        SELECT CASE
            WHEN EXISTS (
                SELECT 1
                FROM TENV_MENU
                WHERE MENU_CD = #{menuCd}
                AND ROWNUM = 1
            ) THEN 1
            ELSE 0
        END
        FROM DUAL
    </select>

    <!-- 상위 메뉴 중 비활성화된 메뉴가 있는지 확인 -->
    <select id="isActive" resultType="boolean">
        SELECT CASE
            WHEN EXISTS (
                SELECT 1
                FROM TENV_MENU
                WHERE MENU_CD IN (
                    SELECT MENU_CD
                    FROM TENV_MENU
                    START WITH MENU_CD = #{menuCd}
                    CONNECT BY MENU_CD = PRIOR PAR_CD
                )
                AND USE_YN = 'N'
            ) THEN 0 -- 비활성화된 메뉴가 하나라도 있으면 0 반환
            ELSE 1 -- 모든 메뉴가 활성화 상태면 1 반환
        END AS IS_ACTIVE
        FROM DUAL
    </select>

    <!-- 메뉴 요약 정보 조회 -->
    <select id="getMenuSummaryById" resultMap="menuSummaryResultMap">
        SELECT
            MENU_CD
            ,MENU_NM
            ,MENU_ROUT
            ,PAR_CD
            ,SORT_ORD
            ,USE_YN
            ,ROUT_GBN
            ,(
                SELECT LEVEL - 1
                FROM TENV_MENU
                WHERE MENU_CD = #{menuCd}
                START WITH PAR_CD IS NULL
                CONNECT BY PRIOR MENU_CD = PAR_CD
            ) AS MENU_LEVEL
        FROM TENV_MENU
        WHERE MENU_CD = #{menuCd}
    </select>

    <!-- 사용자가 조회 권한을 가지면서, 상위 경로가 모두 활성화된 메뉴 반환 -->
    <select id="getAvailableMenuListByUser" resultMap="availableMenuResultMap">
        SELECT
            m.MENU_CD
            ,m.MENU_NM
            ,m.MENU_ROUT
            ,m.PAR_CD
            ,m.SORT_ORD
            ,m.ROUT_GBN
            ,LEVEL - 1 AS MENU_LEVEL
            ,CASE WHEN EXISTS (
                SELECT 1
                FROM TENV_MENU
                WHERE PAR_CD = m.MENU_CD
            ) THEN 1 ELSE 0 END AS HAS_CHILDREN
        FROM TENV_MENU m
        WHERE m.use_yn = 'Y'
          AND EXISTS (
                SELECT 1
                FROM TENV_MB_ROLE mr
                INNER JOIN TENV_ROLE_MENU rm ON mr.ROLE_CD = rm.ROLE_CD
                WHERE mr.USER_ID = #{userId}
                  AND rm.menu_cd = m.MENU_CD
                  AND rm.auth_select = 'Y'
              )
          AND NOT EXISTS (
                -- 상위 경로에 비활성화된 메뉴가 있는지 확인
                SELECT 1
                FROM TENV_MENU parent
                WHERE parent.MENU_CD IN (
                    SELECT MENU_CD
                    FROM TENV_MENU
                    START WITH MENU_CD = m.MENU_CD
                    CONNECT BY MENU_CD = PRIOR PAR_CD
                )
                AND parent.USE_YN = 'N'
              )
        START WITH m.PAR_CD IS NULL
        CONNECT BY PRIOR m.MENU_CD = m.PAR_CD
        ORDER SIBLINGS BY m.SORT_ORD
    </select>

    <!-- 유효하지 않은 menuCd 목록 반환 -->
    <select id="getInvalidMenuCdList" resultType="string">
        SELECT input_cd.menu_cd
        FROM (
            <foreach item="menuCd" collection="menuCdList" separator="UNION ALL">
                SELECT #{menuCd} AS menu_cd FROM DUAL
            </foreach>
        ) input_cd
        WHERE NOT EXISTS (
            SELECT 1
            FROM TENV_MENU m
            WHERE m.MENU_CD = input_cd.menu_cd
        )
    </select>

</mapper>