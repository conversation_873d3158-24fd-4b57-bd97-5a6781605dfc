<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="kr.or.komca.admin.code.retirement.mapper.query.RetirementQueryMapper">

    <!-- 지급공제 목록 조회 쿼리 -->
    <select id="getRetirementList" resultType="kr.or.komca.admin.code.retirement.dto.query.response.Retirement">
        SELECT
            RETIR_ALLO_CD
            ,DUTY_YEARCNT
            ,RETIR_SUR_CNT
            ,SUPP_RATE
            ,RETIR_SURATE1
            ,RETIR_SURATE2
            ,RETIR_SURATE3
            ,RETIR_SURATE4
            ,RETIR_SURATE5
            ,RETIR_SURATE6
            ,RETIR_SURATE7
            ,RETIR_SURATE8
            ,RETIR_SURATE9
            ,RETIR_SURATE10
            ,RETIR_SURATE11
            ,RETIR_SURATE12
            ,<PERSON><PERSON>_YN
        FROM TRET_SURATE
        <where>
            <if test="condition != null ">
                <if test="condition.retirAlloCd != null and condition.retirAlloCd != ''">
                    AND RETIR_ALLO_CD = #{condition.retirAlloCd}
                </if>
            </if>
        </where>
        ORDER BY DUTY_YEARCNT
    </select>
</mapper>
