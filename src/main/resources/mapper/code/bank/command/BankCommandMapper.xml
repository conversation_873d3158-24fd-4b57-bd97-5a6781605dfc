<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="kr.or.komca.admin.code.bank.mapper.command.BankCommandMapper">

    <!-- 은행 생성 쿼리 -->
    <insert id="createBank">
        INSERT INTO TCAC_BANK (BANK_CD, BANK_CD_YN, BANK_NM, REMAK, USE_YN, TRUST_USE_YN, HOMP_USE_YN,
                               INS_DT, INSPERS_ID)
        VALUES ( #{command.bankCd}
               , #{command.bankCdYn}
               , #{command.bankNm}
               , #{command.remak}
               , 'Y'
               , #{command.trustUseYn}
               , #{command.hompUseYn}
               , SYSDATE
               , #{inspersId})
    </insert>

    <!-- 은행 수정 쿼리 -->
    <update id="updateBank">
        UPDATE TCAC_BANK
        SET BANK_CD_YN   = #{command.bankCdYn}
          , BANK_NM      = #{command.bankNm}
          , REMAK        = #{command.remak}
          , TRUST_USE_YN = #{command.trustUseYn}
          , HOMP_USE_YN  = #{command.hompUseYn}
          , MOD_DT       = SYSDATE
          , MODPERS_ID   = #{modpersId}
        WHERE BANK_CD = #{bankCd}
    </update>

    <!-- 은행 삭제 쿼리 -->
    <delete id="deleteBank">
        UPDATE TCAC_BANK
        SET USE_YN = 'N'
        WHERE BANK_CD = #{bankCd}
    </delete>
</mapper>
