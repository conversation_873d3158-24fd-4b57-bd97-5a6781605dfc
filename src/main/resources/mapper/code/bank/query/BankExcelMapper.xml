<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="kr.or.komca.admin.code.bank.mapper.query.BankExcelMapper">

    <!-- 지급공제 목록 조회 쿼리 -->
    <select id="getBankExcelList" resultType="kr.or.komca.admin.code.bank.dto.query.response.BankExcel">
        SELECT
            BANK_CD
            ,BANK_CD_YN
            ,BANK_NM
            ,REMAK
            ,USE_YN
            ,TRUST_USE_YN
            ,HOMP_USE_YN
            ,INSPERS_ID
            ,INS_DT
            ,MODPERS_ID
            ,MOD_DT
        FROM TCAC_BANK
        WHERE USE_YN = 'Y'
        <if test="condition != null ">
            <if test="condition.bankCd != null and condition.bankCd != ''">
                AND BANK_CD = #{condition.bankCd}
            </if>
            <if test="condition.bankNm != null and condition.bankNm != ''">
                AND BANK_NM = #{condition.bankNm}
            </if>
        </if>
        ORDER BY BANK_CD
    </select>
</mapper>
