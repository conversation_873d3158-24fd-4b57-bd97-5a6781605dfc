<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="kr.or.komca.admin.code.common.mapper.query.CommonCodeExcelMapper">

    <!-- 상위 코드 목록 조회 쿼리 -->
    <select id="getParCodeExcelList" resultType="kr.or.komca.admin.code.common.dto.query.response.ParCodeExcel">
        SELECT
            CG.PAR_CD,
            PAR_CD_NM,
            REMAK,
            USE_YN,
            INSPERS_ID,
            INS_DT,
            MODPERS_ID,
            MOD_DT
        FROM TENV_CD_GROUP CG
        LEFT JOIN TENV_CD_GROUP_USER TCGU
        ON TCGU.PAR_CD = CG.PAR_CD
        <where>
            <if test="condition != null ">
                <if test="condition.deptCd != null and condition.deptCd != ''">
                    AND TCGU.dept_cd = #{condition.deptCd}
                </if>
                <if test="condition.useYn != null and condition.useYn != ''">
                    AND USE_YN = #{condition.useYn}
                </if>
                <if test="condition.parCdOrNm != null and condition.parCdOrNm != ''">
                    AND (
                        PAR_CD_NM LIKE '%' || #{condition.parCdOrNm} || '%'
                        OR CG.PAR_CD = #{condition.parCdOrNm}
                    )
                </if>
            </if>
        </where>
        <choose>
            <when test="condition.sortColumn != null and condition.sortOrder != ''">
                ORDER BY CG.${condition.sortColumn} ${condition.sortOrder}
            </when>
            <otherwise>
                ORDER BY CG.PAR_CD
            </otherwise>
        </choose>
    </select>
    
    <!-- 세부 코드 목록 조회 쿼리 -->
    <select id="getDetailCodeExcelList" resultType="kr.or.komca.admin.code.common.dto.query.response.DetailCodeExcel">
        SELECT 
            PAR_CD, 
            CD, 
            CD_NM, 
            CD_ETC, 
            REMAK, 
            SORT_ORD, 
            USE_YN, 
            INSPERS_ID, 
            INS_DT, 
            MODPERS_ID, 
            MOD_DT
        FROM TENV_CD
        WHERE PAR_CD = #{parCd}
        <if test="condition.useYn != null and condition.useYn != ''">
            AND USE_YN = #{condition.useYn}
        </if>
        <choose>
            <when test="condition.sortColumn != null and condition.sortOrder != ''">
                ORDER BY ${condition.sortColumn} ${condition.sortOrder}
            </when>
            <otherwise>
                ORDER BY SORT_ORD, CD
            </otherwise>
        </choose>
    </select>
</mapper>
