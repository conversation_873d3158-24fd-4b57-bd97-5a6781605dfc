<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="kr.or.komca.admin.code.common.mapper.query.CommonCodeQueryMapper">

    <!-- 상위 코드 목록 조회 쿼리 -->
    <select id="getParCodeList" resultType="kr.or.komca.admin.code.common.dto.query.response.ParCode">
        SELECT
            CG.PAR_CD
            ,PAR_CD_NM
            ,REMAK
            ,USE_YN
            ,INSPERS_ID
            ,INS_DT
            ,MODPERS_ID
            ,MOD_DT
        FROM TENV_CD_GROUP CG
        LEFT JOIN TENV_CD_GROUP_USER TCGU
        ON TCGU.PAR_CD = CG.PAR_CD
        <where>
            CG.DEL_YN = 'N'
            <if test="condition != null ">
                <if test="condition.deptCd != null and condition.deptCd != ''">
                    AND TCGU.dept_cd = #{condition.deptCd}
                </if>
                <if test="condition.useYn != null and condition.useYn != ''">
                    AND CG.USE_YN = #{condition.useYn}
                </if>
                <if test="condition.parCd != null and condition.parCd != ''">
                    AND CG.PAR_CD = #{condition.parCd}
                </if>
                <if test="condition.parCdNm != null and condition.parCdNm != ''">
                    AND CG.PAR_CD_NM LIKE '%' || #{condition.parCdNm} || '%'
                </if>
            </if>
        </where>
        <choose>
            <when test="condition.sortColumn != null and condition.sortOrder != ''">
                ORDER BY CG.${condition.sortColumn} ${condition.sortOrder}
            </when>
            <otherwise>
                ORDER BY CG.PAR_CD
            </otherwise>
        </choose>
    </select>

    <!-- id로 상위 코드 조회 쿼리 -->
    <select id="getParCodeById" resultType="kr.or.komca.admin.code.common.dto.query.response.ParCode">
        SELECT
            PAR_CD
            ,PAR_CD_NM
            ,REMAK
            ,USE_YN
            ,INSPERS_ID
            ,INS_DT
            ,MODPERS_ID
            ,MOD_DT
        FROM TENV_CD_GROUP
        WHERE PAR_CD = #{parCd}
        AND DEL_YN = 'N'
    </select>
    
    <!-- 상위 코드 전체 개수 조회 쿼리 -->
    <select id="countParCode" resultType="int">
        SELECT COUNT(*)
        FROM TENV_CD_GROUP
        <where>
            DEL_YN = 'N'
            <if test="condition.useYn != null and condition.useYn != ''">
                AND USE_YN = #{condition.useYn}
            </if>
            <if test="condition != null and condition.parCdOrNm != null and condition.parCdOrNm != ''">
                AND (
                    PAR_CD_NM LIKE '%' || #{condition.parCdOrNm} || '%'
                    OR PAR_CD = #{condition.parCdOrNm}
                )
            </if>
        </where>
    </select>
    
    <!-- 세부 코드 목록 조회 쿼리 -->
    <select id="getDetailCodeList" resultType="kr.or.komca.admin.code.common.dto.query.response.DetailCode">
        SELECT 
            PAR_CD
            ,CD
            ,CD_NM
            ,CD_ETC
            ,REMAK
            ,SORT_ORD
            ,USE_YN
            ,INSPERS_ID
            ,INS_DT
            ,MODPERS_ID
            ,MOD_DT
        FROM TENV_CD
        WHERE PAR_CD = #{parCd}
        AND DEL_YN = 'N'
        <if test="condition.useYn != null and condition.useYn != ''">
            AND USE_YN = #{condition.useYn}
        </if>
        <choose>
            <when test="condition.sortColumn != null and condition.sortOrder != ''">
                ORDER BY ${condition.sortColumn} ${condition.sortOrder}
            </when>
            <otherwise>
                ORDER BY SORT_ORD, CD
            </otherwise>
        </choose>
    </select>

    <!-- id로 세부 코드 조회 쿼리 -->
    <select id="getDetailCodeById" resultType="kr.or.komca.admin.code.common.dto.query.response.DetailCode">
        SELECT
            PAR_CD
            ,CD
            ,CD_NM
            ,CD_ETC
            ,REMAK
            ,SORT_ORD
            ,USE_YN
            ,INSPERS_ID
            ,INS_DT
            ,MODPERS_ID
            ,MOD_DT
        FROM TENV_CD
        WHERE PAR_CD = #{parCd}
        AND CD = #{cd}
        AND DEL_YN = 'N'
    </select>

    <!-- 세부 코드 개수 확인 -->
    <select id="countDetailCode" resultType="int">
        SELECT COUNT(*)
        FROM TENV_CD
        WHERE PAR_CD = #{parCd}
        AND DEL_YN = 'N'
    </select>

    <!-- 다음 상위 코드 조회 -->
    <select id="getNextParCd" resultType="String">
        SELECT LPAD(NVL(MAX(TO_NUMBER(PAR_CD))+1, 1), 5, '0')
        FROM TENV_CD_GROUP
        WHERE DEL_YN = 'N'
    </select>

    <!-- Todo: 숫자만 오는게 아닌 알파벳도 올 수 있음 (직접 입력하도록) 변경 필요 -->
    <!-- 다음 세부 코드 조회 -->
    <select id="getNextDetailCd" resultType="string">
        SELECT LPAD(NVL(MAX(TO_NUMBER(CD))+1, 1), 2, '0')
        FROM TENV_CD
        WHERE PAR_CD = #{parCd}
        AND DEL_YN = 'N'
    </select>

    <!-- 상위 코드 부서 매핑 존재 여부 확인 쿼리 -->
    <select id="existsParCodeGroupUser" resultType="boolean">
        SELECT CASE WHEN COUNT(*) > 0 THEN 1 ELSE 0 END
        FROM TENV_CD_GROUP_USER
        WHERE PAR_CD = #{parCd}
        AND DEPT_CD = #{deptCd}
    </select>
</mapper>
