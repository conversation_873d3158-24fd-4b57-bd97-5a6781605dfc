<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="kr.or.komca.admin.code.common.mapper.command.CommonCodeCommandMapper">

    <!-- 상위 코드 생성 쿼리 -->
    <insert id="createParCode">
        INSERT INTO TENV_CD_GROUP (
            PAR_CD
            ,PAR_CD_NM
            ,REMAK
            ,USE_YN
            ,INSPERS_ID
            ,INS_DT
            ,MODPERS_ID
            ,MOD_DT
        ) VALUES (
            #{parCd}
            ,#{command.parCdNm}
            ,#{command.remak}
            ,#{command.useYn}
            ,#{inspersId}
            ,SYSDATE
            ,null
            ,null
        )
    </insert>
    
    <!-- 상위 코드 수정 쿼리 -->
    <update id="updateParCode">
        UPDATE TENV_CD_GROUP
        SET PAR_CD_NM = #{command.parCdNm}
            ,REMAK = #{command.remak}
            ,USE_YN = #{command.useYn}
            ,MODPERS_ID = #{modpersId}
            ,MOD_DT = SYSDATE
        WHERE PAR_CD = #{parCd}
    </update>
    
    <!-- 상위 코드 삭제 쿼리 (논리삭제) -->
    <update id="deleteParCode">
        UPDATE TENV_CD_GROUP
        SET DEL_YN = 'Y'
            ,DEL_DT = SYSDATE
            ,DELPERS_ID = #{delpersId}
        WHERE PAR_CD = #{parCd}
        AND DEL_YN = 'N'
    </update>

    <!-- 상위 코드 부서 매핑 저장 쿼리 -->
    <insert id="insertParCodeGroupUser">
        INSERT INTO TENV_CD_GROUP_USER (
            PAR_CD
            ,DEPT_CD
        ) VALUES (
            #{parCd}
            ,#{deptCd}
        )
    </insert>
    
    <!-- 세부 코드 생성 쿼리 -->
    <insert id="createDetailCode">
        INSERT INTO TENV_CD (
            PAR_CD
            ,CD
            ,CD_NM
            ,CD_ETC
            ,REMAK
            ,SORT_ORD
            ,USE_YN
            ,INSPERS_ID
            ,INS_DT
            ,MODPERS_ID
            ,MOD_DT
        ) VALUES (
            #{parCd}
            ,#{cd}
            ,#{command.cdNm}
            ,#{command.cdEtc}
            ,#{command.remak}
            ,#{command.sortOrd}
            ,#{command.useYn}
            ,#{inspersId}
            ,SYSDATE
            ,null
            ,null
        )
    </insert>
    
    <!-- 세부 코드 수정 쿼리 -->
    <update id="updateDetailCode">
        UPDATE TENV_CD
        SET CD_NM = #{command.cdNm}
            ,CD_ETC = #{command.cdEtc}
            ,REMAK = #{command.remak}
            ,SORT_ORD = #{command.sortOrd}
            ,USE_YN = #{command.useYn}
            ,MODPERS_ID = #{modpersId}
            ,MOD_DT = SYSDATE
        WHERE PAR_CD = #{parCd}
        AND CD = #{cd}
    </update>
    
    <!-- 세부 코드 삭제 쿼리 (논리삭제) -->
    <update id="deleteDetailCode">
        UPDATE TENV_CD
        SET DEL_YN = 'Y'
            ,DEL_DT = SYSDATE
            ,DELPERS_ID = #{delpersId}
        WHERE PAR_CD = #{parCd}
        AND CD = #{cd}
        AND DEL_YN = 'N'
    </update>
</mapper>
