<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="kr.or.komca.admin.code.account.mapper.query.AccountInfoQueryMapper">
    <!-- 계정 정보 목록 조회 쿼리 -->
    <select id="getAccountInfoList" resultType="kr.or.komca.admin.code.account.dto.query.response.AccountInfo">
        SELECT TA.ACCT_CD
             ,TA.ACCT_NM
             ,TA.PAR_ACCT_CD
             ,TA.PAR_ACCT_SEQ
             ,TA.JOB_GBN
             ,TA.DR_CR_GBN
             ,TA.CROV_YN
             ,TA.ACCTN_GBN_TRUST_YN
             ,TA.ACCTN_GBN_NORMAL_YN
             ,TA.ACCTN_GBN_HALL_YN
             ,TA.ACCTN_GBN_MB_YN
             ,TA.ACCTN_GBN_ELDER_YN
             ,TA.PROV_YN
             ,TA.PROV_AMT_LINK_CD
             ,TA.COMIS_RATE
             ,TA.INS_DT
             ,TA.INSPERS_ID
             ,TA.MOD_DT
             ,TA.MODPERS_ID
             ,TA.USE_YN
             ,TA.ACCT_CLASS
             ,TA.APPRV_YN
             ,TA.BG_YN
             ,TA.READ_ORD
             ,TA.SETT_YN
             ,TA.SLIP_YN
             ,TC.CD_NM AS JOB_GBN_NM
             ,TCD.CD_NM AS DR_CR_GBN_NM
        FROM TCAC_ACCT TA
        LEFT JOIN TENV_CD TC ON (TC.CD = TA.JOB_GBN AND TC.PAR_CD = '00131' AND TC.USE_YN = 'Y') -- 업무 구분
        LEFT JOIN TENV_CD TCD ON (TCD.CD = TA.DR_CR_GBN AND TCD.PAR_CD = '00113' AND TCD.USE_YN = 'Y') -- 차대
        WHERE TA.USE_YN = 'Y'
          <if test="condition != null ">
            <if test="condition.acctnGbn != null and condition.acctnGbn != ''">
              AND (
                (#{condition.acctnGbn} = '001' AND TA.ACCTN_GBN_TRUST_YN = 'Y') OR
                (#{condition.acctnGbn} = '002' AND TA.ACCTN_GBN_NORMAL_YN = 'Y') OR
                (#{condition.acctnGbn} = '003' AND TA.ACCTN_GBN_HALL_YN = 'Y') OR
                (#{condition.acctnGbn} = '004' AND TA.ACCTN_GBN_MB_YN = 'Y') OR
                (#{condition.acctnGbn} = '005' AND TA.ACCTN_GBN_ELDER_YN = 'Y')
                )
            </if>
            <if test="condition.acctNm != null and condition.acctNm != ''">
                AND TA.ACCT_NM = #{condition.acctNm}
            </if>
            <if test="condition.acctCd != null and condition.acctCd != ''">
                AND TA.ACCT_CD = #{condition.acctCd}
            </if>
        </if>
        <![CDATA[
            START
        WITH TA.ACCT_CD = '0000'
        CONNECT BY PRIOR TA.ACCT_CD = TA.PAR_ACCT_CD
        ORDER SIBLINGS BY TA.READ_ORD
        ]]>
    </select>

    <!-- cd로 계정 조회 쿼리 -->
    <select id="getAccountByCd" resultType="kr.or.komca.admin.code.account.dto.query.response.AccountInfo">
        SELECT ACCT_CD
             , ACCT_NM
             , PAR_ACCT_CD
             , PAR_ACCT_SEQ
             , JOB_GBN
             , DR_CR_GBN
             , CROV_YN
             , ACCTN_GBN_TRUST_YN
             , ACCTN_GBN_NORMAL_YN
             , ACCTN_GBN_HALL_YN
             , ACCTN_GBN_MB_YN
             , ACCTN_GBN_ELDER_YN
             , PROV_YN
             , PROV_AMT_LINK_CD
             , COMIS_RATE
             , INS_DT
             , INSPERS_ID
             , MOD_DT
             , MODPERS_ID
             , USE_YN
             , ACCT_CLASS
             , APPRV_YN
             , BG_YN
             , READ_ORD
             , SETT_YN
             , SLIP_YN
        FROM TCAC_ACCT
        WHERE ACCT_CD = #{acctCd}
          AND USE_YN = 'Y'
    </select>
</mapper>
