<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="kr.or.komca.admin.code.account.mapper.query.AccountQueryMapper">

    <!-- 계정 카테고리 목록 조회 쿼리 -->
    <select id="getAccountList" resultType="kr.or.komca.admin.code.account.dto.query.response.Account">
        SELECT A.ACCT_CD
           ,A.LVL
           ,A.SLIP_YN
           ,A.ACCT_NM
        FROM (
                 SELECT ACCT_CD
                        ,ACCT_NM
                        ,LEVEL AS LVL
                        ,PAR_ACCT_CD
                        ,DR_CR_GBN
                        ,SLIP_YN
                        ,JOB_GBN
                        ,ROWNUM AS RNUM
                 FROM TCAC_ACCT
                 WHERE USE_YN = 'Y'
                   AND ACCT_CD != '0000'
                <if test="condition != null ">
                    <if test="condition.acctnGbn != null and condition.acctnGbn != ''">
                       AND (
                            (#{condition.acctnGbn} = '001' AND ACCTN_GBN_TRUST_YN = 'Y') OR
                            (#{condition.acctnGbn} = '002' AND ACCTN_GBN_NORMAL_YN = 'Y') OR
                            (#{condition.acctnGbn} = '003' AND ACCTN_GBN_HALL_YN = 'Y') OR
                            (#{condition.acctnGbn} = '004' AND ACCTN_GBN_MB_YN = 'Y') OR
                            (#{condition.acctnGbn} = '005' AND ACCTN_GBN_ELDER_YN = 'Y') OR
                            (#{condition.acctnGbn} = '%')
                        )
                    </if>
                 </if>
                 START WITH ACCT_CD = '0000'
                 CONNECT BY PRIOR ACCT_CD = PAR_ACCT_CD
                 ORDER SIBLINGS BY READ_ORD
             ) A
                 LEFT JOIN TENV_CD C ON A.DR_CR_GBN = C.CD AND C.PAR_CD = '00113'
                 LEFT JOIN TENV_CD D ON A.JOB_GBN = D.CD AND D.PAR_CD = '00131'
        ORDER BY A.RNUM
    </select>
</mapper>
