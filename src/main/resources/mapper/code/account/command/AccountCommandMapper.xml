<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="kr.or.komca.admin.code.account.mapper.command.AccountCommandMapper">

    <!-- 계정 생성 쿼리 -->
    <insert id="createAccount">
        INSERT INTO TCAC_ACCT ( ACCT_CD
                              , ACCT_NM
                              , PAR_ACCT_CD
                              , JOB_GBN
                              , DR_CR_GBN
                              , CROV_YN
                              , ACCTN_GBN_TRUST_YN
                              , ACCTN_GBN_NORMAL_YN
                              , ACCTN_GBN_HALL_YN
                              , ACCTN_GBN_MB_YN
                              , ACCTN_GBN_ELDER_YN
                              , PROV_YN
                              , PROV_AMT_LINK_CD
                              , INS_DT
                              , INSPERS_ID
                              , USE_YN
                              , APPRV_YN
                              , READ_ORD
                              , SLIP_YN
                              , ACCT_CLASS
                              , BG_YN
                              , SETT_YN)
        VALUES ( #{command.acctCd}
               , #{command.acctNm}
               , #{command.parAcctCd}
               , #{command.jobGbn}
               , #{command.drCrGbn}
               , #{command.crovYn}
               , #{command.acctnGbnTrustYn}
               , #{command.acctnGbnNormalYn}
               , #{command.acctnGbnHallYn}
               , #{command.acctnGbnMbYn}
               , #{command.acctnGbnElderYn}
               , #{command.provYn}
               , #{command.provAmtLinkCd}
               , SYSDATE
               , #{inspersId}
               , 'Y'
               , #{command.apprvYn}
               , #{command.readOrd}
               , #{command.slipYn}
               , '001'
               , 'N'
               , 'N')
    </insert>

    <!-- 계정 수정 쿼리 -->
    <update id="updateAccount">
        UPDATE TCAC_ACCT
        SET ACCT_NM            = #{command.acctNm}
          , PAR_ACCT_CD        = #{command.parAcctCd}
          , JOB_GBN            = #{command.jobGbn}
          , DR_CR_GBN          = #{command.drCrGbn}
          , CROV_YN            = #{command.crovYn}
          , ACCTN_GBN_TRUST_YN = #{command.acctnGbnTrustYn}
          , ACCTN_GBN_NORMAL_YN= #{command.acctnGbnNormalYn}
          , ACCTN_GBN_HALL_YN  = #{command.acctnGbnHallYn}
          , ACCTN_GBN_MB_YN    = #{command.acctnGbnMbYn}
          , ACCTN_GBN_ELDER_YN = #{command.acctnGbnElderYn}
          , PROV_YN            = #{command.provYn}
          , PROV_AMT_LINK_CD   = #{command.provAmtLinkCd}
          , MOD_DT             = SYSDATE
          , MODPERS_ID         = #{modpersId}
          , APPRV_YN           = #{command.apprvYn}
          , READ_ORD           = #{command.readOrd}
          , SLIP_YN            = #{command.slipYn}
        WHERE ACCT_CD = #{acctCd}
    </update>

    <!-- 계정 삭제 쿼리 -->
    <delete id="deleteAccount">
        UPDATE TCAC_ACCT
        SET USE_YN = 'N'
        WHERE ACCT_CD = #{acctCd}
    </delete>
</mapper>
