<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="kr.or.komca.admin.code.mdm.mapper.query.MdmCodeQueryMapper">

    <!-- 매체코드(대분류) 조회 -->
    <select id="getLargeMdmCodeList" resultType="kr.or.komca.admin.code.mdm.dto.query.response.MdmLargeCode">
        SELECT LARGE_CLASS_CD
               ,LARGE_CLASS_NM
               ,USE_YN
               ,SORT_ORD
        FROM TENV_LARGE_CLASS_CD
        WHERE DEL_YN = 'N'
        <choose>
            <when test="condition.sortColumn != null and condition.sortColumn != '' and condition.sortOrder != null and condition.sortOrder != ''">
                ORDER BY ${condition.sortColumn} ${condition.sortOrder}
            </when>
            <otherwise>
                ORDER BY LARGE_CLASS_CD
            </otherwise>
        </choose>
    </select>

    <!-- 매체코드(중분류) 조회 -->
    <select id="getAveMdmCodeList" resultType="kr.or.komca.admin.code.mdm.dto.query.response.MdmAveCode">
        SELECT
            LARGE_CLASS_CD
            ,AVE_CLASS_CD
            ,AVE_CLASS_NM
            ,DOM_ACCT_CD
            ,ABR_ACCT_CD
            ,DOM_BRAN_ACCT_CD
            ,ABR_BRAN_ACCT_CD
            ,USE_YN
            ,SORT_ORD
        FROM TENV_AVE_CLASS_CD
        WHERE
            LARGE_CLASS_CD = #{largeClassCd}
            AND DEL_YN = 'N'
        <choose>
            <when test="condition.sortColumn != null and condition.sortColumn != '' and condition.sortOrder != null and condition.sortOrder != ''">
                ORDER BY ${condition.sortColumn} ${condition.sortOrder}
            </when>
            <otherwise>
                ORDER BY AVE_CLASS_CD
            </otherwise>
        </choose>
    </select>

    <!-- 매체코드(소분류) 조회 -->
    <select id="getSmallMdmCodeList" resultType="kr.or.komca.admin.code.mdm.dto.query.response.MdmSmallCode">
        SELECT
            LARGE_CLASS_CD
            ,AVE_CLASS_CD
            ,SMALL_CLASS_CD
            ,SMALL_CLASS_NM
            ,USE_YN
            ,SORT_ORD
        FROM TENV_SMALL_CLASS_CD
        WHERE
            AVE_CLASS_CD = #{aveClassCd}
            AND DEL_YN = 'N'
        <choose>
            <when test="condition.sortColumn != null and condition.sortColumn != '' and condition.sortOrder != null and condition.sortOrder != ''">
                ORDER BY ${condition.sortColumn} ${condition.sortOrder}
            </when>
            <otherwise>
                ORDER BY SMALL_CLASS_CD
            </otherwise>
        </choose>
    </select>

    <!-- 매체코드(매체) 조회 (사용 수수료율 Join) -->
    <select id="getMdmCodeList" resultType="kr.or.komca.admin.code.mdm.dto.query.response.MdmCode">
        SELECT
            tmc.LARGE_CLASS_CD
            ,lcc.LARGE_CLASS_NM
            ,tmc.AVE_CLASS_CD
            ,acc.AVE_CLASS_NM
            ,tmc.SMALL_CLASS_CD
            ,scc.SMALL_CLASS_NM
            ,tmc.MDM_CD
            ,tmc.MDM_NM
            ,tmc.REMAK
            ,tmc.SORT_ORD
            ,tmc.USE_YN
            ,mco.MNG_COMIS_RATE
            ,tmc.DOM_ACCT_CD
            ,tmc.ABR_ACCT_CD
            ,tmc.DOM_BRAN_ACCT_CD
            ,tmc.ABR_BRAN_ACCT_CD
            ,tmc.ATAX_YN
            ,tmc.INS_DT
            ,tmc.INSPERS_ID
            ,tmc.MOD_DT
            ,tmc.MODPERS_ID
        FROM TENV_MDM_CD tmc
        LEFT JOIN TENV_LARGE_CLASS_CD lcc ON tmc.LARGE_CLASS_CD = lcc.LARGE_CLASS_CD AND lcc.DEL_YN = 'N'
        LEFT JOIN TENV_AVE_CLASS_CD acc ON tmc.AVE_CLASS_CD = acc.AVE_CLASS_CD AND acc.DEL_YN = 'N'
        LEFT JOIN TENV_SMALL_CLASS_CD scc ON tmc.SMALL_CLASS_CD = scc.SMALL_CLASS_CD AND scc.DEL_YN = 'N'
        LEFT JOIN (
            SELECT 
                LARGE_CLASS_CD
                ,AVE_CLASS_CD
                ,SMALL_CLASS_CD
                ,MDM_CD
                ,MNG_COMIS_RATE
            FROM (
                SELECT
                    LARGE_CLASS_CD
                    ,AVE_CLASS_CD
                    ,SMALL_CLASS_CD
                    ,MDM_CD
                    ,MNG_COMIS_RATE
                    ,ROW_NUMBER() OVER (
                        PARTITION BY LARGE_CLASS_CD, AVE_CLASS_CD, SMALL_CLASS_CD, MDM_CD
                        ORDER BY APPL_YRMN DESC
                    ) rn
                FROM TENV_MNG_COMIS
                WHERE APPL_YRMN != '299912'
            )
            WHERE rn = 1
        ) mco ON tmc.LARGE_CLASS_CD = mco.LARGE_CLASS_CD
            AND tmc.AVE_CLASS_CD = mco.AVE_CLASS_CD
            AND tmc.SMALL_CLASS_CD = mco.SMALL_CLASS_CD
            AND tmc.MDM_CD = mco.MDM_CD
        WHERE
            tmc.SMALL_CLASS_CD = #{smallClassCd}
            AND tmc.DEL_YN = 'N'
        <choose>
            <when test="condition.sortColumn != null and condition.sortColumn != '' and condition.sortOrder != null and condition.sortOrder != ''">
                ORDER BY ${condition.sortColumn} ${condition.sortOrder}
            </when>
            <otherwise>
                ORDER BY TO_NUMBER(tmc.SORT_ORD), tmc.MDM_CD
            </otherwise>
        </choose>
    </select>


    <!-- 서비스코드 조회 -->
    <select id="getServiceCodeList" resultType="kr.or.komca.admin.code.mdm.dto.query.response.ServiceCode">
        SELECT
            tsc.LARGE_CLASS_CD
            ,tsc.AVE_CLASS_CD
            ,tsc.SMALL_CLASS_CD
            ,tsc.MDM_CD
            ,tsc.SVC_CD
            ,tsc.SVC_NM
            ,tsc.BSCON_CD
            ,tsc.DISTR_TYPE
            ,tsc.RTAL_RATE
            ,tsc.TUNE_UNCO_AMT
            ,tsc.MNG_RATE
            ,tsc.DEDCT_CFFNT
            ,tsc.ADJ_CFFNT
            ,tsc.IFMNT_ADD_RATE
            ,tsc.DSCT_RATE
            ,tsc.MON_FAMT_AMT
            ,tsc.MIN_AMT
            ,tsc.CALTN_ATEX
            ,tsc.LEVY_PVSN
            ,tsc.ATAX_YN
            ,tsc.USE_YN
            ,tsc.INS_DT
            ,tsc.INSPERS_ID
            ,tsc.MOD_DT
            ,tsc.MODPERS_ID
        FROM TENV_SVC_CD tsc
        WHERE
            tsc.MDM_CD = #{mdmCd}
            AND tsc.DEL_YN = 'N'
        <choose>
            <when test="condition.sortColumn != null and condition.sortColumn != '' and condition.sortOrder != null and condition.sortOrder != ''">
                ORDER BY ${condition.sortColumn} ${condition.sortOrder}
            </when>
            <otherwise>
                ORDER BY tsc.SVC_CD
            </otherwise>
        </choose>
    </select>
    
    <!-- 대분류 코드 존재 여부 확인 -->
    <select id="existsLargeCode" resultType="boolean">
        SELECT CASE
            WHEN EXISTS (SELECT 1 FROM TENV_LARGE_CLASS_CD WHERE LARGE_CLASS_CD = #{largeClassCd} AND DEL_YN = 'N' AND ROWNUM = 1)
                THEN 1
                ELSE 0
            END
        FROM DUAL
    </select>
    
    <!-- 중분류 코드 존재 여부 확인 -->
    <select id="existsAveCode" resultType="boolean">
        SELECT CASE
            WHEN EXISTS (SELECT 1 FROM TENV_AVE_CLASS_CD WHERE AVE_CLASS_CD = #{aveClassCd} AND DEL_YN = 'N' AND ROWNUM = 1)
                THEN 1
                ELSE 0
            END
        FROM DUAL
    </select>
    
    <!-- 소분류 코드 존재 여부 확인 -->
    <select id="existsSmallCode" resultType="boolean">
        SELECT CASE
            WHEN EXISTS (SELECT 1 FROM TENV_SMALL_CLASS_CD WHERE SMALL_CLASS_CD = #{smallClassCd} AND DEL_YN = 'N' AND ROWNUM = 1)
                THEN 1
                ELSE 0
            END
        FROM DUAL
    </select>
    
    <!-- 매체 코드 존재 여부 확인 -->
    <select id="existsMdmCode" resultType="boolean">
        SELECT CASE
            WHEN EXISTS (SELECT 1 FROM TENV_MDM_CD WHERE MDM_CD = #{mdmCd} AND DEL_YN = 'N' AND ROWNUM = 1)
                THEN 1
                ELSE 0
            END
        FROM DUAL
    </select>
    
    <!-- 서비스 코드 존재 여부 확인 -->
    <select id="existsServiceCode" resultType="boolean">
        SELECT CASE
            WHEN EXISTS (SELECT 1 FROM TENV_SVC_CD WHERE SVC_CD = #{svcCd} AND DEL_YN = 'N' AND ROWNUM = 1)
                THEN 1
                ELSE 0
            END
        FROM DUAL
    </select>
    
    <!-- 서비스코드로 매체코드 조회 -->
    <select id="findMdmCdByServiceCode" resultType="string">
        SELECT MDM_CD
        FROM TENV_SVC_CD
        WHERE SVC_CD = #{svcCd}
        AND DEL_YN = 'N'
    </select>

    <!-- 매체코드 단건 조회 -->
    <select id="findMdmCodeByMdmCd" resultType="kr.or.komca.admin.code.mdm.dto.query.response.MdmCode">
        SELECT 
            tmc.LARGE_CLASS_CD
            ,lcc.LARGE_CLASS_NM
            ,tmc.AVE_CLASS_CD
            ,acc.AVE_CLASS_NM
            ,tmc.SMALL_CLASS_CD
            ,scc.SMALL_CLASS_NM
            ,tmc.MDM_CD
            ,tmc.MDM_NM
            ,tmc.REMAK
            ,tmc.SORT_ORD
            ,tmc.USE_YN
            ,mco.MNG_COMIS_RATE
            ,tmc.DOM_ACCT_CD
            ,tmc.ABR_ACCT_CD
            ,tmc.DOM_BRAN_ACCT_CD
            ,tmc.ABR_BRAN_ACCT_CD
            ,tmc.ATAX_YN
            ,tmc.INS_DT
            ,tmc.INSPERS_ID
            ,tmc.MOD_DT
            ,tmc.MODPERS_ID
        FROM TENV_MDM_CD tmc
        LEFT JOIN TENV_LARGE_CLASS_CD lcc ON tmc.LARGE_CLASS_CD = lcc.LARGE_CLASS_CD AND lcc.DEL_YN = 'N'
        LEFT JOIN TENV_AVE_CLASS_CD acc ON tmc.AVE_CLASS_CD = acc.AVE_CLASS_CD AND acc.DEL_YN = 'N'
        LEFT JOIN TENV_SMALL_CLASS_CD scc ON tmc.SMALL_CLASS_CD = scc.SMALL_CLASS_CD AND scc.DEL_YN = 'N'
        LEFT JOIN (
            SELECT 
                LARGE_CLASS_CD
                ,AVE_CLASS_CD
                ,SMALL_CLASS_CD
                ,MDM_CD
                ,MNG_COMIS_RATE
            FROM (
                SELECT
                    LARGE_CLASS_CD
                    ,AVE_CLASS_CD
                    ,SMALL_CLASS_CD
                    ,MDM_CD
                    ,MNG_COMIS_RATE
                    ,ROW_NUMBER() OVER (
                        PARTITION BY LARGE_CLASS_CD, AVE_CLASS_CD, SMALL_CLASS_CD, MDM_CD
                        ORDER BY APPL_YRMN DESC
                    ) rn
                FROM TENV_MNG_COMIS
                WHERE APPL_YRMN != '299912'
            )
            WHERE rn = 1
        ) mco ON tmc.LARGE_CLASS_CD = mco.LARGE_CLASS_CD
            AND tmc.AVE_CLASS_CD = mco.AVE_CLASS_CD
            AND tmc.SMALL_CLASS_CD = mco.SMALL_CLASS_CD
            AND tmc.MDM_CD = mco.MDM_CD
        WHERE tmc.MDM_CD = #{mdmCd}
        AND tmc.DEL_YN = 'N'
    </select>

    <!-- 중분류가 특정 대분류에 속하는지 확인 -->
    <select id="existsAveCodeInLargeClass" resultType="boolean">
        SELECT CASE
            WHEN EXISTS (
                SELECT 1
                FROM TENV_AVE_CLASS_CD
                WHERE LARGE_CLASS_CD = #{largeClassCd}
                AND AVE_CLASS_CD = #{aveClassCd}
                AND DEL_YN = 'N'
                AND ROWNUM = 1
            )
                THEN 1
                ELSE 0
            END
        FROM DUAL
    </select>

    <!-- 소분류가 특정 중분류에 속하는지 확인 -->
    <select id="existsSmallCodeInAveClass" resultType="boolean">
        SELECT CASE
            WHEN EXISTS (
                SELECT 1
                FROM TENV_SMALL_CLASS_CD
                WHERE AVE_CLASS_CD = #{aveClassCd}
                AND SMALL_CLASS_CD = #{smallClassCd}
                AND DEL_YN = 'N'
                AND ROWNUM = 1
            )
                THEN 1
                ELSE 0
            END
        FROM DUAL
    </select>

    <!-- 매체코드가 특정 소분류에 속하는지 확인 -->
    <select id="existsMdmCodeInSmallClass" resultType="boolean">
        SELECT CASE
            WHEN EXISTS (
                SELECT 1
                FROM TENV_MDM_CD
                WHERE SMALL_CLASS_CD = #{smallClassCd}
                AND MDM_CD = #{mdmCd}
                AND DEL_YN = 'N'
                AND ROWNUM = 1
            )
                THEN 1
                ELSE 0
            END
        FROM DUAL
    </select>

    <!-- 서비스코드가 특정 매체코드에 속하는지 확인 -->
    <select id="existsServiceCodeInMdmCode" resultType="boolean">
        SELECT CASE
            WHEN EXISTS (
                SELECT 1
                FROM TENV_SVC_CD
                WHERE MDM_CD = #{mdmCd}
                AND SVC_CD = #{svcCd}
                AND DEL_YN = 'N'
                AND ROWNUM = 1
            )
                THEN 1
                ELSE 0
            END
        FROM DUAL
    </select>
</mapper>
