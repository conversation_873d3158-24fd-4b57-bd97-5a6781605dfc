<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="kr.or.komca.admin.code.mdm.mapper.query.MdmCodeExcelMapper">

    <!-- 서비스코드 엑셀 목록 조회 쿼리 -->
    <select id="getServiceCodeExcelList" resultType="kr.or.komca.admin.code.mdm.dto.query.response.ServiceCodeExcel">
        SELECT
            tsc.LARGE_CLASS_CD
            ,tsc.AVE_CLASS_CD
            ,tsc.SMALL_CLASS_CD
            ,tsc.MDM_CD
            ,tsc.SVC_CD
            ,tsc.SVC_NM
            ,tsc.BSCON_CD
            ,tsc.DISTR_TYPE
            ,tsc.RTAL_RATE
            ,tsc.TUNE_UNCO_AMT
            ,tsc.MNG_RATE
            ,tsc.DEDCT_CFFNT
            ,tsc.ADJ_CFFNT
            ,tsc.IFMNT_ADD_RATE
            ,tsc.DSCT_RATE
            ,tsc.MON_FAMT_AMT
            ,tsc.MIN_AMT
            ,tsc.CALTN_ATEX
            ,tsc.LEVY_PVSN
            ,tsc.ATAX_YN
            ,tsc.USE_YN
            ,TO_CHAR(tsc.INS_DT, 'YYYY-MM-DD HH24:MI:SS')
            ,tsc.INSPERS_ID
            ,TO_CHAR(tsc.MOD_DT, 'YYYY-MM-DD HH24:MI:SS')
            ,tsc.MODPERS_ID
        FROM TENV_SVC_CD tsc
        WHERE
            tsc.MDM_CD = #{mdmCd}
        <choose>
            <when test="condition.sortColumn != null and condition.sortColumn != '' and condition.sortOrder != null and condition.sortOrder != ''">
                ORDER BY ${condition.sortColumn} ${condition.sortOrder}
            </when>
            <otherwise>
                ORDER BY SVC_CD
            </otherwise>
        </choose>
    </select>
</mapper>
