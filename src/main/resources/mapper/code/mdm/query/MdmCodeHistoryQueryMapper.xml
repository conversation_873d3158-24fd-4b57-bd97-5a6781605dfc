<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="kr.or.komca.admin.code.mdm.mapper.query.MdmCodeHistoryQueryMapper">

    <!-- 매체코드 이력 목록 조회 (변경 컬럼 정보 포함 [join]) -->
    <select id="getMdmCodeHistoryList" resultType="kr.or.komca.admin.code.mdm.dto.query.response.MdmCodeHistory">
        SELECT 
            h.TENV_MDM_CD_HIST_SEQ
            ,h.LARGE_CLASS_CD
            ,h.AVE_CLASS_CD
            ,h.SMALL_CLASS_CD
            ,h.MDM_CD
            ,h.MDM_NM
            ,h.REMAK
            ,h.SORT_ORD
            ,h.<PERSON>_<PERSON>
            ,h.<PERSON>OM_ACCT_CD
            ,h.ABR_ACCT_CD
            ,h.DOM_BRAN_ACCT_CD
            ,h.ABR_BRAN_ACCT_CD
            ,h.ATAX_YN
            ,h.MNG_COMIS_RATE
            ,h.INS_DT
            ,h.INSPERS_ID
            ,h.MOD_DT
            ,h.MODPERS_ID
            ,h.APPLY_DT
            ,h.CHG_TYPE_CD
            ,h.APPLY_ID
            ,COALESCE(      -- 변경된 컬럼 목록을 Join하여 ','로 구분된 문자열로 반환 (서비스에서 파싱)
                LISTAGG(c.CHG_COL, ',') WITHIN GROUP (ORDER BY c.CHG_COL),
                ''
            ) changedColumnsString
        FROM TENV_MDM_CD_HIST h
        LEFT JOIN TENV_MDM_CD_COL_HIST c ON h.TENV_MDM_CD_HIST_SEQ = c.TENV_MDM_CD_HIST_SEQ
        <where>
            <if test="condition.mdmCd != null and condition.mdmCd != ''">
                AND h.MDM_CD LIKE '%' || #{condition.mdmCd} || '%'
            </if>
            <if test="condition.mdmNm != null and condition.mdmNm != ''">
                AND h.MDM_NM LIKE '%' || #{condition.mdmNm} || '%'
            </if>
            <if test="condition.chgTypeCd != null and condition.chgTypeCd != ''">
                AND h.CHG_TYPE_CD = #{condition.chgTypeCd}
            </if>
            <if test="condition.startDate != null and condition.startDate != ''">
                AND TO_CHAR(h.APPLY_DT, 'YYYY-MM-DD') &gt;= #{condition.startDate}
            </if>
            <if test="condition.endDate != null and condition.endDate != ''">
                AND TO_CHAR(h.APPLY_DT, 'YYYY-MM-DD') &lt;= #{condition.endDate}
            </if>
            <if test="condition.applyId != null and condition.applyId != ''">
                AND h.APPLY_ID LIKE '%' || #{condition.applyId} || '%'
            </if>
            <if test="condition.smallClassCd != null and condition.smallClassCd != ''">
                AND h.SMALL_CLASS_CD = #{condition.smallClassCd}
            </if>
        </where>
        GROUP BY 
            h.TENV_MDM_CD_HIST_SEQ
            ,h.LARGE_CLASS_CD
            ,h.AVE_CLASS_CD
            ,h.SMALL_CLASS_CD
            ,h.MDM_CD
            ,h.MDM_NM
            ,h.REMAK
            ,h.SORT_ORD
            ,h.USE_YN
            ,h.DOM_ACCT_CD
            ,h.ABR_ACCT_CD
            ,h.DOM_BRAN_ACCT_CD
            ,h.ABR_BRAN_ACCT_CD
            ,h.ATAX_YN
            ,h.MNG_COMIS_RATE
            ,h.INS_DT
            ,h.INSPERS_ID
            ,h.MOD_DT
            ,h.MODPERS_ID
            ,h.APPLY_DT
            ,h.CHG_TYPE_CD
            ,h.APPLY_ID
        ORDER BY 
        <choose>
            <when test="condition.sortColumn != null and condition.sortColumn != ''">
                h.${condition.sortColumn} ${condition.sortOrder}
                ,h.TENV_MDM_CD_HIST_SEQ ${condition.sortOrder}
            </when>
            <otherwise>
                h.APPLY_DT DESC
                ,h.TENV_MDM_CD_HIST_SEQ DESC
            </otherwise>
        </choose>
    </select>


</mapper>