<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="kr.or.komca.admin.code.mdm.mapper.command.MdmCodeCommandMapper">

    <!-- 대분류 코드 생성 -->
    <insert id="createLargeCode">
        INSERT INTO TENV_LARGE_CLASS_CD (
            LARGE_CLASS_CD
            ,LARGE_CLASS_NM
            ,USE_YN
            ,SORT_ORD
        ) VALUES (
            #{command.largeClassCd}
            ,#{command.largeClassNm}
            ,#{command.useYn}
            ,#{command.sortOrd}
        )
    </insert>

    <!-- 대분류 코드 수정 -->
    <update id="updateLargeCode">
        UPDATE TENV_LARGE_CLASS_CD
        SET 
            LARGE_CLASS_NM = #{command.largeClassNm}
            ,USE_YN = #{command.useYn}
            ,SORT_ORD = #{command.sortOrd}
        WHERE LARGE_CLASS_CD = #{command.largeClassCd}
    </update>

    <!-- 대분류 코드 논리적 삭제 -->
    <update id="deleteLargeCode">
        UPDATE TENV_LARGE_CLASS_CD
        SET DEL_YN = 'Y'
            ,DEL_DT = SYSDATE
            ,DELPERS_ID = #{delpersId}
        WHERE LARGE_CLASS_CD = #{largeClassCd}
        AND DEL_YN = 'N'
    </update>

    <!-- 중분류 코드 생성 -->
    <insert id="createAveCode">
        INSERT INTO TENV_AVE_CLASS_CD (
            LARGE_CLASS_CD
            ,AVE_CLASS_CD
            ,AVE_CLASS_NM
            ,DOM_ACCT_CD
            ,ABR_ACCT_CD
            ,DOM_BRAN_ACCT_CD
            ,ABR_BRAN_ACCT_CD
            ,USE_YN
            ,SORT_ORD
        ) VALUES (
            #{largeClassCd}
            ,#{command.aveClassCd}
            ,#{command.aveClassNm}
            ,#{command.domAcctCd}
            ,#{command.abrAcctCd}
            ,#{command.domBranAcctCd}
            ,#{command.abrBranAcctCd}
            ,#{command.useYn}
            ,#{command.sortOrd}
        )
    </insert>

    <!-- 중분류 코드 수정 -->
    <update id="updateAveCode">
        UPDATE TENV_AVE_CLASS_CD
        SET 
            AVE_CLASS_NM = #{command.aveClassNm}
            ,DOM_ACCT_CD = #{command.domAcctCd}
            ,ABR_ACCT_CD = #{command.abrAcctCd}
            ,DOM_BRAN_ACCT_CD = #{command.domBranAcctCd}
            ,ABR_BRAN_ACCT_CD = #{command.abrBranAcctCd}
            ,USE_YN = #{command.useYn}
            ,SORT_ORD = #{command.sortOrd}
        WHERE AVE_CLASS_CD = #{aveClassCd}
    </update>

    <!-- 중분류 코드 논리적 삭제 -->
    <update id="deleteAveCode">
        UPDATE TENV_AVE_CLASS_CD
        SET DEL_YN = 'Y'
            ,DEL_DT = SYSDATE
            ,DELPERS_ID = #{delpersId}
        WHERE AVE_CLASS_CD = #{aveClassCd}
        AND DEL_YN = 'N'
    </update>

    <!-- 소분류 코드 생성 -->
    <insert id="createSmallCode">
        INSERT INTO TENV_SMALL_CLASS_CD (
            LARGE_CLASS_CD
            ,AVE_CLASS_CD
            ,SMALL_CLASS_CD
            ,SMALL_CLASS_NM
            ,USE_YN
            ,SORT_ORD
        ) VALUES (
            #{largeClassCd}
            ,#{aveClassCd}
            ,#{command.smallClassCd}
            ,#{command.smallClassNm}
            ,#{command.useYn}
            ,#{command.sortOrd}
        )
    </insert>

    <!-- 소분류 코드 수정 -->
    <update id="updateSmallCode">
        UPDATE TENV_SMALL_CLASS_CD
        SET 
            SMALL_CLASS_NM = #{command.smallClassNm}
            ,USE_YN = #{command.useYn}
            ,SORT_ORD = #{command.sortOrd}
        WHERE SMALL_CLASS_CD = #{smallClassCd}
    </update>

    <!-- 소분류 코드 논리적 삭제 -->
    <update id="deleteSmallCode">
        UPDATE TENV_SMALL_CLASS_CD
        SET DEL_YN = 'Y'
            ,DEL_DT = SYSDATE
            ,DELPERS_ID = #{delpersId}
        WHERE SMALL_CLASS_CD = #{smallClassCd}
        AND DEL_YN = 'N'
    </update>

    <!-- 매체 코드 생성 -->
    <insert id="createMdmCode">
        INSERT INTO TENV_MDM_CD (
            LARGE_CLASS_CD
            ,LARGE_CLASS_NM
            ,AVE_CLASS_CD
            ,AVE_CLASS_NM
            ,SMALL_CLASS_CD
            ,SMALL_CLASS_NM
            ,MDM_CD
            ,MDM_NM
            ,DOM_ACCT_CD
            ,ABR_ACCT_CD
            ,DOM_BRAN_ACCT_CD
            ,ABR_BRAN_ACCT_CD
            ,ATAX_YN
            ,USE_YN
            ,SORT_ORD
            ,INSPERS_ID
            ,INS_DT
        )
        SELECT 
            #{largeClassCd}
            ,lcc.LARGE_CLASS_NM
            ,#{aveClassCd}
            ,acc.AVE_CLASS_NM
            ,#{smallClassCd}
            ,scc.SMALL_CLASS_NM
            ,#{command.mdmCd}
            ,#{command.mdmNm}
            ,#{command.domAcctCd}
            ,#{command.abrAcctCd}
            ,#{command.domBranAcctCd}
            ,#{command.abrBranAcctCd}
            ,#{command.ataxYn}
            ,#{command.useYn}
            ,#{command.sortOrd}
            ,#{inspersId}
            ,SYSDATE
        FROM DUAL
        LEFT JOIN TENV_LARGE_CLASS_CD lcc ON lcc.LARGE_CLASS_CD = #{largeClassCd} AND lcc.DEL_YN = 'N'
        LEFT JOIN TENV_AVE_CLASS_CD acc ON acc.AVE_CLASS_CD = #{aveClassCd} AND acc.DEL_YN = 'N'
        LEFT JOIN TENV_SMALL_CLASS_CD scc ON scc.SMALL_CLASS_CD = #{smallClassCd} AND scc.DEL_YN = 'N'
    </insert>

    <!-- 매체 코드 수정 -->
    <update id="updateMdmCode">
        UPDATE TENV_MDM_CD
        SET 
            MDM_NM = #{command.mdmNm}
            ,REMAK = #{command.remak}
            ,DOM_ACCT_CD = #{command.domAcctCd}
            ,ABR_ACCT_CD = #{command.abrAcctCd}
            ,DOM_BRAN_ACCT_CD = #{command.domBranAcctCd}
            ,ABR_BRAN_ACCT_CD = #{command.abrBranAcctCd}
            ,ATAX_YN = #{command.ataxYn}
            ,USE_YN = #{command.useYn}
            ,SORT_ORD = #{command.sortOrd}
            ,MODPERS_ID = #{modpersId}
            ,MOD_DT = SYSDATE
        WHERE MDM_CD = #{mdmCd}
    </update>

    <!-- 매체 코드 논리적 삭제 -->
    <update id="deleteMdmCode">
        UPDATE TENV_MDM_CD
        SET DEL_YN = 'Y'
            ,DEL_DT = SYSDATE
            ,DELPERS_ID = #{delpersId}
        WHERE MDM_CD = #{mdmCd}
        AND DEL_YN = 'N'
    </update>

    <!-- 서비스 코드 생성 -->
    <insert id="createServiceCode">
        INSERT INTO TENV_SVC_CD (
            LARGE_CLASS_CD
            ,AVE_CLASS_CD
            ,SMALL_CLASS_CD
            ,MDM_CD
            ,SVC_CD
            ,SVC_NM
            ,BSCON_CD
            ,DISTR_TYPE
            ,RTAL_RATE
            ,TUNE_UNCO_AMT
            ,MNG_RATE
            ,DEDCT_CFFNT
            ,ADJ_CFFNT
            ,IFMNT_ADD_RATE
            ,DSCT_RATE
            ,MON_FAMT_AMT
            ,MIN_AMT
            ,CALTN_ATEX
            ,LEVY_PVSN
            ,ATAX_YN
            ,USE_YN
            ,INSPERS_ID
            ,INS_DT
        ) VALUES (
            #{largeClassCd}
            ,#{aveClassCd}
            ,#{smallClassCd}
            ,#{mdmCd}
            ,#{command.svcCd}
            ,#{command.svcNm}
            ,#{command.bsconCd}
            ,#{command.distrType}
            ,#{command.rtalRate}
            ,#{command.tuneUncoAmt}
            ,#{command.mngRate}
            ,#{command.dedctCffnt}
            ,#{command.adjCffnt}
            ,#{command.ifmntAddRate}
            ,#{command.dsctRate}
            ,#{command.monFamtAmt}
            ,#{command.minAmt}
            ,#{command.caltnAtex}
            ,#{command.levyPvsn}
            ,#{command.ataxYn}
            ,#{command.useYn}
            ,#{inspersId}
            ,SYSDATE
        )
    </insert>

    <!-- 서비스 코드 수정 -->
    <update id="updateServiceCode">
        UPDATE TENV_SVC_CD
        SET 
            SVC_NM = #{command.svcNm}
            ,BSCON_CD = #{command.bsconCd}
            ,DISTR_TYPE = #{command.distrType}
            ,RTAL_RATE = #{command.rtalRate}
            ,TUNE_UNCO_AMT = #{command.tuneUncoAmt}
            ,MNG_RATE = #{command.mngRate}
            ,DEDCT_CFFNT = #{command.dedctCffnt}
            ,ADJ_CFFNT = #{command.adjCffnt}
            ,IFMNT_ADD_RATE = #{command.ifmntAddRate}
            ,DSCT_RATE = #{command.dsctRate}
            ,MON_FAMT_AMT = #{command.monFamtAmt}
            ,MIN_AMT = #{command.minAmt}
            ,CALTN_ATEX = #{command.caltnAtex}
            ,LEVY_PVSN = #{command.levyPvsn}
            ,ATAX_YN = #{command.ataxYn}
            ,USE_YN = #{command.useYn}
            ,MODPERS_ID = #{modpersId}
            ,MOD_DT = SYSDATE
        WHERE SVC_CD = #{svcCd}
    </update>

    <!-- 서비스 코드 논리적 삭제 -->
    <update id="deleteServiceCode">
        UPDATE TENV_SVC_CD
        SET DEL_YN = 'Y'
            ,DEL_DT = SYSDATE
            ,DELPERS_ID = #{delpersId}
        WHERE SVC_CD = #{svcCd}
        AND DEL_YN = 'N'
    </update>
    
    <!-- 서비스코드 일괄 업데이트 -->
    <update id="batchUpdateServiceCodeByMdmCd">
        UPDATE TENV_SVC_CD target
        SET
            (
                target.BSCON_CD
                ,target.DISTR_TYPE
                ,target.RTAL_RATE
                ,target.TUNE_UNCO_AMT
                ,target.MNG_RATE
                ,target.DEDCT_CFFNT
                ,target.ADJ_CFFNT
                ,target.IFMNT_ADD_RATE
                ,target.DSCT_RATE
                ,target.MON_FAMT_AMT
                ,target.MIN_AMT
                ,target.CALTN_ATEX
                ,target.LEVY_PVSN
                ,target.ATAX_YN
                ,target.MODPERS_ID
                ,target.MOD_DT
            )
            =
            (
                SELECT
                    base.BSCON_CD
                    ,base.DISTR_TYPE
                    ,base.RTAL_RATE
                    ,base.TUNE_UNCO_AMT
                    ,base.MNG_RATE
                    ,base.DEDCT_CFFNT
                    ,base.ADJ_CFFNT
                    ,base.IFMNT_ADD_RATE
                    ,base.DSCT_RATE
                    ,base.MON_FAMT_AMT
                    ,base.MIN_AMT
                    ,base.CALTN_ATEX
                    ,base.LEVY_PVSN
                    ,base.ATAX_YN
                    ,#{modpersId}
                    ,SYSDATE
                FROM TENV_SVC_CD base
                WHERE base.SVC_CD = #{baseSvcCd}
            )
        WHERE target.MDM_CD = #{mdmCd}
        AND target.SVC_CD != #{baseSvcCd}
    </update>


</mapper>