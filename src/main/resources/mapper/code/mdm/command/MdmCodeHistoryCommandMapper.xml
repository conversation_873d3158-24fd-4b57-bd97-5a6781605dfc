<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="kr.or.komca.admin.code.mdm.mapper.command.MdmCodeHistoryCommandMapper">

    <!-- 매체코드 이력 저장 (현재 값 전체 저장) -->
    <insert id="createMdmCodeHistory">
        INSERT INTO TENV_MDM_CD_HIST (
            TENV_MDM_CD_HIST_SEQ
            ,LARGE_CLASS_CD
            ,AVE_CLASS_CD
            ,SMALL_CLASS_CD
            ,MDM_CD
            ,MDM_NM
            ,REMAK
            ,SORT_ORD
            ,USE_YN
            ,DOM_ACCT_CD
            ,ABR_ACCT_CD
            ,DOM_BRAN_ACCT_CD
            ,ABR_BRAN_ACCT_CD
            ,ATAX_YN
            ,CHG_TYPE_CD
            ,APPLY_DT
            ,APPLY_ID
            ,INS_DT
            ,INSPERS_ID
            ,MOD_DT
            ,MODPERS_ID
            ,MNG_COMIS_RATE
        ) VALUES (
            #{histSeq}
            ,#{historyData.largeClassCd}
            ,#{historyData.aveClassCd}
            ,#{historyData.smallClassCd}
            ,#{historyData.mdmCd}
            ,#{historyData.mdmNm}
            ,#{historyData.remak}
            ,#{historyData.sortOrd}
            ,#{historyData.useYn}
            ,#{historyData.domAcctCd}
            ,#{historyData.abrAcctCd}
            ,#{historyData.domBranAcctCd}
            ,#{historyData.abrBranAcctCd}
            ,#{historyData.ataxYn}
            ,#{chgTypeCd}
            ,SYSDATE
            ,#{applyId}
            ,#{historyData.insDt}
            ,#{historyData.inspersId}
            ,#{historyData.modDt}
            ,#{historyData.modpersId}
            ,#{historyData.mngComisRate}
        )
    </insert>

    <!-- 매체코드 컬럼 이력 저장 -->
    <insert id="createMdmCodeColumnHistory">
        INSERT INTO TENV_MDM_CD_COL_HIST (
            TENV_MDM_CD_COL_HIST_SEQ
            ,TENV_MDM_CD_HIST_SEQ
            ,CHG_COL
        ) VALUES (
            TENV_MDM_CD_COL_HIST_SEQ.NEXTVAL
            ,#{histSeq}
            ,#{columnName}
        )
    </insert>

    <!-- 다음 매체코드 이력 시퀀스 조회 -->
    <select id="getNextMdmCodeHistorySequence" resultType="Long">
        SELECT TENV_MDM_CD_HIST_SEQ.NEXTVAL FROM DUAL
    </select>

</mapper>