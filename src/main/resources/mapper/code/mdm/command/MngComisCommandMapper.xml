<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="kr.or.komca.admin.code.mdm.mapper.command.MngComisCommandMapper">

    <!-- 관리수수료율 존재 여부 확인 -->
    <select id="existsMngComisRate" resultType="boolean">
        SELECT CASE WHEN COUNT(1) > 0 THEN 1 ELSE 0 END
        FROM TENV_MNG_COMIS
        WHERE MDM_CD = #{mdmCd}
            AND LARGE_CLASS_CD = #{largeClassCd}
            AND AVE_CLASS_CD = #{aveClassCd}
            AND SMALL_CLASS_CD = #{smallClassCd}
            AND APPL_YRMN = #{applYrmn}
    </select>

    <!-- 관리수수료율 생성 -->
    <insert id="insertMngComisRate">
        INSERT INTO TENV_MNG_COMIS (
            MDM_CD
            ,LARGE_CLASS_CD
            ,AVE_CLASS_CD
            ,SMALL_CLASS_CD
            ,APPL_YRMN
            ,MNG_COMIS_RATE
        ) VALUES (
            #{mdmCd}
            ,#{largeClassCd}
            ,#{aveClassCd}
            ,#{smallClassCd}
            ,#{applYrmn}
            ,#{mngComisRate}
        )
    </insert>

    <!-- 관리수수료율 수정 -->
    <update id="updateMngComisRate">
        UPDATE TENV_MNG_COMIS
        SET MNG_COMIS_RATE = #{mngComisRate}
        WHERE MDM_CD = #{mdmCd}
            AND LARGE_CLASS_CD = #{largeClassCd}
            AND AVE_CLASS_CD = #{aveClassCd}
            AND SMALL_CLASS_CD = #{smallClassCd}
            AND APPL_YRMN = #{applYrmn}
    </update>

</mapper>