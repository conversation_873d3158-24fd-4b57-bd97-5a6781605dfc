<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="kr.or.komca.admin.code.payment.mapper.query.PaymentExcelMapper">

    <!-- 지급공제 목록 조회 쿼리 -->
    <select id="getPaymentExcelList" resultType="kr.or.komca.admin.code.payment.dto.query.response.PaymentExcel">
        SELECT
            TI.SUPP_DEDCT_GBN
            ,TI.SUPP_DEDCT_CD
            ,TI.CHG_NM
            ,TI.RETIR_YN
            ,TI.CHG_AMT
            ,TI.EMPINS_YN
            ,TI.PER_CALTN_YN
            ,TI.UNIT_CALTN
            ,TI.ACCT_CD
            ,TI.REMAK
            ,TI.USE_YN
            ,TI.INSPERS_ID
            ,TI.INS_DT
            ,TI.MODPERS_ID
            ,TI.MOD_DT
            ,TA.ACCT_NM
            ,TC.CD_NM AS SUPP_DEDCT_CD_NM
            ,TCG.CD_NM AS SUPP_DEDCT_GBN_NM
            ,TCU.CD_NM AS UNIT_CALTN_NM
        FROM TPAY_INDEUD TI
            LEFT JOIN TENV_CD TC ON (TC.CD = TI.SUPP_DEDCT_CD AND TC.par_cd = '00102' AND TC.USE_YN = 'Y')
            LEFT JOIN TENV_CD TCG ON (TCG.CD = TI.SUPP_DEDCT_GBN AND TCG.PAR_CD = '00099' AND TCG.USE_YN = 'Y')
            LEFT JOIN TENV_CD TCU ON (TCU.CD = TI.UNIT_CALTN AND TCU.PAR_CD = '00164' AND TCU.USE_YN = 'Y')
            LEFT JOIN TCAC_ACCT TA ON (TA.ACCT_CD = TI.ACCT_CD AND TA.ACCTN_GBN_NORMAL_YN = 'Y')
        WHERE TI.USE_YN = 'Y'
        <if test="condition != null ">
            <if test="condition.suppDedctGbn != null and condition.suppDedctGbn != ''">
                AND TI.SUPP_DEDCT_GBN = #{condition.suppDedctGbn}
            </if>
            <if test="condition.retirYn != null and condition.retirYn != ''">
                AND TI.RETIR_YN = #{condition.retirYn}
            </if>
            <if test="condition.useYn != null and condition.useYn != ''">
                AND TI.USE_YN = #{condition.useYn}
            </if>
        </if>
        ORDER BY SUPP_DEDCT_CD
    </select>
</mapper>
