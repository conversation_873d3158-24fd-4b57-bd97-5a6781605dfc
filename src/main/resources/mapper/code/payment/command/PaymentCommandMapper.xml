<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="kr.or.komca.admin.code.payment.mapper.command.PaymentCommandMapper">

    <!-- 지급공제 생성 쿼리 -->
    <insert id="createPayment">
        INSERT INTO TPAY_INDEUD ( SUPP_DEDCT_GBN
                                , SUPP_DEDCT_CD
                                , CHG_NM
                                , RETIR_YN
                                , CHG_AMT
                                , EMPINS_YN
                                , PER_CALTN_YN
                                , UNIT_CALTN
                                , ACCT_CD
                                , REMAK
                                , USE_YN
                                , INSPERS_ID
                                , INS_DT)
        VALUES ( #{command.suppDedctGbn}
               , #{command.suppDedctCd}
               , #{command.chgNm}
               , #{command.retirYn}
               , #{command.chgAmt}
               , #{command.empinsYn}
               , #{command.perCaltnYn}
               , #{command.unitCaltn}
               , #{command.acctCd}
               , #{command.remak}
               , #{command.useYn}
               , #{inspersId}
               , SYSDATE)
    </insert>

    <!-- 지급공제 수정 쿼리 -->
    <update id="updatePayment">
        UPDATE TPAY_INDEUD
        SET SUPP_DEDCT_GBN = #{command.suppDedctGbn}
          , CHG_NM         = #{command.chgNm}
          , RETIR_YN       = #{command.retirYn}
          , CHG_AMT        = #{command.chgAmt}
          , EMPINS_YN      = #{command.empinsYn}
          , PER_CALTN_YN   = #{command.perCaltnYn}
          , UNIT_CALTN     = #{command.unitCaltn}
          , ACCT_CD        = #{command.acctCd}
          , REMAK          = #{command.remak}
          , USE_YN         = #{command.useYn}
          , MODPERS_ID     = #{modpersId}
          , MOD_DT         = SYSDATE
        WHERE SUPP_DEDCT_CD = #{suppDedctCd}
    </update>

    <!-- 지급공제 삭제 쿼리 -->
    <delete id="deletePayment">
        UPDATE TPAY_INDEUD
        SET USE_YN = 'N'
        WHERE SUPP_DEDCT_CD = #{suppDedctCd}
    </delete>
</mapper>
