# --------------------------- 필수 설정 ---------------------------------- #
# app-logging.yml
# 로깅 설정 파일

komca:
  logging:
    aop:
      enabled: true     # AOP 기반 로깅 활성화 (default : true)
    xml-config:
      enabled: true     # 로깅 XML 파일 공통 설정 활성화 (common-logging의 xml 참조) (default : true)
    filter:
      trace-id:
        enabled: true   # 요청 추적 ID 로깅 활성화 (default : true)

# ----------------------- 선택 설정 [Overriding] -------------------------- #

logging:
  # ---- 테스트용 로컬 logging.xml 사용 시 작성 (사용하지 않을 시 주석 처리 필요) ---- #
  # 기본 log4j2.xml는 설정 환경에 따라 common-logging 패키지에서 자동 등록됩니다. #
  # 다른 log파일을 등록하고 싶다면 아래의 주석을 제거하고 경로를 입력해주세요. #
  ###################################################
#  config:
#    classpath: logging-temp/log4j2-dev.xml # 테스트를 위한 xml 파일
  ###################################################
  # -------------------------------------------------------------------- #
    level:
      root: info         # 루트 로거 레벨
      kr:
        or:
          komca: info    # KOMCA 패키지 로거 레벨
