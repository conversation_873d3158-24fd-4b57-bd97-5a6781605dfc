<?xml version="1.0" encoding="UTF-8"?>
<Configuration status="DEBUG">
    <Properties>
        <!-- 애플리케이션 로그 패턴 (traceId, hostname 포함) -->
<!--        <Property name="APP_LOG_PATTERN">[%X{traceId}] [%X{hostname}] %d{yyyy-MM-dd HH:mm:ss.SSS} [%level] [%t] %c{1} - %msg%n</Property> -->
<!--        <Property name="APP_LOG_PATTERN">[%level] [%t] %d{yyyy-MM-dd HH:mm:ss.SSS} [TraceId:%X{traceId}] %c{1} - %msg%n</Property>-->
        <Property name="APP_LOG_PATTERN">[LOCAL][%level] [%t] %d{yyyy-MM-dd HH:mm:ss.SSS} [TraceId:%X{traceId}] [UserId:%X{userId}] %c{1} - %msg%n</Property>
        <!-- 시스템 로그 패턴 (단순화된 형태) -->
        <Property name="SYS_LOG_PATTERN">[LOCAL][%level] [%t] %c{1} - %msg%n</Property>
        <Property name="APP_LOG_ROOT">logs</Property>
    </Properties>

    <Appenders>
        <!-- 애플리케이션 로그용 콘솔 -->
        <Console name="AppConsole" target="SYSTEM_OUT">
            <PatternLayout pattern="${APP_LOG_PATTERN}"/>
        </Console>

        <!-- 시스템 로그용 콘솔 -->
        <Console name="SysConsole" target="SYSTEM_OUT">
            <PatternLayout pattern="${SYS_LOG_PATTERN}"/>
        </Console>

        <RollingFile name="ApplicationLog"
                     fileName="${APP_LOG_ROOT}/application.log"
                     filePattern="${APP_LOG_ROOT}/application-%d{yyyy-MM-dd}-%i.log">
            <PatternLayout pattern="${APP_LOG_PATTERN}"/>
            <Policies>
                <TimeBasedTriggeringPolicy />
                <SizeBasedTriggeringPolicy size="10MB"/>
            </Policies>
            <DefaultRolloverStrategy max="10"/>
        </RollingFile>

        <RollingFile name="JsonApplicationLog"
                     fileName="${APP_LOG_ROOT}/json-application.log"
                     filePattern="${APP_LOG_ROOT}/json-application-%d{yyyy-MM-dd}-%i.log">
            <JsonLayout complete="false" compact="true">
                <KeyValuePair key="timestamp" value="$${date:yyyy-MM-dd HH:mm:ss.SSS}"/>
                <KeyValuePair key="service" value="${APPLICATION_NAME}"/>
                <KeyValuePair key="trace" value="$${ctx:traceId}"/>
                <KeyValuePair key="span" value="$${ctx:spanId}"/>
            </JsonLayout>
            <Policies>
                <TimeBasedTriggeringPolicy />
                <SizeBasedTriggeringPolicy size="10MB"/>
            </Policies>
            <DefaultRolloverStrategy max="10"/>
        </RollingFile>
    </Appenders>

    <Loggers>
        <!-- kr.or.komca 패키지는 APP_LOG_PATTERN 사용 -->
        <Logger name="kr.or.komca" level="DEBUG" additivity="false">
            <AppenderRef ref="AppConsole"/>
            <AppenderRef ref="ApplicationLog"/>
            <AppenderRef ref="JsonApplicationLog"/>
        </Logger>

        <!-- 나머지 모든 로그는 SYS_LOG_PATTERN 사용 -->
        <Root level="INFO">
            <AppenderRef ref="SysConsole"/>
            <AppenderRef ref="ApplicationLog"/>
            <AppenderRef ref="JsonApplicationLog"/>
        </Root>
    </Loggers>
</Configuration>