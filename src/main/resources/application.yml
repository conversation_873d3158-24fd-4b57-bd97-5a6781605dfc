# application.yml
# 메인 애플리케이션 설정 파일

spring:
  jackson:
    time-zone: Asia/Seoul
  application:
    name: komca-admin-api  # 서비스 이름 정의
  config:
    import:
      # 외부 라이브러리 설정
      - optional:classpath:/META-INF/resources/springdoc-config.yml   # Swagger 설정 정보 import (common-core 참조)
      - optional:classpath:/META-INF/resources/mybatis-config.yml     # MyBatis 설정 정보 import (common-core 참조)
      - optional:classpath:/META-INF/resources/jwt-config.yml         # JWT 설정 정보 import (common-core 참조)

      # 로컬 프로젝트 설정
      - classpath:/config/app-core.yml          # 프로젝트 주요 설정 정보 (현재 프로젝트 참조)
      - classpath:/config/app-logging.yml       # logging 설정 정보 (현재 프로젝트 참조)
      - classpath:/config/app-db.yml            # DB 설정 정보 import (현재 프로젝트 참조)


server:
  port: 8080  # 애플리케이션 서버 포트

  management:
    endpoints:
      web:
        exposure:
          include: health,metrics,prometheus
        base-path: /actuator     # 추가: 기본 경로 설정
    endpoint:
      health:
        #     show-details: when_authorized    # 추가: 인증된 사용자에게만 상세 정보 노출 - 현재 테스트로 주석처리
        probes:
          enabled: true    # 추가: k8s 라이브니스/레디니스 프로브 활성화
    metrics:
      tags:
        application: ${spring.application.name}
        environment: ${spring.profiles.active:dev}
      enable:
        jvm: true         # 추가: JVM 메트릭 활성화
        process: true     # 추가: 프로세스 메트릭 활성화
        system: true      # 추가: 시스템 메트릭 활성화
        http: true        # 추가: HTTP 요청 관련 메트릭 활성화
        log4j2: true      # 추가: log4j2 관련 메트릭 활성화
    prometheus:
      metrics:
        export:
          enabled: true   # 추가: 프로메테우스 메트릭 익스포트 활성화

# AWS 공통 설정
aws:
  region: ${AWS_REGION}  # 워크플로우에서 전달받는 리전 사용

  # S3 설정
  s3:
    enabled: true
    uploadPath: admin
    urlExpirationMinutes: 15


