-- ===================================
-- TENV_KAKAO_TEMPLATE 테이블 생성
-- 용도: 카카오 알림톡 템플릿 내용 관리 (ASIS JavaScript 하드코딩 대체)
-- ===================================

-- 1. 템플릿 내용 관리 테이블 생성
CREATE TABLE TENV_KAKAO_TEMPLATE (
    TMPL_CD VARCHAR2(50) PRIMARY KEY,                -- 템플릿 코드 (TENV_CODE의 CODE_CD와 연계)
    TMPL_CONTENT CLOB NOT NULL,                      -- 템플릿 내용
    TMPL_VARIABLES VARCHAR2(500),                    -- 템플릿 변수 (예: #{신탁종류},#{금액})
    CATEGORY VARCHAR2(50),                           -- 카테고리 (MEM, JIBU, BOKJE 등)
    USE_YN VARCHAR2(1) DEFAULT 'Y' NOT NULL,         -- 사용여부
    INS_DT DATE DEFAULT SYSDATE NOT NULL,           -- 등록일시
    INSPERS_ID VARCHAR2(30),                        -- 등록자ID
    MOD_DT DATE DEFAULT SYSDATE,                    -- 수정일시
    MODPERS_ID VARCHAR2(30)                         -- 수정자ID
);

-- 2. 인덱스 생성
CREATE INDEX IDX_TENV_KAKAO_TEMPLATE_01 ON TENV_KAKAO_TEMPLATE(CATEGORY, USE_YN);
CREATE INDEX IDX_TENV_KAKAO_TEMPLATE_02 ON TENV_KAKAO_TEMPLATE(USE_YN, TMPL_CD);

-- 3. 제약조건 추가
ALTER TABLE TENV_KAKAO_TEMPLATE ADD CONSTRAINT CHK_TENV_KAKAO_TEMPLATE_USE_YN 
    CHECK (USE_YN IN ('Y', 'N'));

-- 4. 코멘트 추가
COMMENT ON TABLE TENV_KAKAO_TEMPLATE IS '카카오 알림톡 템플릿 내용 관리 테이블';
COMMENT ON COLUMN TENV_KAKAO_TEMPLATE.TMPL_CD IS '템플릿 코드 (TENV_CODE.CODE_CD와 연계)';
COMMENT ON COLUMN TENV_KAKAO_TEMPLATE.TMPL_CONTENT IS '템플릿 내용';
COMMENT ON COLUMN TENV_KAKAO_TEMPLATE.TMPL_VARIABLES IS '템플릿 변수 목록 (쉼표 구분)';
COMMENT ON COLUMN TENV_KAKAO_TEMPLATE.CATEGORY IS '템플릿 카테고리 (MEM, JIBU, BOKJE 등)';
COMMENT ON COLUMN TENV_KAKAO_TEMPLATE.USE_YN IS '사용여부';

-- 5. 기존 ASIS 하드코딩 템플릿 데이터 마이그레이션
INSERT INTO TENV_KAKAO_TEMPLATE (TMPL_CD, TMPL_CONTENT, TMPL_VARIABLES, CATEGORY, INSPERS_ID) VALUES 
('MEM_24', '[한국음악저작권협회]
-입금요청-
수협은행 1010-2313-7868
#{신탁종류} #{금액(사유)}
(반드시 본인의 성명으로 입금해주시기 바랍니다.)', '#{신탁종류},#{금액(사유)}', 'MEM', 'SYSTEM');

INSERT INTO TENV_KAKAO_TEMPLATE (TMPL_CD, TMPL_CONTENT, TMPL_VARIABLES, CATEGORY, INSPERS_ID) VALUES 
('MEM_19', '[한국음악저작권협회 회원팀]
안녕하세요 회원님
저작권료 지급계좌가 등록되어 있지 않아
아래의 통장 사본 중 1가지를
<EMAIL> 로 제출하여 주십시오.
(국민, 우리, 농협, 외환, 신한, 우체국, 하나, 기업, 카카오뱅크, 제일, 새마을금고, 부산, 수산업협동조합 중 택1)', '', 'MEM', 'SYSTEM');

INSERT INTO TENV_KAKAO_TEMPLATE (TMPL_CD, TMPL_CONTENT, TMPL_VARIABLES, CATEGORY, INSPERS_ID) VALUES 
('MEM_11', '[한국음악저작권협회]
회원님께서 요청하신 재적증명서가 이메일로 발급되었습니다.', '', 'MEM', 'SYSTEM');

INSERT INTO TENV_KAKAO_TEMPLATE (TMPL_CD, TMPL_CONTENT, TMPL_VARIABLES, CATEGORY, INSPERS_ID) VALUES 
('MEM_14', '[한국음악저작권협회]
안녕하세요 회원님
음원이 미제출되어 작품등록이 지연되고 있습니다.
<EMAIL> 로 음원을 제출하여 주세요.', '', 'MEM', 'SYSTEM');

INSERT INTO TENV_KAKAO_TEMPLATE (TMPL_CD, TMPL_CONTENT, TMPL_VARIABLES, CATEGORY, INSPERS_ID) VALUES 
('SEOMYEON_01', '[서면결의서 접수 안내]
#{회원명}회원님, #{접수방법} 통해 접수하신 서면결의서가 접수완료 되었음을 알려드립니다.
제출해주셔서 감사드립니다.

한국음악저작권협회 기획팀 02-2660-#{담당내선번호}', '#{회원명},#{접수방법},#{담당내선번호}', 'PLAN', 'SYSTEM');

INSERT INTO TENV_KAKAO_TEMPLATE (TMPL_CD, TMPL_CONTENT, TMPL_VARIABLES, CATEGORY, INSPERS_ID) VALUES 
('BOKJE_12', '[KOMCA#{부서명}]
-입금요청-
수협 1010-2323-7711 한국음악저작권협회 #{금액}원(#{건수}건) 입니다.

※제3자가 제작/녹음한 음원을 사용하는 경우, 저작인접권을 ''별도로'' 처리해야합니다. 한국음악실연자연합회(027458286),한국음반산업협회(0232705900)에 문의하세요.

※개사,편곡 등 원곡이 변형되는 개작은 협회가 아닌 원저작자의 판단하에 저작인격권 동의가 필요합니다. 협회에서는 홈페이지에 관련 내용을 게시하였으니 참고바랍니다.', '#{부서명},#{금액},#{건수}', 'BOKJE', 'SYSTEM');

INSERT INTO TENV_KAKAO_TEMPLATE (TMPL_CD, TMPL_CONTENT, TMPL_VARIABLES, CATEGORY, INSPERS_ID) VALUES 
('BOKJE_16', '[KOMCA#{부서명}]
-입금요청-
수협 1010-2323-7487 한국음악저작권협회 #{금액}원(#{건수}건) 입니다.

※제3자가 제작/녹음한 음원을 사용하는 경우, 저작인접권을 ''별도로'' 처리해야합니다. 한국음악실연자연합회(027458286),한국음반산업협회(0232705900)에 문의하세요.

※개사,편곡 등 원곡이 변형되는 개작은 협회가 아닌 원저작자의 판단하에 저작인격권 동의가 필요합니다. 협회에서는 홈페이지에 관련 내용을 게시하였으니 참고바랍니다.', '#{부서명},#{금액},#{건수}', 'BOKJE', 'SYSTEM');

INSERT INTO TENV_KAKAO_TEMPLATE (TMPL_CD, TMPL_CONTENT, TMPL_VARIABLES, CATEGORY, INSPERS_ID) VALUES 
('MEM_17', '[한국음악저작권협회 회원팀]
안녕하세요 작가님,
신탁계약체결 및 회원가입, 예명 신청 방법에 대한 안내입니다.
아래 주소를 참조하시어 해당 절차를 진행해주시기 바랍니다.
https://blog.naver.com/komca1964/223319302356', '', 'MEM', 'SYSTEM');

INSERT INTO TENV_KAKAO_TEMPLATE (TMPL_CD, TMPL_CONTENT, TMPL_VARIABLES, CATEGORY, INSPERS_ID) VALUES 
('MEM_18', '[한국음악저작권협회 회원팀]
안녕하세요 작가님,
예명 신청 방법에 대한 안내입니다.
아래 주소를 참조하시어 해당 절차를 진행해주시기 바랍니다.
https://blog.naver.com/komca1964/223319302356', '', 'MEM', 'SYSTEM');

-- 6. 커밋
COMMIT;

-- 확인 쿼리
SELECT TMPL_CD, TMPL_CONTENT, CATEGORY, USE_YN 
FROM TENV_KAKAO_TEMPLATE 
ORDER BY CATEGORY, TMPL_CD;