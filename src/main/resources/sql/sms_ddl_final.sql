-- ====================================================================
-- KOMCA SMS 시스템 DDL 스크립트 (최종 통합버전)
-- 작성일: 2024-12-23
-- 대상: SB_1+2 (통합 발송화면), SB_3 (이력관리)
-- ====================================================================

-- ====================================================================
-- 1. SMS 발송 이력 메인 테이블 (SB_3용)
-- ====================================================================
CREATE TABLE TST_SMS_SEND_HIST (
    SMS_HIST_SEQ        NUMBER          NOT NULL,    -- 이력 시퀀스 (PK)
    MSG_ID              VARCHAR2(50)    NOT NULL,    -- 메시지 ID
    USER_ID             VARCHAR2(50),               -- 발송자 사용자ID
    SMS_MSG             CLOB,                       -- SMS 메시지 내용
    SUBJECT             VARCHAR2(100),              -- 제목
    CALLBACK            VARCHAR2(20),               -- 발신번호
    SEND_DATE           VARCHAR2(14),               -- 전송일자 (YYYYMMDDHH24MISS)
    SEND_STATUS         VARCHAR2(10)    NOT NULL,    -- 전송상태 (PENDING/SUCCESS/FAILED)
    SEND_RESULT         VARCHAR2(500),              -- 발송결과 메시지
    SEND_PROC_TIME      VARCHAR2(14),               -- 발송처리시간
    DEST_COUNT          NUMBER          DEFAULT 0,   -- 총 수신자수
    SUCCESS_COUNT       NUMBER          DEFAULT 0,   -- 성공건수  
    FAILED_COUNT        NUMBER          DEFAULT 0,   -- 실패건수
    CHANNEL             VARCHAR2(1)     NOT NULL,    -- 발송채널 (S:SMS, A:알림톡)
    SENDER_TYPE         VARCHAR2(2),                -- 발신번호구분
    TMPL_CD             VARCHAR2(20),               -- 템플릿코드 (알림톡용)
    IMMEDIATE_YN        VARCHAR2(1)     DEFAULT 'Y', -- 즉시발송여부
    RETRY_COUNT         NUMBER          DEFAULT 0,   -- 재시도 횟수
    INS_DT              DATE            NOT NULL,    -- 등록일시
    INSPERS_ID          VARCHAR2(50)    NOT NULL,    -- 등록자ID
    MOD_DT              DATE,                       -- 수정일시
    MODPERS_ID          VARCHAR2(50),               -- 수정자ID
    
    CONSTRAINT PK_TST_SMS_SEND_HIST PRIMARY KEY (SMS_HIST_SEQ)
);

-- ====================================================================
-- 2. SMS 발송 상세 이력 테이블 (수신자별 - SB_3용)
-- ====================================================================
CREATE TABLE TST_SMS_SEND_DETAIL_HIST (
    SMS_DETAIL_HIST_SEQ NUMBER          NOT NULL,    -- 상세이력 시퀀스 (PK)
    SMS_HIST_SEQ        NUMBER          NOT NULL,    -- 메인이력 시퀀스 (FK)
    RECIPIENT_ID        VARCHAR2(50)    NOT NULL,    -- 수신자ID
    RECIPIENT_TYPE      VARCHAR2(10)    NOT NULL,    -- 수신자타입 (BSCON/MEMBER)
    RECIPIENT_NAME      VARCHAR2(100),              -- 수신자명
    PHONE_NUMBER        VARCHAR2(20)    NOT NULL,    -- 수신자 전화번호
    SEND_STATUS         VARCHAR2(10)    NOT NULL,    -- 개별 발송상태
    SEND_RESULT         VARCHAR2(500),              -- 개별 발송결과
    FALLBACK_SMS_YN     VARCHAR2(1)     DEFAULT 'N', -- SMS 대체발송 여부
    INS_DT              DATE            NOT NULL,    -- 등록일시
    
    CONSTRAINT PK_TST_SMS_SEND_DETAIL_HIST PRIMARY KEY (SMS_DETAIL_HIST_SEQ),
    CONSTRAINT FK_SMS_DETAIL_HIST FOREIGN KEY (SMS_HIST_SEQ) 
        REFERENCES TST_SMS_SEND_HIST(SMS_HIST_SEQ)
);

-- ====================================================================
-- 3. SMS 수신자 임시 선택 테이블 (SB_1+2 통합화면용)
-- ====================================================================
CREATE TABLE TST_SMS_SELECTED_RECIPIENT (
    SMS_SELECT_SEQ      NUMBER          NOT NULL,    -- 시퀀스 PK
    USER_ID             VARCHAR2(30)    NOT NULL,    -- 사용자ID
    RECIPIENT_ID        VARCHAR2(20)    NOT NULL,    -- 수신자ID (BSCON_CD or MB_CD)
    RECIPIENT_TYPE      VARCHAR2(10)    NOT NULL,    -- 수신자타입 (BSCON, MEMBER)
    RECIPIENT_NAME      VARCHAR2(100),              -- 수신자명
    PHONE_NUMBER        VARCHAR2(20),               -- 전화번호
    INSPERS_ID          VARCHAR2(30)    NOT NULL,    -- 등록자
    INS_DT              DATE            DEFAULT SYSDATE NOT NULL, -- 등록일시
    DEL_YN              VARCHAR2(1)     DEFAULT 'N',  -- 삭제여부
    DELPERS_ID          VARCHAR2(30),               -- 삭제자
    DEL_DT              DATE,                       -- 삭제일시
    
    CONSTRAINT PK_TST_SMS_SELECTED_RECIPIENT PRIMARY KEY (SMS_SELECT_SEQ)
);

-- ====================================================================
-- 4. 시퀀스 생성
-- ====================================================================
CREATE SEQUENCE TST_SMS_SEND_HIST_SEQ START WITH 1 INCREMENT BY 1;
CREATE SEQUENCE TST_SMS_SEND_DETAIL_HIST_SEQ START WITH 1 INCREMENT BY 1;
CREATE SEQUENCE TST_SMS_SELECT_SEQ START WITH 1 INCREMENT BY 1;

-- ====================================================================
-- 5. 성능 최적화 인덱스
-- ====================================================================
-- 이력 조회 성능 인덱스
CREATE INDEX IX_SMS_HIST_01 ON TST_SMS_SEND_HIST(SEND_DATE, SEND_STATUS);
CREATE INDEX IX_SMS_HIST_02 ON TST_SMS_SEND_HIST(USER_ID, INS_DT);
CREATE INDEX IX_SMS_HIST_03 ON TST_SMS_SEND_HIST(CHANNEL, SEND_STATUS, SEND_DATE);
CREATE INDEX IX_SMS_HIST_04 ON TST_SMS_SEND_HIST(TMPL_CD, SEND_DATE);

-- 상세 이력 인덱스
CREATE INDEX IX_SMS_DETAIL_HIST_01 ON TST_SMS_SEND_DETAIL_HIST(SMS_HIST_SEQ);
CREATE INDEX IX_SMS_DETAIL_HIST_02 ON TST_SMS_SEND_DETAIL_HIST(RECIPIENT_TYPE, PHONE_NUMBER);

-- 선택 수신자 인덱스
CREATE INDEX IX_SMS_SELECTED_01 ON TST_SMS_SELECTED_RECIPIENT(USER_ID, DEL_YN);
CREATE INDEX IX_SMS_SELECTED_02 ON TST_SMS_SELECTED_RECIPIENT(RECIPIENT_TYPE, RECIPIENT_ID);

-- ====================================================================
-- 6. 기본 코드 데이터 삽입
-- ====================================================================
-- SMS 발신번호 코드
INSERT INTO TENV_CODE (HIGH_CD, CODE_CD, CODE_NM, CODE_VAL, USE_YN, SORT_SEQ, INS_DT, INSPERS_ID) 
VALUES ('SMS_SENDER', '01', '대표번호', '02-2660-0400', 'Y', 1, SYSDATE, 'SYSTEM');

INSERT INTO TENV_CODE (HIGH_CD, CODE_CD, CODE_NM, CODE_VAL, USE_YN, SORT_SEQ, INS_DT, INSPERS_ID) 
VALUES ('SMS_SENDER', '02', '일반번호', '02-2660-0401', 'Y', 2, SYSDATE, 'SYSTEM');

-- SMS 발송 상태 코드
INSERT INTO TENV_CODE (HIGH_CD, CODE_CD, CODE_NM, CODE_VAL, USE_YN, SORT_SEQ, INS_DT, INSPERS_ID) 
VALUES ('SMS_STATUS', '01', 'PENDING', '발송대기', 'Y', 1, SYSDATE, 'SYSTEM');

INSERT INTO TENV_CODE (HIGH_CD, CODE_CD, CODE_NM, CODE_VAL, USE_YN, SORT_SEQ, INS_DT, INSPERS_ID) 
VALUES ('SMS_STATUS', '02', 'SUCCESS', '발송성공', 'Y', 2, SYSDATE, 'SYSTEM');

INSERT INTO TENV_CODE (HIGH_CD, CODE_CD, CODE_NM, CODE_VAL, USE_YN, SORT_SEQ, INS_DT, INSPERS_ID) 
VALUES ('SMS_STATUS', '03', 'FAILED', '발송실패', 'Y', 3, SYSDATE, 'SYSTEM');

INSERT INTO TENV_CODE (HIGH_CD, CODE_CD, CODE_NM, CODE_VAL, USE_YN, SORT_SEQ, INS_DT, INSPERS_ID) 
VALUES ('SMS_STATUS', '04', 'RETRY', '재시도중', 'Y', 4, SYSDATE, 'SYSTEM');

-- SMS 발송 채널 코드
INSERT INTO TENV_CODE (HIGH_CD, CODE_CD, CODE_NM, CODE_VAL, USE_YN, SORT_SEQ, INS_DT, INSPERS_ID) 
VALUES ('SMS_CHANNEL', 'S', 'SMS', 'SMS', 'Y', 1, SYSDATE, 'SYSTEM');

INSERT INTO TENV_CODE (HIGH_CD, CODE_CD, CODE_NM, CODE_VAL, USE_YN, SORT_SEQ, INS_DT, INSPERS_ID) 
VALUES ('SMS_CHANNEL', 'A', 'ALIMTALK', '알림톡', 'Y', 2, SYSDATE, 'SYSTEM');

-- SMS/카카오 발송 플래그
INSERT INTO TENV_FLAG (FLAG, FLAG_GBN, MOD_DT, MODPERS_ID) 
VALUES ('Y', 'SMS_SEND_ENABLED', SYSDATE, 'SYSTEM');

INSERT INTO TENV_FLAG (FLAG, FLAG_GBN, MOD_DT, MODPERS_ID) 
VALUES ('Y', 'KAKAO_SEND_ENABLED', SYSDATE, 'SYSTEM');

-- ====================================================================
-- 7. 데이터 검증 쿼리
-- ====================================================================
SELECT 'SMS 이력 테이블' as 항목, COUNT(*) as 생성여부 FROM USER_TABLES WHERE TABLE_NAME = 'TST_SMS_SEND_HIST';
SELECT 'SMS 상세이력 테이블' as 항목, COUNT(*) as 생성여부 FROM USER_TABLES WHERE TABLE_NAME = 'TST_SMS_SEND_DETAIL_HIST';
SELECT 'SMS 선택 테이블' as 항목, COUNT(*) as 생성여부 FROM USER_TABLES WHERE TABLE_NAME = 'TST_SMS_SELECTED_RECIPIENT';
SELECT '발신번호 코드' as 항목, COUNT(*) as 건수 FROM TENV_CODE WHERE HIGH_CD = 'SMS_SENDER';
SELECT '발송상태 코드' as 항목, COUNT(*) as 건수 FROM TENV_CODE WHERE HIGH_CD = 'SMS_STATUS';
SELECT '발송채널 코드' as 항목, COUNT(*) as 건수 FROM TENV_CODE WHERE HIGH_CD = 'SMS_CHANNEL';
SELECT 'SMS 플래그' as 항목, FLAG as 값 FROM TENV_FLAG WHERE FLAG_GBN = 'SMS_SEND_ENABLED';
SELECT '카카오 플래그' as 항목, FLAG as 값 FROM TENV_FLAG WHERE FLAG_GBN = 'KAKAO_SEND_ENABLED';

COMMIT;

-- ====================================================================
-- 8. 샘플 데이터 (테스트용 - 주석 처리)
-- ====================================================================
/*
-- 샘플 발송 이력
INSERT INTO TST_SMS_SEND_HIST (
    SMS_HIST_SEQ, MSG_ID, USER_ID, SMS_MSG, SUBJECT, CALLBACK, SEND_DATE, 
    SEND_STATUS, DEST_COUNT, SUCCESS_COUNT, FAILED_COUNT, CHANNEL, 
    SENDER_TYPE, IMMEDIATE_YN, INS_DT, INSPERS_ID
) VALUES (
    TST_SMS_SEND_HIST_SEQ.NEXTVAL, 'MSG_' || TO_CHAR(SYSDATE, 'YYYYMMDDHH24MISS'), 
    'ADMIN', '테스트 메시지입니다.', '테스트 제목', '02-2660-0400', 
    TO_CHAR(SYSDATE, 'YYYYMMDDHH24MISS'), 'SUCCESS', 1, 1, 0, 'S', 
    '01', 'Y', SYSDATE, 'SYSTEM'
);

-- 샘플 상세 이력
INSERT INTO TST_SMS_SEND_DETAIL_HIST (
    SMS_DETAIL_HIST_SEQ, SMS_HIST_SEQ, RECIPIENT_ID, RECIPIENT_TYPE, 
    RECIPIENT_NAME, PHONE_NUMBER, SEND_STATUS, SEND_RESULT, INS_DT
) VALUES (
    TST_SMS_SEND_DETAIL_HIST_SEQ.NEXTVAL, TST_SMS_SEND_HIST_SEQ.CURRVAL, 
    'TEST001', 'BSCON', '테스트거래처', '010-1234-5678', 
    'SUCCESS', '발송완료', SYSDATE
);
*/