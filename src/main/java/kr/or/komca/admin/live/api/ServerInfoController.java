package kr.or.komca.admin.live.api;

import kr.or.komca.common.exception.response.success.CommonSuccessResponse;
import kr.or.komca.admin.live.dto.VersionInfo;
import lombok.RequiredArgsConstructor;
import org.springframework.boot.info.BuildProperties;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("api/public/server")
@RequiredArgsConstructor
public class ServerInfoController {
	private final BuildProperties buildProperties;

	@GetMapping("/info")
	public ResponseEntity<CommonSuccessResponse<VersionInfo>> getVersion() {

		VersionInfo versionInfo = new VersionInfo(
				buildProperties.getVersion(),
				buildProperties.getName(),
				buildProperties.getTime(),
				buildProperties.getGroup()
		);

		return CommonSuccessResponse.ok(versionInfo);
	}
}
