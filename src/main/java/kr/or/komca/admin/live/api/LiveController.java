package kr.or.komca.admin.live.api;

import kr.or.komca.common.exception.response.success.CommonSuccessResponse;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequiredArgsConstructor
public class LiveController {
    @GetMapping("/api/public/live")
    public ResponseEntity<CommonSuccessResponse<String>> checkLive() {
        return CommonSuccessResponse.ok("OK!");
    }
}
