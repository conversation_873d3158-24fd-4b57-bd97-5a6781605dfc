package kr.or.komca.admin.code.mdm.dto.command.request.sub;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Pattern;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.ToString;

/**
 * 매체 코드 수정 요청
 */
@Getter
@Builder
@AllArgsConstructor
@ToString
@Schema(description = "매체 코드 수정 요청")
public class UpdateMdmCode {

    /** 매체코드 */
    @Schema(description = "매체코드", example = "AA0103", minLength = 6, maxLength = 6)
    @NotBlank
    @Size(min = 6, max = 6)
    private String mdmCd;

    /** 매체명 */
    @Schema(description = "매체명", example = "KBS", minLength = 0, maxLength = 100)
    @NotBlank
    @Size(max = 100)
    private String mdmNm;

    /** 비고 */
    @Schema(description = "비고", example = "비고 예시")
    private String remak;

    /** 국내계정코드 */
    @Schema(description = "국내계정코드", example = "0845", minLength = 0, maxLength = 100)
    private String domAcctCd;

    /** 국외계정코드 */
    @Schema(description = "국외계정코드", example = "0846", minLength = 0, maxLength = 100)
    private String abrAcctCd;

    /** 국내지부계정코드 */
    @Schema(description = "국내지부계정코드", example = "0845", minLength = 0, maxLength = 100)
    private String domBranAcctCd;

    /** 국외지부계정코드 */
    @Schema(description = "국외지부계정코드", example = "0846", minLength = 0, maxLength = 100)
    private String abrBranAcctCd;

    /** 부가세여부 */
    @Schema(description = "부가세여부", example = "1", allowableValues = {"1", "0"})
    @NotNull
    @Pattern(regexp = "^[01]$")
    private String ataxYn;

    /** 사용여부 */
    @Schema(description = "사용여부", example = "Y", allowableValues = {"Y", "N"})
    @NotNull
    @Pattern(regexp = "^[YN]$")
    private String useYn;
    
    /** 정렬순서 */
    @Schema(description = "정렬순서", example = "1")
    @NotNull
    private Integer sortOrd;

    /** 관리수수료율 */
    @Schema(description = "관리수수료율", example = "3.5")
    private java.math.BigDecimal mngComisRate;
}
