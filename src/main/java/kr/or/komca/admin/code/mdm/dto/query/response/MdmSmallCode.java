package kr.or.komca.admin.code.mdm.dto.query.response;

import io.swagger.v3.oas.annotations.media.Schema; 
import jakarta.validation.constraints.*; 
import lombok.Builder; 
import lombok.Getter; 
import lombok.AllArgsConstructor; 
import lombok.NoArgsConstructor;

/**
* 매체코드(소분류) 조회에 대한 응답 DTO
*/
@Getter 
@Builder 
@AllArgsConstructor 
@NoArgsConstructor 
@Schema(description = "매체코드(소분류) 조회에 대한 응답 DTO")
public class MdmSmallCode {

    /** 대분류코드 */
    @Schema(description = "대분류코드")
    private String largeClassCd;

    /** 중분류코드 */
    @Schema(description = "중분류코드")
    private String aveClassCd;

    /** 소분류코드 */
    @Schema(description = "소분류코드")
    private String smallClassCd;

    /** 소분류명 */
    @Schema(description = "소분류명")
    private String smallClassNm;

    /** 사용여부 */
    @Schema(description = "사용여부", example = "Y")
    private String useYn;

    /** 사용여부 */
    @Schema(description = "정렬순서", example = "1")
    private int sortOrd;
}
