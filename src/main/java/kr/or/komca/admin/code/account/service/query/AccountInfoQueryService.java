package kr.or.komca.admin.code.account.service.query;

import kr.or.komca.admin.code.account.dto.query.condition.AccountInfoSearchCondition;
import kr.or.komca.admin.code.account.dto.query.response.AccountInfo;
import kr.or.komca.common.utils.core.dto.response.ListResponse;

public interface AccountInfoQueryService {

    /**
     * 계정 정보 목록 조회
     *
     * @param condition 검색 조건
     * @return 계정 목록
     */
    ListResponse<AccountInfo> getAccountInfoList(AccountInfoSearchCondition condition);

    /**
     * 계정 존재 여부 확인
     *
     * @param acctCd 계정
     */
    void validateExistAccount(String acctCd);

    /**
     * 계정가 중복되지 않는지 검증
     *
     * @param acctCd 계정
     */
    void validateAccountDuplicate(String acctCd);
}
