package kr.or.komca.admin.code.mdm.service.command;

import kr.or.komca.admin.code.mdm.dto.command.request.*;
import kr.or.komca.admin.code.mdm.dto.command.response.*;

/**
 * 매체 코드 Command 서비스 인터페이스
 */
public interface MdmCodeCommandService {

    /**
     * 대분류 코드 관리 (생성/수정/삭제)
     *
     * @param request 관리 요청
     * @param editpersId 편집자 ID
     * @return 관리 결과
     */
    ManageLargeCode manageLargeCode(ManageLargeCodeRequest request, String editpersId);

    /**
     * 중분류 코드 관리 (생성/수정/삭제)
     *
     * @param request 관리 요청
     * @param editpersId 편집자 ID
     * @return 관리 결과
     */
    ManageAveCode manageAveCode(ManageAveCodeRequest request, String editpersId);

    /**
     * 소분류 코드 관리 (생성/수정/삭제)
     *
     * @param request 관리 요청
     * @param editpersId 편집자 ID
     * @return 관리 결과
     */
    ManageSmallCode manageSmallCode(ManageSmallCodeRequest request, String editpersId);

    /**
     * 매체 코드 관리 (생성/수정/삭제)
     *
     * @param request 관리 요청
     * @param editpersId 편집자 ID
     * @return 관리 결과
     */
    ManageMdmCode manageMdmCode(ManageMdmCodeRequest request, String editpersId);

    /**
     * 서비스 코드 관리 (생성/수정/삭제)
     *
     * @param request 관리 요청
     * @param editpersId 편집자 ID
     * @return 관리 결과
     */
    ManageServiceCode manageServiceCode(ManageServiceCodeRequest request, String editpersId);

    /**
     * 대분류 코드 삭제
     *
     * @param largeClassCd 대분류 코드
     * @return 삭제 결과
     */
//    DeleteLargeCode deleteLargeCode(String largeClassCd);

    /**
     * 중분류 코드 삭제
     *
     * @param largeClassCd 대분류 코드
     * @param aveClassCd 중분류 코드
     * @return 삭제 결과
     */
//    DeleteAveCode deleteAveCode(String largeClassCd, String aveClassCd);

    /**
     * 소분류 코드 삭제
     *
     * @param largeClassCd 대분류 코드
     * @param aveClassCd 중분류 코드
     * @param smallClassCd 소분류 코드
     * @return 삭제 결과
     */
//    DeleteSmallCode deleteSmallCode(String largeClassCd, String aveClassCd, String smallClassCd);

    /**
     * 매체 코드 삭제
     *
     * @param largeClassCd 대분류 코드
     * @param aveClassCd 중분류 코드
     * @param smallClassCd 소분류 코드
     * @param mdmCd 매체 코드
     * @return 삭제 결과
     */
//    DeleteMdmCode deleteMdmCode(String largeClassCd, String aveClassCd, String smallClassCd, String mdmCd);

    /**
     * 서비스 코드 삭제
     *
     * @param largeClassCd 대분류 코드
     * @param aveClassCd 중분류 코드
     * @param smallClassCd 소분류 코드
     * @param mdmCd 매체 코드
     * @param svcCd 서비스 코드
     * @return 삭제 결과
     */
//    DeleteServiceCode deleteServiceCode(String largeClassCd, String aveClassCd, String smallClassCd, String mdmCd, String svcCd);
    
    /**
     * 서비스코드 일괄 업데이트
     * 특정 서비스코드를 기준으로 같은 계층(같은 매체코드)의 모든 서비스코드 속성을 일괄 변경
     *
     * @param svcCd 일괄 업데이트 기준 서비스 코드
     * @param modpersId 편집자 ID
     * @return 서비스코드 일괄 업데이트 결과
     */
    BatchUpdateServiceCode batchUpdateServiceCode(String svcCd, String modpersId);
}
