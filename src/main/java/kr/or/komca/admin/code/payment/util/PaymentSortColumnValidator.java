package kr.or.komca.admin.code.payment.util;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

/**
 * Sort Validator
 */
@Slf4j
@Component
public class PaymentSortColumnValidator {

    /**
     * 허용된 정렬 컬럼 매핑 정보
     * Key: 프론트엔드 요청 파라미터명
     * Value: 실제 DB 테이블의 컬럼명
     */
    private static final Map<String, String> ALLOWED_SORT_COLUMNS_MAP = new HashMap<>() {
        {
            put("suppDedctGbn", "supp_dedct_gbn");      // 지급공제 구분
            put("suppDedctCd", "supp_dedct_cd");        // 지급공제 코드
            put("chgNm", "chg_nm");                    // 변경항목명
            put("retirYn", "retir_yn");                // 퇴직적용여부
            put("chgAmt", "chg_amt");                  // 변동 금액
            put("empinsYn", "empins_yn");              // 고용보험포함여부
            put("perCaltnYn", "perCalc_yn");           // 1할계산여부
            put("unitCaltn", "unit_caltn");            // 단위계산
            put("acctCd", "acct_cd");                  // 계정 코드
            put("remak", "remak");                     // 비고
            put("useYn", "use_yn");                    // 사용구분
            put("inspersId", "inspers_id");            // 등록자 ID
            put("insDt", "ins_dt");                    // 등록일시
            put("modpersId", "modpers_id");            // 수정자 ID
            put("modDt", "mod_dt");                    // 수정일시
            put("acctNm", "acct_nm");                  // 계정명
            put("suppDedctCdNm", "supp_dedct_cd_nm");  // 지급공제명
            put("suppDedctGbnNm", "supp_dedct_gbn_nm");// 지급공제 구분
            put("unitCaltnNm", "unit_caltn_nm");// 지급공제 구분
        }
    };

    /**
     * 정렬 컬럼 유효성 검사 및 매핑
     */
    public static String validate(String sortColumn) {
        return ALLOWED_SORT_COLUMNS_MAP.get(sortColumn);
    }
}