package kr.or.komca.admin.code.common.service.query;

import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import kr.or.komca.admin.code.common.dto.query.condition.DetailCodeSearchCondition;
import kr.or.komca.admin.code.common.dto.query.condition.ParCodeSearchCondition;
import kr.or.komca.admin.code.common.dto.query.response.DetailCode;
import kr.or.komca.admin.code.common.dto.query.response.ParCode;
import kr.or.komca.admin.code.common.mapper.query.CommonCodeQueryMapper;
import kr.or.komca.common.exception.core.CommonException;
import kr.or.komca.common.exception.response.error.code.CommonErrorCode;
import kr.or.komca.admin.code.common.enums.errorcode.CommonCodeErrorCode;
import kr.or.komca.common.utils.core.dto.response.PageListResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Slf4j
@RequiredArgsConstructor
@Service
@Transactional(readOnly = true)
public class CommonCodeQueryServiceImpl implements CommonCodeQueryService {

    private final CommonCodeQueryMapper commonCodeQueryMapper;

    @Override
    public PageListResponse<ParCode> getParCodeList(ParCodeSearchCondition condition) {
        log.info("상위 코드 목록 조회 요청 - page: {}, pageSize: {}", condition.getPage(), condition.getPageSize());
        
        try (Page<ParCode> page = PageHelper.startPage(condition.getPage(), condition.getPageSize())) {
            List<ParCode> parCodeList = commonCodeQueryMapper.getParCodeList(condition);

            PageInfo<ParCode> pageInfo = new PageInfo<>(parCodeList);
            
            PageListResponse<ParCode> result = PageListResponse.<ParCode>builder()
                    .contents(pageInfo.getList())
                    .totalElements(pageInfo.getTotal())
                    .page(pageInfo.getPageNum())
                    .totalPages(pageInfo.getPages())
                    .pageSize(pageInfo.getPageSize())
                    .build();
            
            return result;

        } catch (Exception e) {
            log.error("상위 코드 목록 조회 실패 - 페이징 처리 오류 발생", e);
            throw new CommonException(CommonErrorCode.INTERNAL_SERVER_ERROR, "페이징 처리 오류 발생");
        }
    }

    @Override
    public PageListResponse<DetailCode> getDetailCodeList(String parCd, DetailCodeSearchCondition condition) {
        log.info("세부 코드 목록 조회 요청 - parCd: {}, page: {}, pageSize: {}", 
                parCd, condition.getPage(), condition.getPageSize());
        
        validateParCodeExists(parCd);

        try (Page<ParCode> page = PageHelper.startPage(condition.getPage(), condition.getPageSize())) {
            List<DetailCode> detailCodes = commonCodeQueryMapper.getDetailCodeList(parCd, condition);

            PageInfo<DetailCode> pageInfo = new PageInfo<>(detailCodes);
            
            PageListResponse<DetailCode> result = PageListResponse.<DetailCode>builder()
                    .contents(pageInfo.getList())
                    .totalElements(pageInfo.getTotal())
                    .page(pageInfo.getPageNum())
                    .totalPages(pageInfo.getPages())
                    .pageSize(pageInfo.getPageSize())
                    .build();
            
            return result;
        }
    }

    @Override
    @Transactional(readOnly = true)
    public void validateParCodeExists(String parCd) {
        
        ParCode parCode = commonCodeQueryMapper.getParCodeById(parCd);

        if (parCode == null) {
            log.error("상위 코드 존재 여부 검증 실패 - 존재하지 않는 코드: {}", parCd);
            throw new CommonException(
                CommonCodeErrorCode.PAR_CODE_NOT_FOUND,
                String.format("존재하지 않는 상위 코드입니다: %s", parCd)
            );
        }
        
    }

    @Override
    @Transactional(readOnly = true)
    public void validateParCodeDuplicate(String parCd) {
        ParCode parCode = commonCodeQueryMapper.getParCodeById(parCd);

        if (parCode != null) {
            throw new CommonException(
                CommonCodeErrorCode.PAR_CODE_ALREADY_EXISTS,
                String.format("이미 존재하는 상위 코드입니다: %s", parCd)
            );
        }
    }

    @Override
    @Transactional(readOnly = true)
    public void validateNoDetailCode(String parCd) {
        int detailCodeCount = commonCodeQueryMapper.countDetailCode(parCd);

        if (detailCodeCount > 0) {
            throw new CommonException(
                CommonCodeErrorCode.CANNOT_DELETE_PAR_CODE_WITH_DETAIL,
                String.format("하위 코드가 존재하여 상위 코드를 삭제할 수 없습니다: %s", parCd)
            );
        }
    }

    @Override
    @Transactional(readOnly = true)
    public void validateDetailCodeExists(String parCd, String cd) {
        DetailCode detailCodeById = commonCodeQueryMapper.getDetailCodeById(parCd, cd);

        if (detailCodeById == null) {
            throw new CommonException(
                CommonCodeErrorCode.DETAIL_CODE_NOT_FOUND,
                String.format("존재하지 않는 세부 코드입니다: 상위코드=%s, 코드=%s", parCd, cd)
            );
        }
    }

    @Override
    @Transactional(readOnly = true)
    public void validateDetailCodeDuplicate(String parCd, String cd) {
        DetailCode detailCodeById = commonCodeQueryMapper.getDetailCodeById(parCd, cd);

        if (detailCodeById != null) {
            throw new CommonException(
                CommonCodeErrorCode.DETAIL_CODE_ALREADY_EXISTS,
                String.format("이미 존재하는 세부 코드입니다: 상위코드=%s, 코드=%s", parCd, cd)
            );
        }
    }

    @Override
    public ParCode getParCodeById(String parCd) {
        return commonCodeQueryMapper.getParCodeById(parCd);
    }

    @Override
    public DetailCode getDetailCodeById(String parCd, String cd) {
        return commonCodeQueryMapper.getDetailCodeById(parCd, cd);
    }

    @Override
    public boolean existsParCodeDept(String parCd, String deptCd) {
        log.info("상위 코드 부서 매핑 존재 여부 확인 - parCd: {}, deptCd: {}", parCd, deptCd);
        
        boolean exists = commonCodeQueryMapper.existsParCodeGroupUser(parCd, deptCd);
        
        log.info("상위 코드 부서 매핑 존재 여부 확인 완료 - parCd: {}, deptCd: {}, exists: {}", parCd, deptCd, exists);
        return exists;
    }

    @Override
    public void validateParCodeGroupUserDuplicate(String parCd, String deptCd) {
        boolean exists = existsParCodeDept(parCd, deptCd);
        if (exists) {
            log.error("상위 코드 부서 매핑 중복 - parCd: {}, deptCd: {}", parCd, deptCd);
            throw new CommonException(CommonCodeErrorCode.PAR_CODE_NOT_FOUND,
                    String.format("이미 존재하는 상위 코드 부서 매핑입니다. parCd: %s, deptCd: %s", parCd, deptCd));
        }
    }
}
