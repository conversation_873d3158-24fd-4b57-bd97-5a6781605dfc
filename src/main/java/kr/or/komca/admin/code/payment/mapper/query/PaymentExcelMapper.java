package kr.or.komca.admin.code.payment.mapper.query;

import kr.or.komca.admin.code.payment.dto.query.condition.PaymentExcelCondition;
import kr.or.komca.admin.code.payment.dto.query.response.PaymentExcel;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface PaymentExcelMapper {

    /**
     * 지급공제 목록 엑셀 조회
     * @param condition 검색 조건
     * @return 지급공제 목록
     */
    List<PaymentExcel> getPaymentExcelList(@Param("condition") PaymentExcelCondition condition);
}
