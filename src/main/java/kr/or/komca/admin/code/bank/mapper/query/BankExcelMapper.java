package kr.or.komca.admin.code.bank.mapper.query;

import kr.or.komca.admin.code.bank.dto.query.condition.BankExcelCondition;
import kr.or.komca.admin.code.bank.dto.query.response.BankExcel;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface BankExcelMapper {

    /**
     * 은행 목록 엑셀 조회
     * @param condition 검색 조건
     * @return 은행 목록
     */
    List<BankExcel> getBankExcelList(@Param("condition") BankExcelCondition condition);
}
