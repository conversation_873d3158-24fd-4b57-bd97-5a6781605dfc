package kr.or.komca.admin.code.mdm.dto.command.request;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import kr.or.komca.admin.code.mdm.dto.command.request.sub.CreateSmallCode;
import kr.or.komca.admin.code.mdm.dto.command.request.sub.UpdateSmallCode;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.ToString;

import java.util.List;


/**
 * 소분류 코드 통합 업데이트 요청 (공통 필드 분리)
 */
@Getter
@Builder
@AllArgsConstructor
@ToString
@Schema(description = "소분류 코드 통합 업데이트 요청 (C/U/D)")
public class ManageSmallCodeRequest {

	/** 대분류 코드 (공통) */
	@Schema(description = "대분류코드", example = "A", minLength = 1, maxLength = 1)
	@NotBlank
	@Size(min = 1, max = 1)
	private String largeClassCd;

	/** 중분류 코드 (공통) */
	@Schema(description = "중분류코드", example = "AA", minLength = 2, maxLength = 2)
	@NotBlank
	@Size(min = 2, max = 2)
	private String aveClassCd;

	@Schema(description = "생성 목록")
	private List<@Valid CreateSmallCode> create;

	@Schema(description = "수정 목록")
	private List<@Valid UpdateSmallCode> update;

	@Schema(description = "삭제 목록")
	private List<String> delete;
}
