package kr.or.komca.admin.code.payment.mapper.command;

import kr.or.komca.admin.code.payment.dto.command.request.sub.CreatePayment;
import kr.or.komca.admin.code.payment.dto.command.request.sub.UpdatePayment;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface PaymentCommandMapper {

    /**
     * 지급공제 저장
     *
     * @param suppDedctCd 지급공제 코드
     * @param command 저장할 지급공제 정보
     * @return 저장된 행 수
     */
    int createPayment(
            @Param("suppDedctCd") String suppDedctCd,
            @Param("inspersId") String inspersId,
            @Param("command") CreatePayment command
    );
    
    /**
     * 지급공제 수정
     * @param suppDedctCd 지급공제 코드
     * @param command 수정할 지급공제 정보
     * @return 수정된 행 수
     */
    int updatePayment(
            @Param("suppDedctCd") String suppDedctCd,
            @Param("modpersId") String modpersId,
            @Param("command") UpdatePayment command
    );
    
    /**
     * 지급공제 삭제
     * @param suppDedctCd 지급공제 코드
     * @return 삭제된 행 수
     */
    int deletePayment(@Param("suppDedctCd") String suppDedctCd);
}
