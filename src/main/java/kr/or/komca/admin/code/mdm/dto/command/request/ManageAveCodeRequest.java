package kr.or.komca.admin.code.mdm.dto.command.request;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import kr.or.komca.admin.code.mdm.dto.command.request.sub.CreateAveCode;
import kr.or.komca.admin.code.mdm.dto.command.request.sub.UpdateAveCode;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.ToString;

import java.util.List;


/**
 * 중분류 코드 통합 업데이트 요청 (공통 필드 분리)
 */
@Getter
@Builder
@AllArgsConstructor
@ToString
@Schema(description = "중분류 코드 통합 업데이트 요청 (C/U/D)")
public class ManageAveCodeRequest {

	/** 대분류 코드 (공통) */
	@Schema(description = "대분류코드", example = "A", minLength = 1, maxLength = 1)
	@NotBlank
	@Size(min = 1, max = 1)
	private String largeClassCd;

	@Schema(description = "생성 목록")
	private List<@Valid CreateAveCode> create;

	@Schema(description = "수정 목록")
	private List<@Valid UpdateAveCode> update;

	@Schema(description = "삭제 목록")
	private List<String> delete;
}
