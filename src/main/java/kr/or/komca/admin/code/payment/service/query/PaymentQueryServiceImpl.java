package kr.or.komca.admin.code.payment.service.query;

import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import kr.or.komca.admin.code.payment.dto.query.condition.PaymentSearchCondition;
import kr.or.komca.admin.code.payment.dto.query.response.Payment;
import kr.or.komca.admin.code.payment.enums.errorcode.PaymentErrorCode;
import kr.or.komca.admin.code.payment.mapper.query.PaymentQueryMapper;
import kr.or.komca.common.exception.core.CommonException;
import kr.or.komca.common.exception.response.error.code.CommonErrorCode;
import kr.or.komca.common.utils.core.dto.response.PageListResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Slf4j
@RequiredArgsConstructor
@Service
@Transactional(readOnly = true)
public class PaymentQueryServiceImpl implements PaymentQueryService {

    private final PaymentQueryMapper paymentQueryMapper;

    @Override
    public PageListResponse<Payment> getPaymentList(PaymentSearchCondition condition) {
        try (Page<Payment> page = PageHelper.startPage(condition.getPage(), condition.getPageSize())) {
            List<Payment> paymentList = paymentQueryMapper.getPaymentList(condition);

            PageInfo<Payment> pageInfo = new PageInfo<>(paymentList);

            return PageListResponse.<Payment>builder()
                .contents(pageInfo.getList())
                .totalElements(pageInfo.getTotal())
                .page(pageInfo.getPageNum())
                .totalPages(pageInfo.getPages())
                .pageSize(pageInfo.getPageSize())
                .build();

        } catch (Exception e) {
            log.error("페이징 처리 오류 발생", e);
            throw new CommonException(CommonErrorCode.INTERNAL_SERVER_ERROR, "페이징 처리 오류 발생");
        }
    }

    @Override
    public void validateExistPayment(String suppDedctCd) {
        Payment payment = paymentQueryMapper.getPaymentByCd(suppDedctCd);

        if (payment == null) {
            throw new CommonException(
                PaymentErrorCode.PAYMENT_NOT_FOUND,
                String.format("존재하지 않는 지급공제입니다: %s", suppDedctCd)
            );
        }
    }

    @Override
    public void validatePaymentDuplicate(String suppDedctCd) {
        Payment payment = paymentQueryMapper.getPaymentByCd(suppDedctCd);

        if (payment != null) {
            throw new CommonException(
                PaymentErrorCode.PAYMENT_ALREADY_EXISTS,
                String.format("이미 존재하는 지급공제입니다: %s", suppDedctCd)
            );
        }
    }
}
