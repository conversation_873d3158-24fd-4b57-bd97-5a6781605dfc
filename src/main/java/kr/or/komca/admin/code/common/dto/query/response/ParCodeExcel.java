package kr.or.komca.admin.code.common.dto.query.response;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

import java.time.LocalDateTime;

/**
 * 상위 코드 응답 DTO
 */
@ToString
@Getter
@AllArgsConstructor
@NoArgsConstructor
@Builder(toBuilder = true)
public class ParCodeExcel {
    /** 상위 코드 */
    @Schema(description = "상위 코드", example = "MB001")
    private String parCd;
    
    /** 상위 코드명 */
    @Schema(description = "상위 코드명", example = "회원 상태")
    private String parCdNm;
    
    /** 비고 */
    @Schema(description = "비고", example = "회원 상태 코드")
    private String remak;
    
    /** 사용 여부 */
    @Schema(description = "사용 여부", example = "Y")
    private String useYn;
    
    /** 등록자 ID */
    @Schema(description = "등록자 ID", example = "admin")
    private String inspersId;
    
    /** 등록일시 */
    @Schema(description = "등록일시", example = "2024-05-01T10:30:00")
    private LocalDateTime insDt;
    
    /** 수정자 ID */
    @Schema(description = "수정자 ID", example = "admin")
    private String modpersId;
    
    /** 수정일시 */
    @Schema(description = "수정일시", example = "2024-05-02T15:45:00")
    private LocalDateTime modDt;
}