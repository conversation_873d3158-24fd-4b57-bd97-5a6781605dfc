package kr.or.komca.admin.code.mdm.service.query;

import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import kr.or.komca.admin.code.mdm.dto.query.condition.MdmCodeHistorySearchCondition;
import kr.or.komca.admin.code.mdm.dto.query.response.MdmCodeHistory;
import kr.or.komca.admin.code.mdm.mapper.query.MdmCodeHistoryQueryMapper;
import kr.or.komca.common.utils.core.dto.response.PageListResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;
import kr.or.komca.admin.code.mdm.enums.MdmCodeHistoryChangeType;

/**
 * 매체코드 이력 조회 서비스 구현체
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class MdmCodeHistoryQueryServiceImpl implements MdmCodeHistoryQueryService {

    private final MdmCodeHistoryQueryMapper mdmCodeHistoryQueryMapper;

    @Override
    @Transactional(readOnly = true)
    public PageListResponse<MdmCodeHistory> getMdmCodeHistoryList(MdmCodeHistorySearchCondition condition) {
        log.info("매체코드 이력 목록 조회 요청 - condition: {}", condition);

        try (Page<MdmCodeHistory> page = PageHelper.startPage(condition.getPage(), condition.getPageSize())) {
            List<MdmCodeHistory> historyList = mdmCodeHistoryQueryMapper.getMdmCodeHistoryList(condition);

            // 변경된 컬럼을 파싱하여 재매핑
            List<MdmCodeHistory> convertedList = historyList.stream()
                    .map(history -> {
                        // 변경된 문자열 정보(string, String, ...)를 리스트로 변환
                        List<String> columnsList = convertChangedColumnsString(history.getChangedColumnsString());
                        return history.toBuilder()
                                .changedColumns(columnsList)
                                .build();
                    })
                    .collect(Collectors.toList());

            PageInfo<MdmCodeHistory> pageInfo = new PageInfo<>(convertedList);

            PageListResponse<MdmCodeHistory> result = PageListResponse.<MdmCodeHistory>builder()
                    .contents(convertedList)
                    .totalElements(pageInfo.getTotal())
                    .page(pageInfo.getPageNum())
                    .totalPages(pageInfo.getPages())
                    .pageSize(pageInfo.getPageSize())
                    .build();

            log.info("매체코드 이력 목록 조회 완료 - 조회 건수: {}", result.getTotalElements());

            return result;
        }
    }

    /**
     * 변경 컬럼의 콤마 구분 문자열을 List로 변환
     *
     * @param changedColumnsString (s, s, s) 형태의 변경 컬럼 문자열
     * @return List로 변환된 변경 컬럼 문자열)
     */
    private List<String> convertChangedColumnsString(String changedColumnsString) {
        if (changedColumnsString == null || changedColumnsString.trim().isEmpty()) {
            return List.of();
        }

        // ',' 을 기준으로 리스트로 변환 -> 앞뒤 공백 제거 -> 공백 요소 제거 -> 리스트 매핑
        return Arrays.stream(changedColumnsString.split(","))
                .map(String::trim)
                .filter(s -> !s.isEmpty())
                .collect(Collectors.toList());
    }
}