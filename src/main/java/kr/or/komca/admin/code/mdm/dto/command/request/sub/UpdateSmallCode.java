package kr.or.komca.admin.code.mdm.dto.command.request.sub;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Pattern;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.ToString;

/**
 * 소분류 코드 수정 요청
 */
@Getter
@Builder
@AllArgsConstructor
@ToString
@Schema(description = "소분류 코드 수정 요청")
public class UpdateSmallCode {

    /** 소분류코드 */
    @Schema(description = "소분류코드", example = "AA01", minLength = 4, maxLength = 4)
    @NotBlank
    @Size(min = 4, max = 4)
    private String smallClassCd;

    /** 소분류명(0-30자) */
    @Schema(description = "소분류명", example = "TV", minLength = 0, maxLength = 30)
    @NotBlank
    @Size(max = 30)
    private String smallClassNm;

    /** 사용여부 */
    @Schema(description = "사용여부", example = "Y", allowableValues = {"Y", "N"})
    @NotNull
    @Pattern(regexp = "^[YN]$")
    private String useYn;

    /** 정렬순서 */
    @Schema(description = "정렬순서", example = "1")
    @NotNull
    private Integer sortOrd;
}
