package kr.or.komca.admin.code.payment.dto.command.request;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.Valid;
import kr.or.komca.admin.code.payment.dto.command.request.sub.CreatePayment;
import kr.or.komca.admin.code.payment.dto.command.request.sub.UpdatePayment;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.ToString;

import java.util.List;

/**
 * 지급공제 통합 관리 요청 DTO
 */
@Getter
@Builder
@AllArgsConstructor
@ToString
@Schema(description = "지급공제 통합 관리 요청 (C/U/D)")
public class ManagePaymentRequest {

    @Schema(description = "생성 목록")
    private List<@Valid CreatePayment> create;

    @Schema(description = "수정 목록")
    private List<@Valid UpdatePayment> update;

    @Schema(description = "삭제 목록 (삭제 지급공제 리스트)")
    private List<String> delete;
}
