package kr.or.komca.admin.code.mdm.dto.command.request.sub;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Pattern;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.ToString;

/**
 * 중분류 코드 수정 요청
 */
@Getter
@Builder
@AllArgsConstructor
@ToString
@Schema(description = "중분류 코드 수정 요청")
public class UpdateAveCode {

    /** 중분류코드 */
    @Schema(description = "중분류코드", example = "AA", minLength = 2, maxLength = 2)
    @NotBlank
    @Size(min = 2, max = 2)
    private String aveClassCd;

    /** 중분류명(0-50자) */
    @Schema(description = "중분류명", example = "지상파방송", minLength = 0, maxLength = 50)
    @NotBlank
    @Size(max = 50)
    private String aveClassNm;

    /** 국내계정코드 */
    @Schema(description = "국내계정코드", example = "0845")
    private String domAcctCd;

    /** 국외계정코드 */
    @Schema(description = "국외계정코드", example = "0846")
    private String abrAcctCd;

    /** 국내지부계정코드 */
    @Schema(description = "국내지부계정코드", example = "0845")
    private String domBranAcctCd;

    /** 국외지부계정코드 */
    @Schema(description = "국외지부계정코드", example = "0846")
    private String abrBranAcctCd;

    /** 사용여부 */
    @Schema(description = "사용여부", example = "Y", allowableValues = {"Y", "N"})
    @NotNull
    @Pattern(regexp = "^[YN]$")
    private String useYn;

    /** 정렬순서 */
    @Schema(description = "정렬순서", example = "1")
    @NotNull
    private Integer sortOrd;
}
