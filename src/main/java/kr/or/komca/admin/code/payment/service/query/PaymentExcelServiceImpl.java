package kr.or.komca.admin.code.payment.service.query;

import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import kr.or.komca.admin.code.payment.dto.query.condition.PaymentExcelCondition;
import kr.or.komca.admin.code.payment.dto.query.response.PaymentExcel;
import kr.or.komca.admin.code.payment.mapper.query.PaymentExcelMapper;
import kr.or.komca.common.exception.core.CommonException;
import kr.or.komca.common.exception.response.error.code.CommonErrorCode;
import kr.or.komca.common.utils.core.code.common.service.query.CommonCodeModuleQueryService;
import kr.or.komca.common.utils.core.dto.response.PageListResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

@Slf4j
@RequiredArgsConstructor
@Service
public class PaymentExcelServiceImpl implements PaymentExcelService {

    private final PaymentExcelMapper paymentExcelMapper;
    private final CommonCodeModuleQueryService commonCodeModuleQueryService;

    // -------------- EXCEL ----------------

    @Override
    public PageListResponse<PaymentExcel> getPaymentExcelList(PaymentExcelCondition condition) {
        try (Page<PaymentExcel> page = PageHelper.startPage(condition.getPage(), condition.getPageSize())) {
            List<PaymentExcel> parCodeList = paymentExcelMapper.getPaymentExcelList(condition);

            PageInfo<PaymentExcel> pageInfo = new PageInfo<>(parCodeList);

            return PageListResponse.<PaymentExcel>builder()
                    .contents(pageInfo.getList())
                    .totalElements(pageInfo.getTotal())
                    .page(pageInfo.getPageNum())
                    .totalPages(pageInfo.getPages())
                    .pageSize(pageInfo.getPageSize())
                    .build();

        } catch (Exception e) {
            log.error("페이징 처리 오류 발생", e);
            throw new CommonException(CommonErrorCode.INTERNAL_SERVER_ERROR, "페이징 처리 오류 발생");
        }
    }
}
