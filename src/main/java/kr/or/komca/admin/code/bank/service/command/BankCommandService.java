package kr.or.komca.admin.code.bank.service.command;

import kr.or.komca.admin.code.bank.dto.command.request.ManageBankRequest;
import kr.or.komca.admin.code.bank.dto.command.response.ManageBank;

public interface BankCommandService {

    /**
     * 은행 통합 관리
     *
     * @param request 통합 관리 요청
     * @param inspersId 사용자 ID
     * @return 관리 결과
     */
    ManageBank manageBank(ManageBankRequest request, String inspersId);
}
