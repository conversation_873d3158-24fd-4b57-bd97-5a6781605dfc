package kr.or.komca.admin.code.mdm.service.command;

import kr.or.komca.admin.code.mdm.dto.command.request.*;
import kr.or.komca.admin.code.mdm.dto.command.request.sub.*;
import kr.or.komca.admin.code.mdm.dto.command.response.*;
import kr.or.komca.admin.code.mdm.dto.query.response.MdmCode;
import kr.or.komca.admin.code.mdm.enums.errorcode.MdmCodeErrorCode;
import kr.or.komca.admin.code.mdm.mapper.command.MdmCodeCommandMapper;
import kr.or.komca.admin.code.mdm.service.query.MdmCodeQueryService;
import kr.or.komca.common.exception.core.CommonException;
import kr.or.komca.common.exception.response.error.code.CommonErrorCode;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.List;
import java.util.Objects;

/**
 * 매체 코드 Command 서비스 구현
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class MdmCodeCommandServiceImpl implements MdmCodeCommandService {

    private final MdmCodeCommandMapper mdmCodeCommandMapper;
    private final MdmCodeQueryService mdmCodeQueryService;
    private final MdmCodeHistoryCommandService mdmCodeHistoryCommandService;
    private final MngComisCommandService mngComisCommandService;

    // Todo: 삭제된(논리) ID로 Create 요청 시 오류/처리 결정 필요 (논리적으로 삭제되어도 row는 남아있으므로)
    @Override
    @Transactional
    public ManageLargeCode manageLargeCode(ManageLargeCodeRequest command, String editpersId) {

        log.info("대분류 코드 관리 시작 - 생성: {}건, 수정: {}건, 삭제: {}건", 
            command.getCreate() != null ? command.getCreate().size() : 0,
            command.getUpdate() != null ? command.getUpdate().size() : 0,
            command.getDelete() != null ? command.getDelete().size() : 0);

        // 각 작업 수행 결과
        int createAffectedRows = 0;
        int updateAffectedRows = 0;
        int deleteAffectedRows = 0;

        List<CreateLargeCode> create = command.getCreate();

        // create 요청이 존재한다면 대분류 생성
        if (create != null && !create.isEmpty()) {
            log.info("대분류 코드 생성 작업 시작 - 대상: {}건", create.size());
            for (CreateLargeCode createLargeCode : create) {
                createAffectedRows += createLargeCode(createLargeCode);
            }
        }

        // update 요청이 존재한다면 대분류 수정
        List<UpdateLargeCode> update = command.getUpdate();

        if (update != null && !update.isEmpty()) {
            log.info("대분류 코드 수정 작업 시작 - 대상: {}건", update.size());
            for (UpdateLargeCode updateLargeCode : update) {
                updateAffectedRows += updateLargeCode(updateLargeCode);
            }
        }

        // delete 요청이 존재한다면 대분류 삭제
        List<String> delete = command.getDelete();

        if (delete != null && !delete.isEmpty()) {
            for (String largeClassCd : delete) {
                deleteAffectedRows += deleteLargeCode(largeClassCd, editpersId);
            }
        }

        log.info("대분류 코드 관리 완료 - 생성: {}건, 수정: {}건, 삭제: {}건", createAffectedRows, updateAffectedRows, deleteAffectedRows);

        return ManageLargeCode.builder()
                .createAffectedRows(createAffectedRows)
                .updateAffectedRows(updateAffectedRows)
                .deleteAffectedRows(deleteAffectedRows)
                .build();
    }

    @Override
    @Transactional
    public ManageAveCode manageAveCode(ManageAveCodeRequest command, String editpersId) {

        log.info("중분류 코드 관리 시작 - 생성: {}건, 수정: {}건, 삭제: {}건, editpersId: {}",
                command.getCreate() != null ? command.getCreate().size() : 0,
                command.getUpdate() != null ? command.getUpdate().size() : 0,
                command.getDelete() != null ? command.getDelete().size() : 0,
                editpersId);

        String largeClassCd = command.getLargeClassCd();

        // 각 작업 수행 결과
        int createAffectedRows = 0;
        int updateAffectedRows = 0;
        int deleteAffectedRows = 0;

        // 대분류 코드 계층 유효성 확인
        mdmCodeQueryService.validateMdmCodeHierarchy(largeClassCd, null, null, null, null);

        List<CreateAveCode> create = command.getCreate();

        // create 요청이 존재한다면 중분류 생성
        if (create != null && !create.isEmpty()) {
            log.info("중분류 코드 생성 작업 시작 - 대상: {}건", create.size());
            for (CreateAveCode createAveCode : create) {
                createAffectedRows += createAveCode(
                        largeClassCd,
                        createAveCode
                );
            }
        }

        List<UpdateAveCode> update = command.getUpdate();

        // update 요청이 존재한다면 중분류 수정
        if (update != null && !update.isEmpty()) {
            log.info("중분류 코드 수정 작업 시작 - 대상: {}건", update.size());
            for (UpdateAveCode updateAveCode : update) {
                updateAffectedRows += updateAveCode(updateAveCode);
            }
        }

        List<String> delete = command.getDelete();

        // delete 요청이 존재한다면 대분류 삭제
        if (delete != null && !delete.isEmpty()) {
            for (String aveClassCd : delete) {
                deleteAffectedRows += deleteAveCode(aveClassCd, editpersId);
            }
        }

        log.info("중분류 코드 관리 완료 - 생성: {}건, 수정: {}건, 삭제: {}건", createAffectedRows, updateAffectedRows, deleteAffectedRows);

        return ManageAveCode.builder()
                .createAffectedRows(createAffectedRows)
                .updateAffectedRows(updateAffectedRows)
                .deleteAffectedRows(deleteAffectedRows)
                .build();
    }

    @Override
    @Transactional
    public ManageSmallCode manageSmallCode(ManageSmallCodeRequest command, String editpersId) {
        log.info("소분류 코드 관리 시작 - 생성: {}건, 수정: {}건, 삭제: {}건, editpersId: {}",
                command.getCreate() != null ? command.getCreate().size() : 0,
                command.getUpdate() != null ? command.getUpdate().size() : 0,
                command.getDelete() != null ? command.getDelete().size() : 0,
                editpersId);

        String largeClassCd = command.getLargeClassCd();
        String aveClassCd = command.getAveClassCd();

        // 각 작업 수행 결과
        int createAffectedRows = 0;
        int updateAffectedRows = 0;
        int deleteAffectedRows = 0;

        // 대분류/중분류 코드 계층 유효성 확인
        mdmCodeQueryService.validateMdmCodeHierarchy(largeClassCd, aveClassCd, null, null, null);

        List<CreateSmallCode> create = command.getCreate();

        // create 요청이 존재한다면 소분류 생성
        if (create != null && !create.isEmpty()) {
            log.info("소분류 코드 생성 작업 시작 - 대상: {}건", create.size());
            for (CreateSmallCode createSmallCode : create) {
                createAffectedRows += createSmallCode(
                        largeClassCd,
                        aveClassCd,
                        createSmallCode
                );
            }
        }

        List<UpdateSmallCode> update = command.getUpdate();

        // update 요청이 존재한다면 소분류 수정
        if (update != null && !update.isEmpty()) {
            log.info("소분류 코드 수정 작업 시작 - 대상: {}건", update.size());
            for (UpdateSmallCode updateSmallCode : update) {
                updateAffectedRows += updateSmallCode(updateSmallCode.getSmallClassCd(), updateSmallCode);
            }
        }

        List<String> delete = command.getDelete();

        // delete 요청이 존재한다면 대분류 삭제 수행
        if (delete != null && !delete.isEmpty()) {
            for (String smallClassCd : delete) {
                deleteAffectedRows += deleteSmallCode(smallClassCd, editpersId);
            }
        }

        log.info("소분류 코드 관리 완료 - 생성: {}건, 수정: {}건, 삭제: {}건", createAffectedRows, updateAffectedRows, deleteAffectedRows);

        return ManageSmallCode.builder()
                .createAffectedRows(createAffectedRows)
                .updateAffectedRows(updateAffectedRows)
                .deleteAffectedRows(deleteAffectedRows)
                .build();
    }

    @Override
    @Transactional
    public ManageMdmCode manageMdmCode(ManageMdmCodeRequest command, String editpersId) {
        log.info("매체 코드 관리 시작 - 생성: {}건, 수정: {}건, 삭제: {}건, editpersId: {}",
                command.getCreate() != null ? command.getCreate().size() : 0,
                command.getUpdate() != null ? command.getUpdate().size() : 0,
                command.getDelete() != null ? command.getDelete().size() : 0,
                editpersId);


        String largeClassCd = command.getLargeClassCd();
        String aveClassCd = command.getAveClassCd();
        String smallClassCd = command.getSmallClassCd();

        // 각 작업 수행 결과
        int createAffectedRows = 0;
        int updateAffectedRows = 0;
        int deleteAffectedRows = 0;

        // 대분류/중분류/소분류 코드 계층 유효성 확인
        mdmCodeQueryService.validateMdmCodeHierarchy(largeClassCd, aveClassCd, smallClassCd, null, null);

        List<CreateMdmCode> create = command.getCreate();

        // create 요청이 존재한다면 매체 생성
        if (create != null && !create.isEmpty()) {
            log.info("매체 코드 생성 작업 시작 - 대상: {}건", create.size());
            for (CreateMdmCode createMdmCode : create) {
                createAffectedRows += createMdmCode(
                        largeClassCd,
                        aveClassCd,
                        smallClassCd,
                        createMdmCode,
                        editpersId
                );
            }
        }

        List<UpdateMdmCode> update = command.getUpdate();

        // update 요청이 존재한다면 매체 수정
        if (update != null && !update.isEmpty()) {
            log.info("매체 코드 수정 작업 시작 - 대상: {}건", update.size());
            for (UpdateMdmCode updateMdmCode : update) {
                updateAffectedRows += updateMdmCode(
                        largeClassCd,
                        aveClassCd,
                        smallClassCd,
                        updateMdmCode,
                        editpersId
                );
            }
        }

        List<String> delete = command.getDelete();

        // delete 요청이 존재한다면 대분류 삭제 수행
        if (delete != null && !delete.isEmpty()) {
            for (String mdmCd : delete) {
                deleteAffectedRows += deleteMdmCode(mdmCd, editpersId);
            }
        }

        log.info("매체 코드 관리 완료 - 생성: {}건, 수정: {}건, 삭제: {}건", createAffectedRows, updateAffectedRows, deleteAffectedRows);

        return ManageMdmCode.builder()
                .createAffectedRows(createAffectedRows)
                .updateAffectedRows(updateAffectedRows)
                .deleteAffectedRows(deleteAffectedRows)
                .build();
    }

    @Override
    @Transactional
    public ManageServiceCode manageServiceCode(ManageServiceCodeRequest command, String editpersId) {
        log.info("서비스 코드 관리 시작 - 생성: {}건, 수정: {}건, 삭제: {}건, editpersId: {}",
                command.getCreate() != null ? command.getCreate().size() : 0,
                command.getUpdate() != null ? command.getUpdate().size() : 0,
                command.getDelete() != null ? command.getDelete().size() : 0,
                editpersId);

        String largeClassCd = command.getLargeClassCd();
        String aveClassCd = command.getAveClassCd();
        String smallClassCd = command.getSmallClassCd();
        String mdmCd = command.getMdmCd();

        // 각 작업 수행 결과
        int createAffectedRows = 0;
        int updateAffectedRows = 0;
        int deleteAffectedRows = 0;

        // 전체 계층 구조 유효성 확인
        mdmCodeQueryService.validateMdmCodeHierarchy(largeClassCd, aveClassCd, smallClassCd, mdmCd, null);

        List<CreateServiceCode> create = command.getCreate();

        // create 요청이 존재한다면 서비스 코드 생성
        if (create != null && !create.isEmpty()) {
            log.info("서비스 코드 생성 작업 시작 - 대상: {}건", create.size());
            for (CreateServiceCode createServiceCode : create) {
                createAffectedRows += createServiceCode(
                        largeClassCd,
                        aveClassCd,
                        smallClassCd,
                        mdmCd,
                        createServiceCode,
                        editpersId
                );
            }
        }

        List<UpdateServiceCode> update = command.getUpdate();

        // update 요청이 존재한다면 서비스 코드 수정
        if (update != null && !update.isEmpty()) {
            log.info("서비스 코드 수정 작업 시작 - 대상: {}건", update.size());
            for (UpdateServiceCode updateServiceCode : update) {
                updateAffectedRows += updateServiceCode(updateServiceCode.getSvcCd(), editpersId, updateServiceCode);
            }
        }

        List<String> delete = command.getDelete();

        // delete 요청이 존재한다면 서비스 코드 삭제
        if (delete != null && !delete.isEmpty()) {
            for (String svcCd : delete) {
                deleteAffectedRows += deleteServiceCode(svcCd, editpersId);
            }
        }

        log.info("서비스 코드 관리 완료 - 생성: {}건, 수정: {}건, 삭제: {}건", createAffectedRows, updateAffectedRows, deleteAffectedRows);

        return ManageServiceCode.builder()
                .createAffectedRows(createAffectedRows)
                .updateAffectedRows(updateAffectedRows)
                .deleteAffectedRows(deleteAffectedRows)
                .build();
    }

    @Override
    @Transactional
    public BatchUpdateServiceCode batchUpdateServiceCode(String baseSvcCd, String editpersId) {
        log.info("서비스코드 일괄 업데이트 시작 - baseSvcCd: {}, editpersId: {}", baseSvcCd, editpersId);

        // 서비스코드 존재 확인
        mdmCodeQueryService.validateServiceCodeExists(baseSvcCd);

        // 서비스코드의 매체코드(MDM) 조회
        String mdmCd = mdmCodeQueryService.findMdmCdByServiceCode(baseSvcCd);

        // 매체코드가 존재하지 않는다면 오류 반환
        if (mdmCd == null || mdmCd.isEmpty()) {
            log.warn("서비스코드 일괄 업데이트 실패 - 매체코드 없음: baseSvcCd: {}", baseSvcCd);
            throw new CommonException(MdmCodeErrorCode.MDM_CODE_NOT_FOUND,
                    String.format("서비스코드에 해당하는 매체코드를 찾을 수 없습니다. 서비스코드: %s", baseSvcCd));
        }

        // 서비스코드 일괄 업데이트
        int affectedRows = mdmCodeCommandMapper.batchUpdateServiceCodeByMdmCd(mdmCd, baseSvcCd, editpersId);

        // 응답 생성
        return BatchUpdateServiceCode.builder()
                .mdmCd(mdmCd)
                .baseSvcCd(baseSvcCd)
                .affectedRows(affectedRows)
                .build();
    }

    // ================== Private Methods ==================

    private int createLargeCode(CreateLargeCode request) {
        String largeClassCd = request.getLargeClassCd();
        log.info("대분류 코드 생성 시작 - largeClassCd: {}, largeClassNm: {}", largeClassCd, request.getLargeClassNm());

        // 대분류 코드 중복 확인
        mdmCodeQueryService.validateLargeCodeDuplicate(largeClassCd);

        // 코드 저장
        int affectedRows = mdmCodeCommandMapper.createLargeCode(request);

        if (affectedRows <= 0) {
            log.warn("대분류 코드 생성 실패 - largeClassCd: {}, affectedRows: {}", largeClassCd, affectedRows);
            throw new CommonException(CommonErrorCode.INTERNAL_SERVER_ERROR, "대분류 코드 생성에 실패했습니다.");
        }

        log.info("대분류 코드 생성 완료 - largeClassCd: {}, affectedRows: {}", largeClassCd, affectedRows);
        return affectedRows;
    }

    private int createAveCode(
            String largeClassCd,
            CreateAveCode request
    ) {
        String aveClassCd = request.getAveClassCd();

        // 중분류 코드 중복 확인
        mdmCodeQueryService.validateAveCodeDuplicate(aveClassCd);

        int affectedRows = mdmCodeCommandMapper.createAveCode(largeClassCd, request);

        if (affectedRows <= 0) {
            throw new CommonException(CommonErrorCode.INTERNAL_SERVER_ERROR, "중분류 코드 생성에 실패했습니다.");
        }

        return affectedRows;
    }

    private int createSmallCode(
            String largeClassCd,
            String aveClassCd,
            CreateSmallCode request
    ) {
        String smallClassCd = request.getSmallClassCd();

        // 소분류 코드 중복 확인
        mdmCodeQueryService.validateSmallCodeDuplicate(smallClassCd);

        int affectedRows = mdmCodeCommandMapper.createSmallCode(largeClassCd, aveClassCd, request);

        if (affectedRows <= 0) {
            throw new CommonException(CommonErrorCode.INTERNAL_SERVER_ERROR, "소분류 코드 생성에 실패했습니다.");
        }

        return affectedRows;
    }

    private int createMdmCode(
            String largeClassCd,
            String aveClassCd,
            String smallClassCd,
            CreateMdmCode request,
            String inspersId
    ) {
        String mdmCd = request.getMdmCd();

        // 매체 코드 중복 확인
        mdmCodeQueryService.validateMdmCodeDuplicate(mdmCd);

        // 매체 코드 생성
        int affectedRows = mdmCodeCommandMapper.createMdmCode(largeClassCd, aveClassCd, smallClassCd, inspersId, request);

        if (affectedRows <= 0) {
            throw new CommonException(CommonErrorCode.INTERNAL_SERVER_ERROR, "매체 코드 생성에 실패했습니다.");
        }

        // 요청에 포함된 관리 수수료율
        BigDecimal mngComisRate = request.getMngComisRate();

        // 관리수수료율이 요청에 존재 시
        if (mngComisRate != null) {
            // 입력받은 관리수수료율로 설정
            MdmCode mdmCodeForInsert = MdmCode.builder()
                    .largeClassCd(largeClassCd)
                    .aveClassCd(aveClassCd)
                    .smallClassCd(smallClassCd)
                    .mdmCd(mdmCd)
                    .mngComisRate(mngComisRate)
                    .build();

            // 관리수수료율 저장
            mngComisCommandService.upsertMngComisRate(mdmCodeForInsert);
        }

        // 생성 이력 저장
        mdmCodeHistoryCommandService.saveInsertHistory(mdmCd, inspersId);

        return affectedRows;
    }

    private int createServiceCode(
            String largeClassCd,
            String aveClassCd,
            String smallClassCd,
            String mdmCd,
            CreateServiceCode request,
            String inspersId
    ) {
        String svcCd = request.getSvcCd();

        // 서비스 코드 중복 확인
        mdmCodeQueryService.validateServiceCodeDuplicate(svcCd);

        int affectedRows = mdmCodeCommandMapper.createServiceCode(largeClassCd, aveClassCd, smallClassCd, mdmCd, request, inspersId);

        if (affectedRows <= 0) {
            throw new CommonException(CommonErrorCode.INTERNAL_SERVER_ERROR, "서비스 코드 생성에 실패했습니다.");
        }

        return affectedRows;
    }

    // ================== Private Update Methods ==================

    private int updateLargeCode(UpdateLargeCode request) {
        // 존재하는 코드인지 확인
        String largeClassCd = request.getLargeClassCd();

        mdmCodeQueryService.validateLargeCodeExists(largeClassCd);

        int affectedRows = mdmCodeCommandMapper.updateLargeCode(request);

        if (affectedRows <= 0) {
            throw new CommonException(CommonErrorCode.INTERNAL_SERVER_ERROR,
                    String.format("대분류 코드 수정에 실패했습니다. largeClassCd: %s", largeClassCd));
        }

        return affectedRows;
    }

    private int updateAveCode(UpdateAveCode request) {
        String aveClassCd = request.getAveClassCd();

        // 존재하는 코드인지 확인
        mdmCodeQueryService.validateAveCodeExists(aveClassCd);

        int affectedRows = mdmCodeCommandMapper.updateAveCode(aveClassCd, request);

        if (affectedRows <= 0) {
            throw new CommonException(CommonErrorCode.INTERNAL_SERVER_ERROR,
                    String.format("중분류 코드 수정에 실패했습니다. aveClassCd: %s", aveClassCd));
        }

        return affectedRows;
    }

    private int updateSmallCode(String smallClassCd, UpdateSmallCode request) {
        // 존재하는 코드인지 확인
        mdmCodeQueryService.validateSmallCodeExists(smallClassCd);

        int affectedRows = mdmCodeCommandMapper.updateSmallCode(smallClassCd, request);

        if (affectedRows <= 0) {
            throw new CommonException(CommonErrorCode.INTERNAL_SERVER_ERROR,
                    String.format("소분류 코드 수정에 실패했습니다. smallClassCd: %s", smallClassCd));
        }

        return affectedRows;
    }

    private int updateMdmCode(
            String largeClassCd,
            String aveClassCd,
            String smallClassCd,
            UpdateMdmCode request,
            String modpersId
    ) {
        String mdmCd = request.getMdmCd();

        // 존재하는 코드인지 확인
        mdmCodeQueryService.validateMdmCodeExists(mdmCd);

        // 수정 전 데이터 (기록 저장 시 변경 컬럼 감지를 위함)
        MdmCode beforeData = mdmCodeQueryService.findMdmCodeByMdmCd(mdmCd);

        // 매체 코드 수정 작업
        int affectedRows = mdmCodeCommandMapper.updateMdmCode(mdmCd, modpersId, request);

        // 관리수수료율
        BigDecimal mngComisRate = request.getMngComisRate();

        // 관리수수료율 변경 확인 및 업데이트 (이전 데이터와 다르다면 수행)
        if (isBigDecimalChanged(beforeData.getMngComisRate(), mngComisRate)) {
            MdmCode mdmCodeForUpdate = MdmCode.builder()
                    .largeClassCd(largeClassCd)
                    .aveClassCd(aveClassCd)
                    .smallClassCd(smallClassCd)
                    .mdmCd(mdmCd)
                    .mngComisRate(mngComisRate)
                    .build();
            // 관리수수료율 추가/변경
            mngComisCommandService.upsertMngComisRate(mdmCodeForUpdate);
        }

        // 매체코드 수정 실패 시 예외처리
        if (affectedRows <= 0) {
            throw new CommonException(CommonErrorCode.INTERNAL_SERVER_ERROR,
                    String.format("매체 코드 수정에 실패했습니다. mdmCd: %s", mdmCd));
        }

        // UPDATE 이력 저장
        mdmCodeHistoryCommandService.saveUpdateHistory(mdmCd, beforeData, modpersId);

        return affectedRows;
    }

    private int updateServiceCode(String svcCd, String modpersId, UpdateServiceCode request) {
        // 존재하는 코드인지 확인
        mdmCodeQueryService.validateServiceCodeExists(svcCd);

        int affectedRows = mdmCodeCommandMapper.updateServiceCode(svcCd, request, modpersId);

        if (affectedRows <= 0) {
            throw new CommonException(CommonErrorCode.INTERNAL_SERVER_ERROR,
                    String.format("서비스 코드 수정에 실패했습니다. svcCd: %s", svcCd));
        }

        return affectedRows;
    }

    // ================== Private Delete Methods ==================

    /**
     * 대분류 코드 논리적 삭제
     *
     * @param largeClassCd 대분류 코드
     * @param delpersId 편집자 ID
     * @return 삭제된 행 수
     */
    private int deleteLargeCode(String largeClassCd, String delpersId) {
        log.info("대분류 코드 삭제 시작 - largeClassCd: {}, delpersId: {}", largeClassCd, delpersId);
        
        // 존재하는 코드인지 확인
        mdmCodeQueryService.validateLargeCodeExists(largeClassCd);

        int affectedRows = mdmCodeCommandMapper.deleteLargeCode(largeClassCd, delpersId);

        if (affectedRows <= 0) {
            log.warn("대분류 코드 삭제 실패 - largeClassCd: {}, affectedRows: {}", largeClassCd, affectedRows);
            throw new CommonException(CommonErrorCode.INTERNAL_SERVER_ERROR,
                    String.format("대분류 코드 삭제에 실패했습니다. largeClassCd: %s", largeClassCd));
        }

        log.info("대분류 코드 삭제 완료 - largeClassCd: {}, affectedRows: {}", largeClassCd, affectedRows);
        return affectedRows;
    }

    /**
     * 중분류 코드 논리적 삭제
     *
     * @param aveClassCd 중분류 코드
     * @param delpersId 편집자 ID
     * @return 삭제된 행 수
     */
    private int deleteAveCode(String aveClassCd, String delpersId) {
        log.info("중분류 코드 삭제 시작 - aveClassCd: {}, delpersId: {}", aveClassCd, delpersId);
        
        // 존재하는 코드인지 확인
        mdmCodeQueryService.validateAveCodeExists(aveClassCd);

        int affectedRows = mdmCodeCommandMapper.deleteAveCode(aveClassCd, delpersId);

        if (affectedRows <= 0) {
            log.warn("중분류 코드 삭제 실패 - aveClassCd: {}, affectedRows: {}", aveClassCd, affectedRows);
            throw new CommonException(CommonErrorCode.INTERNAL_SERVER_ERROR,
                    String.format("중분류 코드 삭제에 실패했습니다. aveClassCd: %s", aveClassCd));
        }

        log.info("중분류 코드 삭제 완료 - aveClassCd: {}, affectedRows: {}", aveClassCd, affectedRows);
        return affectedRows;
    }

    /**
     * 소분류 코드 논리적 삭제
     *
     * @param smallClassCd 소분류 코드
     * @param delpersId 편집자 ID
     * @return 삭제된 행 수
     */
    private int deleteSmallCode(String smallClassCd, String delpersId) {
        log.info("소분류 코드 삭제 시작 - smallClassCd: {}, delpersId: {}", smallClassCd, delpersId);
        
        // 존재하는 코드인지 확인
        mdmCodeQueryService.validateSmallCodeExists(smallClassCd);

        int affectedRows = mdmCodeCommandMapper.deleteSmallCode(smallClassCd, delpersId);

        if (affectedRows <= 0) {
            log.warn("소분류 코드 삭제 실패 - smallClassCd: {}, affectedRows: {}", smallClassCd, affectedRows);
            throw new CommonException(CommonErrorCode.INTERNAL_SERVER_ERROR,
                    String.format("소분류 코드 삭제에 실패했습니다. smallClassCd: %s", smallClassCd));
        }

        log.info("소분류 코드 삭제 완료 - smallClassCd: {}, affectedRows: {}", smallClassCd, affectedRows);
        return affectedRows;
    }

    /**
     * 매체 코드 논리적 삭제
     *
     * @param mdmCd 매체 코드
     * @param delpersId 편집자 ID
     * @return 삭제된 행 수
     */
    private int deleteMdmCode(String mdmCd, String delpersId) {
        log.info("매체 코드 삭제 시작 - mdmCd: {}, delpersId: {}", mdmCd, delpersId);
        
        // 존재하는 코드인지 확인
        mdmCodeQueryService.validateMdmCodeExists(mdmCd);

        // 삭제전 데이터 메모리 저장
        MdmCode deletedData = mdmCodeQueryService.findMdmCodeByMdmCd(mdmCd);

        // 매체 코드 삭제 (논리적 삭제)
        int affectedRows = mdmCodeCommandMapper.deleteMdmCode(mdmCd, delpersId);

        if (affectedRows <= 0) {
            log.warn("매체 코드 삭제 실패 - mdmCd: {}, affectedRows: {}", mdmCd, affectedRows);
            throw new CommonException(CommonErrorCode.INTERNAL_SERVER_ERROR,
                    String.format("매체 코드 삭제에 실패했습니다. mdmCd: %s", mdmCd));
        }

        // DELETE 후 이력 저장 (삭제 전 데이터 기반)
        mdmCodeHistoryCommandService.saveDeleteHistory(mdmCd, deletedData, delpersId);

        log.info("매체 코드 삭제 완료 - mdmCd: {}, affectedRows: {}", mdmCd, affectedRows);
        return affectedRows;
    }

    /**
     * 서비스 코드 논리적 삭제
     *
     * @param svcCd 서비스 코드
     * @param delpersId 편집자 ID
     * @return 삭제된 행 수
     */
    private int deleteServiceCode(String svcCd, String delpersId) {
        log.info("서비스 코드 삭제 시작 - svcCd: {}, delpersId: {}", svcCd, delpersId);
        
        // 존재하는 코드인지 확인
        mdmCodeQueryService.validateServiceCodeExists(svcCd);

        int affectedRows = mdmCodeCommandMapper.deleteServiceCode(svcCd, delpersId);

        if (affectedRows <= 0) {
            log.warn("서비스 코드 삭제 실패 - svcCd: {}, affectedRows: {}", svcCd, affectedRows);
            throw new CommonException(CommonErrorCode.INTERNAL_SERVER_ERROR,
                    String.format("서비스 코드 삭제에 실패했습니다. svcCd: %s", svcCd));
        }

        log.info("서비스 코드 삭제 완료 - svcCd: {}, affectedRows: {}", svcCd, affectedRows);
        return affectedRows;
    }

    /**
     * BigDecimal 변경 여부 확인
     *
     * @param before 변경 전 값
     * @param after 변경 후 값
     * @return 변경 여부
     */
    private boolean isBigDecimalChanged(BigDecimal before, BigDecimal after) {
        // 둘 다 null이면 변경 없음
        if (before == null && after == null) {
            return false;
        }
        // 하나만 null이면 변경 있음
        if (before == null || after == null) {
            return true;
        }
        // 둘 다 non-null이면 compareTo()로 수치 비교
        return before.compareTo(after) != 0;
    }

}
