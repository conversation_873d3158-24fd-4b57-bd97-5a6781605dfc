package kr.or.komca.admin.code.common.api;


import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.extensions.Extension;
import io.swagger.v3.oas.annotations.extensions.ExtensionProperty;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import kr.or.komca.admin.code.common.dto.command.request.ManageDetailCodeRequest;
import kr.or.komca.admin.code.common.dto.command.request.ManageParCodeRequest;
import kr.or.komca.admin.code.common.dto.command.response.ManageDetailCode;
import kr.or.komca.admin.code.common.dto.command.response.ManageParCode;
import kr.or.komca.admin.code.common.dto.query.condition.DetailCodeExcelCondition;
import kr.or.komca.admin.code.common.dto.query.condition.DetailCodeSearchCondition;
import kr.or.komca.admin.code.common.dto.query.condition.ParCodeExcelCondition;
import kr.or.komca.admin.code.common.dto.query.condition.ParCodeSearchCondition;
import kr.or.komca.admin.code.common.dto.query.response.DetailCode;
import kr.or.komca.admin.code.common.dto.query.response.DetailCodeExcel;
import kr.or.komca.admin.code.common.dto.query.response.ParCode;
import kr.or.komca.admin.code.common.dto.query.response.ParCodeExcel;
import kr.or.komca.common.exception.response.error.CommonErrorResponse;
import kr.or.komca.common.exception.response.success.CommonSuccessResponse;
import kr.or.komca.common.utils.core.dto.response.PageListResponse;
import org.springframework.http.ResponseEntity;

@Tag(name = "CommonCode", description = "시스템/사용자 코드")
public interface CommonCodeApi {

    /** ======== Query API ======== */

    @Operation(
            summary = "상위 코드 목록 조회",
            description = "검색 조건에 맞는 상위 코드 목록을 조회합니다."
    )
    @ApiResponses({
            @ApiResponse(
                    responseCode = "200",
                    description = "조회 성공"
            ),
            @ApiResponse(
                    responseCode = "400",
                    description = "잘못된 요청",
                    content = @Content(schema = @Schema(implementation = CommonErrorResponse.class))
            ),
    })
    ResponseEntity<CommonSuccessResponse<PageListResponse<ParCode>>> getParCodeList(
            @Parameter(description = "상위 코드 검색 조건", required = true)
            ParCodeSearchCondition condition
    );

    @Operation(
            summary = "세부 코드 목록 조회",
            description = "상위 코드에 해당하는 세부 코드 목록을 조회합니다."
    )
    @ApiResponses({
            @ApiResponse(
                    responseCode = "200",
                    description = "조회 성공"
            ),
            @ApiResponse(
                    responseCode = "400",
                    description = "잘못된 요청",
                    content = @Content(schema = @Schema(implementation = CommonErrorResponse.class))
            ),
    })
    ResponseEntity<CommonSuccessResponse<PageListResponse<DetailCode>>> getDetailCodeList(
            @Parameter(description = "상위 코드", required = true)
            String parCd,
            @Parameter(description = "세부 코드 검색 조건", required = true)
            DetailCodeSearchCondition condition
    );


    @Operation(
            summary = "상위 코드 목록 조회",
            description = "검색 조건에 맞는 상위 코드 목록을 엑셀 조회합니다."
    )
    @ApiResponses({
            @ApiResponse(
                    responseCode = "200",
                    description = "조회 성공"
            ),
            @ApiResponse(
                    responseCode = "400",
                    description = "잘못된 요청",
                    content = @Content(schema = @Schema(implementation = CommonErrorResponse.class))
            ),
    })
    ResponseEntity<CommonSuccessResponse<PageListResponse<ParCodeExcel>>> getParCodeExcelList(
            @Parameter(description = "상위 코드 검색 조건", required = true)
            ParCodeExcelCondition condition
    );

    @Operation(
            summary = "세부 코드 목록 엑셀 조회",
            description = "상위 코드에 해당하는 세부 코드 목록을 엑셀 조회합니다."
    )
    @ApiResponses({
            @ApiResponse(
                    responseCode = "200",
                    description = "조회 성공"
            ),
            @ApiResponse(
                    responseCode = "400",
                    description = "잘못된 요청",
                    content = @Content(schema = @Schema(implementation = CommonErrorResponse.class))
            ),
    })
    ResponseEntity<CommonSuccessResponse<PageListResponse<DetailCodeExcel>>> getDetailCodeExcelList(
            @Parameter(description = "상위 코드", required = true)
            String parCd,
            @Parameter(description = "세부 코드 검색 조건", required = true)
            DetailCodeExcelCondition condition
    );


    /** ======== Command API ======== */

    @Operation(
            summary = "상위 코드 통합 관리",
            description = "상위 코드를 일괄로 생성/수정/삭제합니다."
    )
    @ApiResponses({
            @ApiResponse(
                    responseCode = "200",
                    description = "처리 성공"
            ),
            @ApiResponse(
                    responseCode = "400",
                    description = "잘못된 요청",
                    content = @Content(schema = @Schema(implementation = CommonErrorResponse.class))
            ),
    })
    ResponseEntity<CommonSuccessResponse<ManageParCode>> manageParCode(
            @Parameter(description = "상위 코드 통합 관리 요청", required = true)
            ManageParCodeRequest request
    );

    @Operation(
            summary = "세부 코드 통합 관리",
            description = "세부 코드를 일괄로 생성/수정/삭제합니다."
    )
    @ApiResponses({
            @ApiResponse(
                    responseCode = "200",
                    description = "처리 성공"
            ),
            @ApiResponse(
                    responseCode = "400",
                    description = "잘못된 요청",
                    content = @Content(schema = @Schema(implementation = CommonErrorResponse.class))
            ),
    })
    ResponseEntity<CommonSuccessResponse<ManageDetailCode>> manageDetailCode(
            @Parameter(description = "상위 코드", required = true)
            String parCd,
            @Parameter(description = "세부 코드 통합 관리 요청", required = true)
            ManageDetailCodeRequest request
    );
}
