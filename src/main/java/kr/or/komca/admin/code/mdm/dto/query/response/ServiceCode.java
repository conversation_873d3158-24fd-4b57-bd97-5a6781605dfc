package kr.or.komca.admin.code.mdm.dto.query.response;

import io.swagger.v3.oas.annotations.media.Schema; 
import lombok.Builder; 
import lombok.Getter; 
import lombok.AllArgsConstructor; 
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
* 서비스코드 조회에 대한 응답 DTO
*/
@Getter 
@Builder 
@AllArgsConstructor 
@NoArgsConstructor 
@Schema(description = "서비스코드 조회에 대한 응답 DTO")
public class ServiceCode {

    /** 대분류코드 */
    @Schema(description = "대분류코드", example = "A")
    private String largeClassCd;

    /** 중분류코드 */
    @Schema(description = "중분류코드", example = "AA")
    private String aveClassCd;

    /** 소분류코드 */
    @Schema(description = "소분류코드", example = "AA01")
    private String smallClassCd;

    /** 매체코드 */
    @Schema(description = "매체코드", example = "AA0103")
    private String mdmCd;

    /** 서비스코드 */
    @Schema(description = "서비스코드", example = "701")
    private String svcCd;

    /** 서비스명 */
    @Schema(description = "서비스명", example = "TBN 도로교통")
    private String svcNm;

    /** 거래처코드 */
    @Schema(description = "거래처코드", example = "G0129")
    private String bsconCd;

    /** 분배유형 [ common - 00303 ] */
    @Schema(description = "분배유형", example = "3")
    private String distrType;

    /** 사용료율 */
    @Schema(description = "사용료율(%)", example = "1.35")
    private BigDecimal rtalRate;

    /** 곡단가금액 */
    @Schema(description = "곡단가금액", example = "0")
    private Long tuneUncoAmt;

    /** 관리비율 */
    @Schema(description = "관리비율(%)", example = "100.0")
    private BigDecimal mngRate;

    /** 공제계수 */
    @Schema(description = "공제계수(%)", example = "70.0")
    private BigDecimal dedctCffnt;

    /** 조정계수 */
    @Schema(description = "조정계수(%)", example = "46.0")
    private BigDecimal adjCffnt;

    /** 침해가산율 */
    @Schema(description = "침해가산율(%)", example = "30.0")
    private BigDecimal ifmntAddRate;

    /** 할인율 */
    @Schema(description = "할인율(%)", example = "0.0")
    private BigDecimal dsctRate;

    /** 월정액금액 */
    @Schema(description = "월정액금액(원)", example = "0")
    private Long monFamtAmt;

    /** 하한가금액 */
    @Schema(description = "하한가금액(원)", example = "0")
    private Long minAmt;

    /** 산출산식 */
    @Schema(description = "산출산식", example = "매출액 * 사용료율 * 관리비율")
    private String caltnAtex;

    /** 징수규정 */
    @Schema(description = "징수규정", example = "매출액 * 사용료율 * 관리비율, 단가금액 * 가입자수 * 관리비율")
    private String levyPvsn;

    /** 부가세여부 */
    @Schema(description = "부가세여부", example = "1")
    private String ataxYn;

    /** 사용여부 */
    @Schema(description = "사용여부", example = "Y")
    private String useYn;

    /** 등록일시 */
    @Schema(description = "등록일시", example = "2011-06-02 17:34:32")
    private String insDt;
    
    /** 등록자ID */
    @Schema(description = "등록자ID", example = "K095008")
    private String inspersId;
    
    /** 수정일시 */
    @Schema(description = "수정일시", example = "2019-01-10 11:16:47")
    private String modDt;
    
    /** 수정자ID */
    @Schema(description = "수정자ID", example = "K180021")
    private String modpersId;
}
