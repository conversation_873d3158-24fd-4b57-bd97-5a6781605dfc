package kr.or.komca.admin.code.mdm.util;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

/**
 * Sort Validator
 */
@Slf4j
@Component
public class ServiceCodeSortColumnValidator {

	/**
	 * 허용된 정렬 컬럼 매핑 정보
	 * Key: 프론트엔드 요청 파라미터명
	 * Value: 실제 DB 테이블의 컬럼명
	 */
	private static final Map<String, String> ALLOWED_SORT_COLUMNS_MAP = new HashMap<>() {
		{
			put("svcCd", "svc_cd");                      // 서비스코드
			put("svcNm", "svc_nm");                      // 서비스명
			put("bsconCd", "bscon_cd");                  // 입금 거래처코드
			put("distrType", "distr_type");              // 분배유형
			put("rtalRate", "TO_NUMBER(rtal_rate)");     // 사용료율
			put("tuneUncoAmt", "TO_NUMBER(tune_unco_amt)"); // 곡단가금액
			put("mngRate", "TO_NUMBER(mng_rate)");        // 관리비율
			put("dedctCffnt", "TO_NUMBER(dedct_cffnt)");  // 공제계수
			put("adjCffnt", "TO_NUMBER(adj_cffnt)");      // 조정계수
			put("ifmntAddRate", "TO_NUMBER(ifmnt_add_rate)"); // 침해가산율
			put("dsctRate", "TO_NUMBER(dsct_rate)");      // 할인율
			put("monFamtAmt", "TO_NUMBER(mon_famt_amt)"); // 월정액금액
			put("minAmt", "TO_NUMBER(min_amt)");         // 하한가금액
			put("caltnAtex", "caltn_atex");              // 산출산식
			put("levyPvsn", "levy_pvsn");                // 징수규정
			put("ataxYn", "atax_yn");                    // 부가세여부
			put("useYn", "use_yn");                      // 사용여부
		}
	};

	/**
	 * 정렬 컬럼 유효성 검사 및 매핑
	 */
	public static String validate(String sortColumn) {
		return ALLOWED_SORT_COLUMNS_MAP.get(sortColumn);
	}
}
