package kr.or.komca.admin.code.common.dto.command.request;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.Valid;
import kr.or.komca.admin.code.common.dto.command.request.sub.CreateDetailCode;
import kr.or.komca.admin.code.common.dto.command.request.sub.UpdateDetailCode;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.ToString;

import java.util.List;

/**
 * 세부 코드 통합 관리 요청 DTO
 */
@Getter
@Builder
@AllArgsConstructor
@ToString
@Schema(description = "세부 코드 통합 관리 요청 (C/U/D)")
public class ManageDetailCodeRequest {

    @Schema(description = "생성 목록")
    private List<@Valid CreateDetailCode> create;

    @Schema(description = "수정 목록")
    private List<@Valid UpdateDetailCode> update;

    @Schema(description = "삭제 목록 (삭제 세부 코드 리스트)")
    private List<String> delete;
}
