package kr.or.komca.admin.code.common.mapper.command;

import kr.or.komca.admin.code.common.dto.command.request.sub.CreateDetailCode;
import kr.or.komca.admin.code.common.dto.command.request.sub.CreateParCode;
import kr.or.komca.admin.code.common.dto.command.request.sub.UpdateDetailCode;
import kr.or.komca.admin.code.common.dto.command.request.sub.UpdateParCode;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface CommonCodeCommandMapper {

    /**
     * 상위 코드 저장
     *
     * @param parCd 상위코드
     * @param command 저장할 상위 코드 정보
     * @return 저장된 행 수
     */
    int createParCode(
            @Param("parCd") String parCd,
            @Param("inspersId") String inspersId,
            @Param("command") CreateParCode command
    );
    
    /**
     * 상위 코드 수정
     * @param parCd 상위 코드
     * @param command 수정할 상위 코드 정보
     * @return 수정된 행 수
     */
    int updateParCode(
            @Param("parCd") String parCd,
            @Param("modpersId") String modpersId,
            @Param("command") UpdateParCode command
    );
    
    /**
     * 상위 코드 삭제 (논리삭제)
     * @param parCd 상위 코드
     * @param delpersId 삭제자 ID
     * @return 삭제된 행 수
     */
    int deleteParCode(@Param("parCd") String parCd, @Param("delpersId") String delpersId);

    /**
     * 상위 코드 부서 매핑 저장
     * @param parCd 상위 코드
     * @param deptCd 부서 코드
     * @return 저장된 행 수
     */
    int insertParCodeGroupUser(
            @Param("parCd") String parCd,
            @Param("deptCd") String deptCd
    );
    
    /**
     * 세부 코드 저장
     *
     * @param parCd 상위 코드
     * @param cd 코드 값
     * @param command 저장할 세부 코드 정보
     * @return 저장된 행 수
     */
    int createDetailCode(
            @Param("parCd") String parCd,
            @Param("cd") String cd,
            @Param("inspersId") String inspersId,
            @Param("command") CreateDetailCode command
    );

    /**
     * 세부 코드 수정
     * @param parCd 상위 코드
     * @param cd 코드 값
     * @param command 수정할 세부 코드 정보
     * @return 수정된 행 수
     */
    int updateDetailCode(
            @Param("parCd") String parCd,
            @Param("cd") String cd,
            @Param("modpersId") String modpersId,
            @Param("command") UpdateDetailCode command
    );

    /**
     * 세부 코드 삭제 (논리삭제)
     * @param parCd 상위 코드
     * @param cd 코드 값
     * @param delpersId 삭제자 ID
     * @return 삭제된 행 수
     */
    int deleteDetailCode(@Param("parCd") String parCd, @Param("cd") String cd, @Param("delpersId") String delpersId);
}
