package kr.or.komca.admin.code.common.service.command;

import kr.or.komca.admin.code.common.dto.command.request.ManageDetailCodeRequest;
import kr.or.komca.admin.code.common.dto.command.request.ManageParCodeRequest;
import kr.or.komca.admin.code.common.dto.command.request.sub.CreateDetailCode;
import kr.or.komca.admin.code.common.dto.command.request.sub.CreateParCode;
import kr.or.komca.admin.code.common.dto.command.request.sub.UpdateDetailCode;
import kr.or.komca.admin.code.common.dto.command.request.sub.UpdateParCode;
import kr.or.komca.admin.code.common.dto.command.response.ManageDetailCode;
import kr.or.komca.admin.code.common.dto.command.response.ManageParCode;
import kr.or.komca.admin.code.common.mapper.command.CommonCodeCommandMapper;
import kr.or.komca.admin.code.common.service.query.CommonCodeQueryService;
import kr.or.komca.common.exception.core.CommonException;
import kr.or.komca.common.exception.response.error.code.CommonErrorCode;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Slf4j
@RequiredArgsConstructor
@Service
public class CommonCodeCommandServiceImpl implements CommonCodeCommandService {

    private final CommonCodeCommandMapper commonCodeCommandMapper;
    private final CommonCodeQueryService commonCodeQueryService;

    @Override
    @Transactional
    public ManageParCode manageParCode(ManageParCodeRequest request, String editperId) {
        List<CreateParCode> create = request.getCreate();
        List<UpdateParCode> update = request.getUpdate();
        List<String> delete = request.getDelete();
        
        log.info("상위 코드 관리 시작 - editpersId: {}, 생성: {}건, 수정: {}건, 삭제: {}건",
                editperId,
                create != null ? create.size() : 0,
                update != null ? update.size() : 0,
                delete != null ? delete.size() : 0);

        // 각 작업 수행 결과
        int createAffectedRows = 0;
        int updateAffectedRows = 0;
        int deleteAffectedRows = 0;

        // create 요청이 존재한다면 상위 코드 추가
        if (create != null && !create.isEmpty()) {
            for (CreateParCode createParCode : create) {
                createAffectedRows += createParCode(createParCode, editperId);
            }
        }

        // update 요청이 존재한다면 상위 코드 수정
        if (update != null && !update.isEmpty()) {
            for (UpdateParCode updateParCode : update) {
                updateAffectedRows += updateParCode(updateParCode.getParCd(), editperId, updateParCode);
            }
        }

        // delete 요청이 존재한다면 상위 코드 삭제
        if (delete != null && !delete.isEmpty()) {
            for (String parCd : delete) {
                deleteAffectedRows += deleteParCode(parCd, editperId);
            }
        }
        
        ManageParCode result = ManageParCode.builder()
                .createAffectedRows(createAffectedRows)
                .updateAffectedRows(updateAffectedRows)
                .deleteAffectedRows(deleteAffectedRows)
                .build();

        log.info("상위 코드 관리 완료 - editpersId: {}, 생성: {}건, 수정: {}건, 삭제: {}건",
                editperId, createAffectedRows, updateAffectedRows, deleteAffectedRows);

        return result;
    }

    @Override
    @Transactional
    public ManageDetailCode manageDetailCode(String parCd, ManageDetailCodeRequest request, String editpersId) {
        List<CreateDetailCode> create = request.getCreate();
        List<UpdateDetailCode> update = request.getUpdate();
        List<String> delete = request.getDelete();
        
        log.info("세부 코드 관리 시작 - parCd: {}, editpersId: {}, 생성: {}건, 수정: {}건, 삭제: {}건",
                parCd, editpersId,
                create != null ? create.size() : 0,
                update != null ? update.size() : 0,
                delete != null ? delete.size() : 0);

        // 상위 코드 존재 확인
        commonCodeQueryService.validateParCodeExists(parCd);

        // 각 작업 수행 결과
        int createAffectedRows = 0;
        int updateAffectedRows = 0;
        int deleteAffectedRows = 0;

        // create 요청이 존재한다면 세부 코드 추가
        if (create != null && !create.isEmpty()) {
            for (CreateDetailCode createDetailCode : create) {
                createAffectedRows += createDetailCode(parCd, createDetailCode, editpersId);
            }
        }

        // update 요청이 존재한다면 세부 코드 수정
        if (update != null && !update.isEmpty()) {
            for (UpdateDetailCode updateDetailCode : update) {
                updateAffectedRows += updateDetailCode(parCd, updateDetailCode.getCd(), editpersId, updateDetailCode);
            }
        }

        // delete 요청이 존재한다면 세부 코드 삭제
        if (delete != null && !delete.isEmpty()) {
            for (String cd : delete) {
                deleteAffectedRows += deleteDetailCode(parCd, cd, editpersId);
            }
        }
        
        ManageDetailCode result = ManageDetailCode.builder()
                .createAffectedRows(createAffectedRows)
                .updateAffectedRows(updateAffectedRows)
                .deleteAffectedRows(deleteAffectedRows)
                .build();

        log.info("세부 코드 관리 완료 - parCd: {}, editpersId: {}, 생성: {}건, 수정: {}건, 삭제: {}건",
                parCd, editpersId, createAffectedRows, updateAffectedRows, deleteAffectedRows);

        return result;
    }

    // ================== Private Create Methods ==================

    private int createParCode(CreateParCode request, String inspersId) {
        String parCd = request.getParCd();
        String deptCd = request.getDeptCd();

        // deptCd가 존재한다면 플래그 true, 존재하지 않는다면 false
        boolean deptFlag = deptCd != null && !deptCd.trim().isEmpty();

        log.info("상위 코드 생성 시작 - parCd: {}, deptCd: {}, inspersId: {}", parCd, deptCd, inspersId);

        // 상위 코드 중복 확인
        commonCodeQueryService.validateParCodeDuplicate(parCd);

        // deptCd가 있는 경우 중복 매핑 확인
        if (deptFlag) {
            commonCodeQueryService.validateParCodeGroupUserDuplicate(parCd, deptCd);
        }

        int affectedRows = commonCodeCommandMapper.createParCode(parCd, inspersId, request);

        if (affectedRows <= 0) {
            log.error("상위 코드 생성 실패 - parCd: {}, affectedRows: {}", parCd, affectedRows);
            throw new CommonException(CommonErrorCode.INTERNAL_SERVER_ERROR,
                    String.format("상위 코드 생성에 실패했습니다. parCd: %s", parCd));
        }

        // deptCd가 있는 경우 부서 매핑 생성
        if (deptFlag) {
            int mappingAffectedRows = commonCodeCommandMapper.insertParCodeGroupUser(parCd, deptCd);

            if (mappingAffectedRows <= 0) {
                log.error("상위 코드 부서 매핑 생성 실패 - parCd: {}, deptCd: {}, affectedRows: {}",
                        parCd, deptCd, mappingAffectedRows);
                throw new CommonException(CommonErrorCode.INTERNAL_SERVER_ERROR,
                        String.format("상위 코드 부서 매핑 생성에 실패했습니다. parCd: %s, deptCd: %s", parCd, deptCd));
            }
            log.info("상위 코드 부서 매핑 생성 완료 - parCd: {}, deptCd: {}, affectedRows: {}", 
                    parCd, deptCd, mappingAffectedRows);
        }

        log.info("상위 코드 생성 완료 - parCd: {}, deptCd: {}, affectedRows: {}", parCd, deptCd, affectedRows);
        return affectedRows;
    }

    private int createDetailCode(
            String parCd,
            CreateDetailCode request,
            String inspersId
    ) {
        String cd = request.getCd();
        
        log.info("세부 코드 생성 시작 - parCd: {}, cd: {}, inspersId: {}", parCd, cd, inspersId);

        // 세부 코드 중복 확인
        commonCodeQueryService.validateDetailCodeDuplicate(parCd, cd);

        int affectedRows = commonCodeCommandMapper.createDetailCode(parCd, cd, inspersId, request);

        if (affectedRows <= 0) {
            log.error("세부 코드 생성 실패 - parCd: {}, cd: {}, affectedRows: {}", parCd, cd, affectedRows);
            throw new CommonException(CommonErrorCode.INTERNAL_SERVER_ERROR,
                    String.format("세부 코드 생성에 실패했습니다. parCd: %s, cd: %s", parCd, cd));
        }
        
        log.info("세부 코드 생성 완료 - parCd: {}, cd: {}, affectedRows: {}", parCd, cd, affectedRows);
        return affectedRows;
    }

    private int updateParCode(String parCd, String modpersId, UpdateParCode request) {
        log.info("상위 코드 수정 시작 - parCd: {}, modpersId: {}", parCd, modpersId);

        // 존재하는 코드인지 확인
        commonCodeQueryService.validateParCodeExists(parCd);

        int affectedRows = commonCodeCommandMapper.updateParCode(parCd, modpersId, request);

        if (affectedRows <= 0) {
            log.error("상위 코드 수정 실패 - parCd: {}, affectedRows: {}", parCd, affectedRows);
            throw new CommonException(
                    CommonErrorCode.INTERNAL_SERVER_ERROR,
                    String.format("상위 코드 수정에 실패했습니다. parCd: %s", parCd)
            );
        }
        
        log.info("상위 코드 수정 완료 - parCd: {}, affectedRows: {}", parCd, affectedRows);
        return affectedRows;
    }

    private int updateDetailCode(String parCd, String cd, String modpersId, UpdateDetailCode request) {
        log.info("세부 코드 수정 시작 - parCd: {}, cd: {}, modpersId: {}", parCd, cd, modpersId);

        // 코드가 존재하는지 확인
        commonCodeQueryService.validateDetailCodeExists(parCd, cd);

        int affectedRows = commonCodeCommandMapper.updateDetailCode(parCd, cd, modpersId, request);

        if (affectedRows <= 0) {
            log.error("세부 코드 수정 실패 - parCd: {}, cd: {}, affectedRows: {}", parCd, cd, affectedRows);
            throw new CommonException(
                    CommonErrorCode.INTERNAL_SERVER_ERROR,
                    String.format("세부 코드 수정에 실패했습니다. parCd: %s, cd: %s", parCd, cd)
            );
        }
        
        log.info("세부 코드 수정 완료 - parCd: {}, cd: {}, affectedRows: {}", parCd, cd, affectedRows);
        return affectedRows;
    }

    // ================== Private Delete Methods ==================

    private int deleteParCode(String parCd, String delpersId) {
        log.info("상위 코드 삭제 시작 - parCd: {}, delpersId: {}", parCd, delpersId);

        // 존재하는 코드인지 확인
        commonCodeQueryService.validateParCodeExists(parCd);

        // 하위 코드가 있는지 확인
        commonCodeQueryService.validateNoDetailCode(parCd);

        int affectedRows = commonCodeCommandMapper.deleteParCode(parCd, delpersId);

        if (affectedRows <= 0) {
            log.error("상위 코드 삭제 실패 - parCd: {}, affectedRows: {}", parCd, affectedRows);
            throw new CommonException(
                    CommonErrorCode.INTERNAL_SERVER_ERROR,
                    String.format("상위 코드 삭제에 실패했습니다. parCd: %s", parCd)
            );
        }
        
        log.info("상위 코드 삭제 완료 - parCd: {}, affectedRows: {}", parCd, affectedRows);
        return affectedRows;
    }

    private int deleteDetailCode(String parCd, String cd, String delpersId) {
        log.info("세부 코드 삭제 시작 - parCd: {}, cd: {}, delpersId: {}", parCd, cd, delpersId);

        // 코드가 존재하는지 확인
        commonCodeQueryService.validateDetailCodeExists(parCd, cd);

        int affectedRows = commonCodeCommandMapper.deleteDetailCode(parCd, cd, delpersId);

        if (affectedRows <= 0) {
            log.error("세부 코드 삭제 실패 - parCd: {}, cd: {}, affectedRows: {}", parCd, cd, affectedRows);
            throw new CommonException(
                    CommonErrorCode.INTERNAL_SERVER_ERROR,
                    String.format("세부 코드 삭제에 실패했습니다. parCd: %s, cd: %s", parCd, cd)
            );
        }
        
        log.info("세부 코드 삭제 완료 - parCd: {}, cd: {}, affectedRows: {}", parCd, cd, affectedRows);
        return affectedRows;
    }
}
