package kr.or.komca.admin.code.mdm.service.query;

import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import kr.or.komca.admin.code.mdm.dto.query.condition.ServiceCodeExcelCondition;
import kr.or.komca.admin.code.mdm.dto.query.response.ServiceCodeExcel;
import kr.or.komca.admin.code.mdm.mapper.query.MdmCodeExcelMapper;
import kr.or.komca.common.exception.core.CommonException;
import kr.or.komca.common.exception.response.error.code.CommonErrorCode;
import kr.or.komca.common.utils.core.dto.response.PageListResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * 매체코드 엑셀 관련 서비스 구현체
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class MdmCodeExcelServiceImpl implements MdmCodeExcelService {

    private final MdmCodeExcelMapper mdmCodeExcelMapper;
    private final MdmCodeQueryService mdmCodeQueryService;

    @Override
    @Transactional(readOnly = true)
    public PageListResponse<ServiceCodeExcel> getServiceCodeExcelList(String mdmCd, ServiceCodeExcelCondition condition) {
        // 매체코드 존재 여부 확인
        mdmCodeQueryService.validateMdmCodeExists(mdmCd);

        try (Page<ServiceCodeExcel> page = PageHelper.startPage(condition.getPage(), condition.getPageSize())) {
            List<ServiceCodeExcel> serviceCodeList = mdmCodeExcelMapper.getServiceCodeExcelList(mdmCd, condition);

            PageInfo<ServiceCodeExcel> pageInfo = new PageInfo<>(serviceCodeList);

            return PageListResponse.<ServiceCodeExcel>builder()
                    .contents(pageInfo.getList())
                    .totalElements(pageInfo.getTotal())
                    .page(pageInfo.getPageNum())
                    .totalPages(pageInfo.getPages())
                    .pageSize(pageInfo.getPageSize())
                    .build();

        } catch (Exception e) {
            log.error("서비스코드 엑셀 조회 중 페이징 처리 오류 발생", e);
            throw new CommonException(CommonErrorCode.INTERNAL_SERVER_ERROR, "서비스코드 엑셀 조회 중 페이징 처리 오류 발생");
        }
    }
}
