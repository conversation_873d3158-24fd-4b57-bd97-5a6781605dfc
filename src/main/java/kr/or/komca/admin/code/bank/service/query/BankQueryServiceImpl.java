package kr.or.komca.admin.code.bank.service.query;

import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import kr.or.komca.admin.code.bank.dto.query.condition.BankSearchCondition;
import kr.or.komca.admin.code.bank.dto.query.response.Bank;
import kr.or.komca.admin.code.bank.enums.errorcode.BankErrorCode;
import kr.or.komca.admin.code.bank.mapper.query.BankQueryMapper;
import kr.or.komca.common.exception.core.CommonException;
import kr.or.komca.common.exception.response.error.code.CommonErrorCode;
import kr.or.komca.common.utils.core.dto.response.PageListResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Slf4j
@RequiredArgsConstructor
@Service
@Transactional(readOnly = true)
public class BankQueryServiceImpl implements BankQueryService {

    private final BankQueryMapper bankQueryMapper;

    @Override
    public PageListResponse<Bank> getBankList(BankSearchCondition condition) {
        try (Page<Bank> page = PageHelper.startPage(condition.getPage(), condition.getPageSize())) {
            List<Bank> bankList = bankQueryMapper.getBankList(condition);

            PageInfo<Bank> pageInfo = new PageInfo<>(bankList);

            return PageListResponse.<Bank>builder()
                .contents(pageInfo.getList())
                .totalElements(pageInfo.getTotal())
                .page(pageInfo.getPageNum())
                .totalPages(pageInfo.getPages())
                .pageSize(pageInfo.getPageSize())
                .build();

        } catch (Exception e) {
            log.error("페이징 처리 오류 발생", e);
            throw new CommonException(CommonErrorCode.INTERNAL_SERVER_ERROR, "페이징 처리 오류 발생");
        }
    }

    @Override
    public void validateExistBank(String bankCd) {
        Bank bank = bankQueryMapper.getBankByCd(bankCd);

        if (bank == null) {
            throw new CommonException(
                BankErrorCode.BANK_NOT_FOUND,
                String.format("존재하지 않는 은행입니다: %s", bankCd)
            );
        }
    }

    @Override
    public void validateBankDuplicate(String bankCd) {
        Bank bank = bankQueryMapper.getBankByCd(bankCd);

        if (bank != null) {
            throw new CommonException(
                BankErrorCode.BANK_ALREADY_EXISTS,
                String.format("이미 존재하는 은행입니다: %s", bankCd)
            );
        }
    }
}
