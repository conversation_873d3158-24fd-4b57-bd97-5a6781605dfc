package kr.or.komca.admin.code.bank.mapper.query;

import kr.or.komca.admin.code.bank.dto.query.condition.BankSearchCondition;
import kr.or.komca.admin.code.bank.dto.query.response.Bank;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface BankQueryMapper {

    /**
     * 은행 목록 조회
     * @param condition 검색 조건
     * @return 은행 목록
     */
    List<Bank> getBankList(@Param("condition") BankSearchCondition condition);

    /**
     * cd로 은행 조회
     * @param bankCd 은행 코드
     * @return 은행
     */
    Bank getBankByCd(@Param("bankCd") String bankCd);
}
