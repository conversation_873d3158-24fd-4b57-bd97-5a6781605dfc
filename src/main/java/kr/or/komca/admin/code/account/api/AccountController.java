package kr.or.komca.admin.code.account.api;

import jakarta.validation.Valid;
import kr.or.komca.admin.code.account.dto.command.request.ManageAccountRequest;
import kr.or.komca.admin.code.account.dto.command.response.ManageAccount;
import kr.or.komca.admin.code.account.dto.query.condition.AccountInfoSearchCondition;
import kr.or.komca.admin.code.account.dto.query.condition.AccountSearchCondition;
import kr.or.komca.admin.code.account.dto.query.response.Account;
import kr.or.komca.admin.code.account.dto.query.response.AccountInfo;
import kr.or.komca.admin.code.account.service.command.AccountCommandService;
import kr.or.komca.admin.code.account.service.query.AccountInfoQueryService;
import kr.or.komca.admin.code.account.service.query.AccountQueryService;
import kr.or.komca.common.auth.support.utils.context.UserContextHolder;
import kr.or.komca.common.exception.response.success.CommonSuccessResponse;
import kr.or.komca.common.utils.core.dto.response.ListResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

@Slf4j
@RestController
@RequestMapping("/api/v1/code/account")
@RequiredArgsConstructor
public class AccountController implements AccountApi {

    private final AccountCommandService accountCommandService;
    private final AccountQueryService accountQueryService;
    private final AccountInfoQueryService accountInfoQueryService;

    /**
     * ======== Query API ========
     */

    @GetMapping
    public ResponseEntity<CommonSuccessResponse<ListResponse<Account>>> getAccountList(
        @ModelAttribute @Valid AccountSearchCondition condition
    ) {
        log.info("계정 목록 조회 요청: {}", condition);
        ListResponse<Account> response = accountQueryService.getAccountList(condition);
        return CommonSuccessResponse.ok(response);
    }

    @GetMapping("/info/list")
    public ResponseEntity<CommonSuccessResponse<ListResponse<AccountInfo>>> getAccountInfoList(
        @ModelAttribute @Valid AccountInfoSearchCondition condition
    ) {
        log.info("계정 정보 목록 조회 요청: {}", condition);
        ListResponse<AccountInfo> response = accountInfoQueryService.getAccountInfoList(condition);
        return CommonSuccessResponse.ok(response);
    }

    /**
     * ======== Command API ========
     */

    @PostMapping
    public ResponseEntity<CommonSuccessResponse<ManageAccount>> manageAccount(
        @Valid @RequestBody ManageAccountRequest request
    ) {
        log.info("계정 통합 관리 요청: {}", request);

        // 임시 사용자 ID (인증 구현 후 실제 로그인 사용자로 변경 필요)
        String inspersId = UserContextHolder.getContext().getUserId();

        ManageAccount result = accountCommandService.manageAccount(request, inspersId);
        return CommonSuccessResponse.ok(result);
    }
}
