package kr.or.komca.admin.code.mdm.util;

import kr.or.komca.admin.code.mdm.dto.command.request.sub.UpdateMdmCode;
import kr.or.komca.admin.code.mdm.dto.query.response.MdmCode;
import lombok.extern.slf4j.Slf4j;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * 매체코드 변경 감지 유틸리티
 */
@Slf4j
public class MdmCodeChangeDetector {

    /**
     * 매체코드 변경된 컬럼을 감지합니다.
     *
     * @param before 변경 전 매체코드 정보
     * @param after 변경 요청 정보
     * @return 변경된 컬럼명 리스트
     */
    public static List<String> detectChangedColumns(MdmCode before, MdmCode after) {
        List<String> changedColumns = new ArrayList<>();

        if (before == null || after == null) {
            log.warn("변경 감지 대상이 null입니다. before: {}, after: {}", before, after);
            return changedColumns;
        }

        // 매체명 변경 감지
        if (isChanged(before.getMdmNm(), after.getMdmNm())) {
            changedColumns.add("MDM_NM");
        }

        // 비고 변경 감지
        if (isChanged(before.getRemak(), after.getRemak())) {
            changedColumns.add("REMAK");
        }

        // 정렬순서 변경 감지
        if (!Objects.equals(before.getSortOrd(), after.getSortOrd())) {
            changedColumns.add("SORT_ORD");
        }

        // 사용여부 변경 감지
        if (isChanged(before.getUseYn(), after.getUseYn())) {
            changedColumns.add("USE_YN");
        }

        // 국내계정코드 변경 감지
        if (isChanged(before.getDomAcctCd(), after.getDomAcctCd())) {
            changedColumns.add("DOM_ACCT_CD");
        }

        // 국외계정코드 변경 감지
        if (isChanged(before.getAbrAcctCd(), after.getAbrAcctCd())) {
            changedColumns.add("ABR_ACCT_CD");
        }

        // 국내지부계정코드 변경 감지
        if (isChanged(before.getDomBranAcctCd(), after.getDomBranAcctCd())) {
            changedColumns.add("DOM_BRAN_ACCT_CD");
        }

        // 국외지부계정코드 변경 감지
        if (isChanged(before.getAbrBranAcctCd(), after.getAbrBranAcctCd())) {
            changedColumns.add("ABR_BRAN_ACCT_CD");
        }

        // 부가세여부 변경 감지
        if (isChanged(before.getAtaxYn(), after.getAtaxYn())) {
            changedColumns.add("ATAX_YN");
        }

        // 관리수수료율 변경 감지
        if (isChanged(before.getMngComisRate(), after.getMngComisRate())) {
            changedColumns.add("MNG_COMIS_RATE");
        }

        return changedColumns;
    }

    /**
     * null과 빈 문자열을 동일하게 처리하여 변경 여부를 확인
     * 
     * @param before 변경 전 값
     * @param after 변경 후 값
     * @return 변경 여부
     */
    private static boolean isChanged(String before, String after) {
        String normalizedBefore = normalizeString(before);
        String normalizedAfter = normalizeString(after);
        return !Objects.equals(normalizedBefore, normalizedAfter);
    }

    /**
     * BigDecimal 타입 변경 여부 확인
     * 
     * @param before 변경 전 값
     * @param after 변경 후 값
     * @return 변경 여부
     */
    private static boolean isChanged(BigDecimal before, BigDecimal after) {
        // 둘 다 null이면 변경 없음
        if (before == null && after == null) {
            return false;
        }
        // 하나만 null이면 변경 있음
        if (before == null || after == null) {
            return true;
        }
        // 둘 다 non-null이면 compareTo()로 수치 비교
        return before.compareTo(after) != 0;
    }

    /**
     * null과 빈 문자열을 null로 정규화
     * 
     * @param value 정규화할 문자열
     * @return 정규화된 문자열 (null 또는 실제 값)
     */
    private static String normalizeString(String value) {
        if (value == null || value.trim().isEmpty()) {
            return null;
        }
        return value.trim();
    }

}