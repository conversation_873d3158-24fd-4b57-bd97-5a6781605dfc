package kr.or.komca.admin.code.mdm.util;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

/**
 * 매체코드 이력 Sort Validator
 */
@Slf4j
@Component
public class MdmCodeHistorySortColumnValidator {

    /**
     * 허용된 정렬 컬럼 매핑 정보
     * Key: 프론트엔드 요청 파라미터명
     * Value: 실제 DB 테이블의 컬럼명
     */
    private static final Map<String, String> ALLOWED_SORT_COLUMNS_MAP = new HashMap<>() {{
        put("applyDt", "APPLY_DT");                                // 적용일자
        put("chgTypeCd", "CHG_TYPE_CD");                           // 변경구분코드
        put("mdmCd", "MDM_CD");                                    // 매체코드
        put("mdmNm", "MDM_NM");                                    // 매체명
        put("applyId", "APPLY_ID");                                // 변경자ID
        put("sortOrd", "TO_NUMBER(SORT_ORD)");                     // 정렬순서
        put("useYn", "USE_YN");                                    // 사용여부
    }};

    /**
     * 정렬 컬럼 유효성 검사 및 매핑
     */
    public static String validate(String sortColumn) {
        return ALLOWED_SORT_COLUMNS_MAP.get(sortColumn);
    }
}