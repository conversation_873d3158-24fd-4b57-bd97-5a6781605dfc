package kr.or.komca.admin.code.common.dto.query.condition;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.Pattern;
import kr.or.komca.admin.code.common.util.DetailCodeSortColumnValidator;
import lombok.*;

/**
 * 상세 코드 검색 조건 DTO
 */
@ToString
@Getter
@AllArgsConstructor
@Builder(toBuilder = true)
public class DetailCodeExcelCondition {
    /** 사용 여부 */
    @Schema(description = "사용 여부", example = "Y")
    @Pattern(regexp = "^(Y|N|)$", message = "사용 여부는 Y, N, NULL 중 하나여야 합니다.")
    private String useYn;

    /** 페이지 번호 */
    @Schema(description = "페이지 번호", example = "1")
    private int page;

    /** 페이지 크기 */
    @Schema(description = "페이지 크기", example = "10")
    private int pageSize;

    /** 정렬할 컬럼 **/
    @Schema(description = "정렬할 컬럼", example = "cd")
    private String sortColumn;

    /** 정렬 순서 1: asc. 2: desc  */
    @Schema(description = "정렬 순서 (1: asc, 2: desc)", example = "1")
    private String sortOrder;

    public String getSortColumn() {
        return DetailCodeSortColumnValidator.validate(this.sortColumn);
    }

    public String getSortOrder() {
        if ("1".equals(this.sortOrder)) return "ASC";
        if ("2".equals(this.sortOrder)) return "DESC";
        return "";
    }
}