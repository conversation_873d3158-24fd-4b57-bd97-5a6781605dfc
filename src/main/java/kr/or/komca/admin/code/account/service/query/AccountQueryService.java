package kr.or.komca.admin.code.account.service.query;

import kr.or.komca.admin.code.account.dto.query.condition.AccountSearchCondition;
import kr.or.komca.admin.code.account.dto.query.response.Account;
import kr.or.komca.common.utils.core.dto.response.ListResponse;

public interface AccountQueryService {

    /**
     * 계정 목록 조회
     *
     * @param condition 검색 조건
     * @return 계정 목록
     */
    ListResponse<Account> getAccountList(AccountSearchCondition condition);
}
