package kr.or.komca.admin.code.payment.dto.query.condition;

import io.swagger.v3.oas.annotations.media.Schema;
import kr.or.komca.admin.code.payment.util.PaymentSortColumnValidator;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.ToString;

/**
 * 지급공제 검색 조건 DTO
 */
@ToString
@Getter
@AllArgsConstructor
@Builder(toBuilder = true)
public class PaymentExcelCondition {
    /** 지급공제 구분 */
    @Schema(description = "지급공제 구분", example = "1")
    private String suppDedctGbn;

    /** 퇴직적용여부 */
    @Schema(description = "퇴직적용여부", example = "1")
    private String retirYn;

    /** 사용구분 */
    @Schema(description = "사용구분", example = "Y")
    private String useYn;
    
    /** 페이지 번호 */
    @Schema(description = "페이지 번호", example = "1")
    private int page;
    
    /** 페이지 크기 */
    @Schema(description = "페이지 크기", example = "10")
    private int pageSize;

    /** 정렬할 컬럼 **/
    @Schema(description = "정렬할 컬럼", example = "suppDedctCd")
    private String sortColumn;

    /** 정렬 순서 1: asc. 2: desc  */
    @Schema(description = "정렬 순서 (1: asc, 2: desc)", example = "1")
    private String sortOrder;

    public String getSortColumn() {
        return PaymentSortColumnValidator.validate(this.sortColumn);
    }

    public String getSortOrder() {
        if ("1".equals(this.sortOrder)) return "ASC";
        if ("2".equals(this.sortOrder)) return "DESC";
        return "";
    }
}