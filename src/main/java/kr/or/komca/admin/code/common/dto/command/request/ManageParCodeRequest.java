package kr.or.komca.admin.code.common.dto.command.request;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.Valid;
import kr.or.komca.admin.code.common.dto.command.request.sub.CreateParCode;
import kr.or.komca.admin.code.common.dto.command.request.sub.UpdateParCode;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.ToString;

import java.util.List;

/**
 * 상위 코드 통합 관리 요청 DTO
 */
@Getter
@Builder
@AllArgsConstructor
@ToString
@Schema(description = "상위 코드 통합 관리 요청 (C/U/D)")
public class ManageParCodeRequest {

    @Schema(description = "생성 목록")
    private List<@Valid CreateParCode> create;

    @Schema(description = "수정 목록")
    private List<@Valid UpdateParCode> update;

    @Schema(description = "삭제 목록 (삭제 상위 코드 리스트)")
    private List<String> delete;
}
