package kr.or.komca.admin.code.account.dto.command.request.sub;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.ToString;

/**
 * 계정 생성 요청
 */
@Getter
@Builder
@AllArgsConstructor
@ToString
@Schema(description = "계정 생성 요청")
public class CreateAccount {

    /**
     * 계정코드
     */
    @Schema(description = "계정코드", example = "0002")
    @Size(max = 256)
    private String acctCd;

    /**
     * 계정명
     */
    @Schema(description = "계정명", example = "유동자산")
    @Size(max = 256)
    private String acctNm;

    /**
     * 상위계정코드
     */
    @Schema(description = "상위계정코드", example = "0001")
    @Size(max = 256)
    private String parAcctCd;

    /**
     * 업무구분
     */
    @Schema(description = "업무구분", example = "001")
    @Size(max = 256)
    private String jobGbn;

    /**
     * 차대구분
     */
    @Schema(description = "차대구분", example = "001")
    @Size(max = 256)
    private String drCrGbn;

    /**
     * 이월여부
     */
    @Schema(description = "이월여부", example = "Y")
    @Size(max = 256)
    private String crovYn;

    /**
     * 회계구분신탁여부
     */
    @Schema(description = "회계구분신탁여부", example = "Y")
    @Size(max = 256)
    private String acctnGbnTrustYn;

    /**
     * 회계구분일반여부
     */
    @Schema(description = "회계구분일반여부", example = "Y")
    @Size(max = 256)
    private String acctnGbnNormalYn;

    /**
     * 회계구분회관여부
     */
    @Schema(description = "회계구분회관여부", example = "Y")
    @Size(max = 256)
    private String acctnGbnHallYn;

    /**
     * 회계구분회원여부
     */
    @Schema(description = "회계구분회원여부", example = "Y")
    @Size(max = 256)
    private String acctnGbnMbYn;

    /**
     * 회계구분원로여부
     */
    @Schema(description = "회계구분원로여부", example = "Y")
    @Size(max = 256)
    private String acctnGbnElderYn;

    /**
     * 충당여부
     */
    @Schema(description = "충당여부", example = "Y")
    @Size(max = 256)
    private String provYn;

    /**
     * 충당금연결코드
     */
    @Schema(description = "충당금연결코드", example = "0086")
    @Size(max = 256)
    private String provAmtLinkCd;

    /**
     * 수수료율
     */
    @Schema(description = "수수료율", example = "0.00")
    @Size(max = 256)
    private String comisRate;

    /**
     * 계정분류
     */
    @Schema(description = "계정분류", example = "002")
    @Size(max = 256)
    private String acctClass;

    /**
     * 승인여부
     */
    @Schema(description = "승인여부", example = "Y")
    @Size(max = 256)
    private String apprvYn;

    /**
     * 조회순서
     */
    @Schema(description = "조회순서", example = "1")
    @Size(max = 256)
    private String readOrd;

    /**
     * 전표여부
     */
    @Schema(description = "전표여부", example = "N")
    @Size(max = 256)
    private String slipYn;
}
