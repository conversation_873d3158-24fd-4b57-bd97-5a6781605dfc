package kr.or.komca.admin.code.mdm.service.command;

import kr.or.komca.admin.code.mdm.dto.command.request.MdmCodeHistoryRequest;
import kr.or.komca.admin.code.mdm.dto.command.request.sub.CreateMdmCode;
import kr.or.komca.admin.code.mdm.dto.command.request.sub.UpdateMdmCode;
import kr.or.komca.admin.code.mdm.dto.query.response.MdmCode;
import kr.or.komca.admin.code.mdm.enums.MdmCodeHistoryChangeType;
import kr.or.komca.admin.code.mdm.mapper.command.MdmCodeHistoryCommandMapper;
import kr.or.komca.admin.code.mdm.service.query.MdmCodeQueryService;
import kr.or.komca.admin.code.mdm.util.MdmCodeChangeDetector;
import kr.or.komca.common.exception.core.CommonException;
import kr.or.komca.common.exception.response.error.code.CommonErrorCode;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.List;

/**
 * 매체코드 이력 관리 Command 서비스 구현체
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class MdmCodeHistoryCommandServiceImpl implements MdmCodeHistoryCommandService {

    private final MdmCodeHistoryCommandMapper mdmCodeHistoryCommandMapper;
    private final MdmCodeQueryService mdmCodeQueryService;

    @Override
    @Transactional
    public void saveInsertHistory(String mdmCd, String applyId) {
        log.info("매체코드 INSERT 이력 저장 시작 - 매체코드: {}, 적용자: {}", mdmCd, applyId);

        // 저장된 데이터 조회
        MdmCode savedData = mdmCodeQueryService.findMdmCodeByMdmCd(mdmCd);

        // INSERT 이력 DTO 생성
        MdmCodeHistoryRequest historyData = MdmCodeHistoryRequest.forInsertWithSavedData(savedData, applyId);

        // 이력 저장
        saveHistory(historyData, MdmCodeHistoryChangeType.INSERT, applyId, null);

        log.info("매체코드 INSERT 이력 저장 완료 - 매체코드: {}", mdmCd);
    }

    @Override
    @Transactional
    public void saveUpdateHistory(String mdmCd, MdmCode beforeData, String applyId) {
        log.info("매체코드 UPDATE 이력 저장 시작 - 매체코드: {}, 적용자: {}", mdmCd, applyId);

        MdmCode updateData = mdmCodeQueryService.findMdmCodeByMdmCd(mdmCd);

        // 변경 감지
        List<String> changedColumns = MdmCodeChangeDetector.detectChangedColumns(beforeData, updateData);
        
        log.info("변경된 컬럼: {} - 매체코드: {}", changedColumns, mdmCd);

        // 변경사항이 없는 경우 이력 저장 생략
        if (changedColumns.isEmpty()) {
            log.info("매체코드 UPDATE 이력 저장 생략 - 변경사항 없음. 매체코드: {}", mdmCd);
            return;
        }

        // UPDATE 이력 DTO 생성
        MdmCodeHistoryRequest historyData = MdmCodeHistoryRequest.forUpdateWithAfterData(updateData, applyId);

        // 이력 저장
        saveHistory(historyData, MdmCodeHistoryChangeType.UPDATE, applyId, changedColumns);

        log.info("매체코드 UPDATE 이력 저장 완료 - 매체코드: {}, 변경 컬럼 수: {}", mdmCd, changedColumns.size());
    }

    @Override
    @Transactional
    public void saveDeleteHistory(String mdmCd, MdmCode deletedData, String applyId) {
        log.info("매체코드 DELETE 이력 저장 시작 - 매체코드: {}, 적용자: {}", mdmCd, applyId);

        // DELETE 이력 DTO 생성
        MdmCodeHistoryRequest historyData = MdmCodeHistoryRequest.forDelete(deletedData, applyId);
        
        // 이력 저장
        saveHistory(historyData, MdmCodeHistoryChangeType.DELETE, applyId, null);

        log.info("매체코드 DELETE 이력 저장 완료 - 매체코드: {}", mdmCd);
    }

    /**
     * 이력 저장 공통 처리
     * 작업 타입(C/U/D)에 따라 적절한 이력 관리기능 수행
     *
     * @param historyData 이력 저장용 데이터
     * @param changeType 변경 타입
     * @param applyId 편집자 ID
     * @param changedColumns 변경된 컬럼 목록 (UPDATE인 경우만)
     */
    private void saveHistory(MdmCodeHistoryRequest historyData, MdmCodeHistoryChangeType changeType, String applyId, List<String> changedColumns) {
        // 이력 시퀀스 조회
        Long histSeq = mdmCodeHistoryCommandMapper.getNextMdmCodeHistorySequence();
        
        // 매체코드 전체 데이터 이력 저장
        int affectedRows = mdmCodeHistoryCommandMapper.createMdmCodeHistory(
                histSeq,
                historyData,
                changeType.getCode(),   // ENUM에 등록된 C/U/D에 대응되는 코드로 변환 후 DB에 저장
                applyId
        );

        if (affectedRows <= 0) {
            throw new CommonException(
                    CommonErrorCode.INTERNAL_SERVER_ERROR,
                    String.format(
                            "매체코드 이력 저장에 실패했습니다. mdmCd: %s, changeType: %s",
                            historyData.getMdmCd(), changeType
                    )
            );
        }
        
        // 매체코드 변경된 컬럼에 대한 이력 저장 (변경된 컬럼이 존재할때만 -> update operation)
        if (MdmCodeHistoryChangeType.UPDATE.equals(changeType) && changedColumns != null && !changedColumns.isEmpty()) {
            saveChangedColumnHistory(histSeq, changedColumns);
        }
    }
    
    /**
     * 변경된 컬럼 이력 저장
     *
     * @param histSeq 이력 시퀀스
     * @param changedColumns 변경된 컬럼 목록
     */
    private void saveChangedColumnHistory(Long histSeq, List<String> changedColumns) {
        for (String columnName : changedColumns) {
            int affectedRows = mdmCodeHistoryCommandMapper.createMdmCodeColumnHistory(histSeq, columnName);

            if (affectedRows <= 0) {
                throw new CommonException(
                        CommonErrorCode.INTERNAL_SERVER_ERROR,
                        String.format(
                                "매체코드 컬럼 이력 저장에 실패했습니다. histSeq: %s, columnName: %s",
                                histSeq, columnName
                        )
                );
            }
        }

        log.info("매체코드 컬럼 이력 저장 완료 - 이력 시퀀스: {}, 저장된 컬럼 수: {}", histSeq, changedColumns.size());
    }

}