package kr.or.komca.admin.code.bank.service.command;

import kr.or.komca.admin.code.bank.dto.command.request.ManageBankRequest;
import kr.or.komca.admin.code.bank.dto.command.request.sub.CreateBank;
import kr.or.komca.admin.code.bank.dto.command.request.sub.UpdateBank;
import kr.or.komca.admin.code.bank.dto.command.response.ManageBank;
import kr.or.komca.admin.code.bank.mapper.command.BankCommandMapper;
import kr.or.komca.admin.code.bank.service.query.BankQueryService;
import kr.or.komca.common.exception.core.CommonException;
import kr.or.komca.common.exception.response.error.code.CommonErrorCode;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Slf4j
@RequiredArgsConstructor
@Service
public class BankCommandServiceImpl implements BankCommandService {

    private final BankCommandMapper bankCommandMapper;
    private final BankQueryService bankQueryService;

    @Override
    @Transactional
    public ManageBank manageBank(ManageBankRequest request, String inspersId) {
        log.info("은행 통합 관리 요청: {}", request);

        // 각 작업 수행 결과
        int createAffectedRows = 0;
        int updateAffectedRows = 0;
        int deleteAffectedRows = 0;

        List<CreateBank> create = request.getCreate();

        // create 요청이 존재한다면 은행 추가
        if (create != null && !create.isEmpty()) {
            for (CreateBank createBank : create) {
                createAffectedRows += createBank(createBank, inspersId);
            }
        }

        List<UpdateBank> update = request.getUpdate();

        // update 요청이 존재한다면 은행 수정
        if (update != null && !update.isEmpty()) {
            for (UpdateBank updateBank : update) {
                updateAffectedRows += updateBank(updateBank.getBankCd(), inspersId, updateBank);
            }
        }

        List<String> delete = request.getDelete();

        // delete 요청이 존재한다면 은행 삭제
        if (delete != null && !delete.isEmpty()) {
            for (String bankCd : delete) {
                deleteAffectedRows += deleteBank(bankCd);
            }
        }

        return ManageBank.builder()
                .createAffectedRows(createAffectedRows)
                .updateAffectedRows(updateAffectedRows)
                .deleteAffectedRows(deleteAffectedRows)
                .build();
    }

    // ================== Private Create Methods ==================

    private int createBank(CreateBank request, String inspersId) {
        log.info("은행 생성: {}", request);

        String bankCd = request.getBankCd();

        // 은행 중복 확인
        bankQueryService.validateBankDuplicate(bankCd);

        int affectedRows = bankCommandMapper.createBank(bankCd, inspersId, request);

        if (affectedRows <= 0) {
            throw new CommonException(CommonErrorCode.INTERNAL_SERVER_ERROR,
                    String.format("은행 생성에 실패했습니다. bankCd: %s", bankCd));
        }

        return affectedRows;
    }

    private int updateBank(String bankCd, String modpersId, UpdateBank request) {
        log.info("은행 수정: bankCd={}, request={}", bankCd, request);

        // 존재하는 코드인지 확인
        bankQueryService.validateExistBank(bankCd);

        int affectedRows = bankCommandMapper.updateBank(bankCd, modpersId, request);

        if (affectedRows <= 0) {
            throw new CommonException(
                    CommonErrorCode.INTERNAL_SERVER_ERROR,
                    String.format("은행 수정에 실패했습니다. bankCd: %s", bankCd)
            );
        }

        return affectedRows;
    }

    // ================== Private Delete Methods ==================

    private int deleteBank(String bankCd) {
        log.info("은행 삭제: bankCd={}", bankCd);

        // 존재하는 코드인지 확인
        bankQueryService.validateExistBank(bankCd);

        int affectedRows = bankCommandMapper.deleteBank(bankCd);

        if (affectedRows <= 0) {
            throw new CommonException(
                    CommonErrorCode.INTERNAL_SERVER_ERROR,
                    String.format("은행 삭제에 실패했습니다. bankCd: %s", bankCd)
            );
        }

        return affectedRows;
    }
}
