package kr.or.komca.admin.code.account.service.query;

import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import kr.or.komca.admin.code.account.dto.query.condition.AccountSearchCondition;
import kr.or.komca.admin.code.account.dto.query.response.Account;
import kr.or.komca.admin.code.account.mapper.query.AccountQueryMapper;
import kr.or.komca.admin.usermanagement.dto.query.response.UserManagement;
import kr.or.komca.common.exception.core.CommonException;
import kr.or.komca.common.exception.response.error.code.CommonErrorCode;
import kr.or.komca.common.utils.core.dto.response.ListResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Slf4j
@RequiredArgsConstructor
@Service
@Transactional(readOnly = true)
public class AccountQueryServiceImpl implements AccountQueryService {

    private final AccountQueryMapper accountQueryMapper;

    @Override
    public ListResponse<Account> getAccountList(AccountSearchCondition condition) {
        try (Page<UserManagement> page = PageHelper.startPage(1, Integer.MAX_VALUE, true)) {
            List<Account> accountList = accountQueryMapper.getAccountList(condition);

            long totalCount = page.getTotal();

            return ListResponse.<Account>builder()
                .contents(accountList)
                .totalElements(totalCount)
                .build();

        } catch (Exception e) {
            log.error("페이징 처리 오류 발생", e);
            throw new CommonException(CommonErrorCode.INTERNAL_SERVER_ERROR, "페이징 처리 오류 발생");
        }
    }
}
