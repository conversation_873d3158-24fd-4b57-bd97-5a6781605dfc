package kr.or.komca.admin.code.bank.dto.query.response;

import io.swagger.v3.oas.annotations.media.Schema;
import kr.or.komca.admin.code.bank.util.BankSortColumnValidator;
import lombok.*;

import java.time.LocalDateTime;

/**
 * 은행 응답 DTO
 */
@ToString
@Getter
@AllArgsConstructor
@NoArgsConstructor
@Builder(toBuilder = true)
public class Bank {
    /** 은행코드 */
    @Schema(description = "은행코드", example = "001")
    private String bankCd;

    /** 은행명 */
    @Schema(description = "은행명", example = "한국은행")
    private String bankNm;

    /** 비고 */
    @Schema(description = "비고", example = "사용")
    private String remak;

    /** 대표은행 */
    @Schema(description = "대표은행", example = "Y")
    private String bankCdYn;

    /** 신탁사용여부 */
    @Schema(description = "신탁사용여부", example = "Y")
    private String trustUseYn;

    /** 홈페이지사용여부 */
    @Schema(description = "홈페이지사용여부", example = "Y")
    private String hompUseYn;

    /** 등록자 ID */
    @Schema(description = "등록자 ID", example = "admin")
    private String inspersId;

    /** 등록일시 */
    @Schema(description = "등록일시", example = "2024-05-01T10:30:00")
    private LocalDateTime insDt;

    /** 수정자 ID */
    @Schema(description = "수정자 ID", example = "admin")
    private String modpersId;

    /** 수정일시 */
    @Schema(description = "수정일시", example = "2024-05-02T15:45:00")
    private LocalDateTime modDt;
}