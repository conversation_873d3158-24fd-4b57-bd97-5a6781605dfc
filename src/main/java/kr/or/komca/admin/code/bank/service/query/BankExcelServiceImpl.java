package kr.or.komca.admin.code.bank.service.query;

import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import kr.or.komca.admin.code.bank.dto.query.condition.BankExcelCondition;
import kr.or.komca.admin.code.bank.dto.query.response.BankExcel;
import kr.or.komca.admin.code.bank.mapper.query.BankExcelMapper;
import kr.or.komca.common.exception.core.CommonException;
import kr.or.komca.common.exception.response.error.code.CommonErrorCode;
import kr.or.komca.common.utils.core.code.common.service.query.CommonCodeModuleQueryService;
import kr.or.komca.common.utils.core.dto.response.PageListResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

@Slf4j
@RequiredArgsConstructor
@Service
public class BankExcelServiceImpl implements BankExcelService {

    private final BankExcelMapper bankExcelMapper;
    private final CommonCodeModuleQueryService commonCodeModuleQueryService;

    // -------------- EXCEL ----------------

    @Override
    public PageListResponse<BankExcel> getBankExcelList(BankExcelCondition condition) {
        try (Page<BankExcel> page = PageHelper.startPage(condition.getPage(), condition.getPageSize())) {
            List<BankExcel> parCodeList = bankExcelMapper.getBankExcelList(condition);

            PageInfo<BankExcel> pageInfo = new PageInfo<>(parCodeList);

            return PageListResponse.<BankExcel>builder()
                    .contents(pageInfo.getList())
                    .totalElements(pageInfo.getTotal())
                    .page(pageInfo.getPageNum())
                    .totalPages(pageInfo.getPages())
                    .pageSize(pageInfo.getPageSize())
                    .build();

        } catch (Exception e) {
            log.error("페이징 처리 오류 발생", e);
            throw new CommonException(CommonErrorCode.INTERNAL_SERVER_ERROR, "페이징 처리 오류 발생");
        }
    }
}
