package kr.or.komca.admin.code.payment.service.command;

import kr.or.komca.admin.code.payment.dto.command.request.ManagePaymentRequest;
import kr.or.komca.admin.code.payment.dto.command.response.ManagePayment;

public interface PaymentCommandService {

    /**
     * 지급공제 통합 관리
     *
     * @param request 통합 관리 요청
     * @param inspersId 사용자 ID
     * @return 관리 결과
     */
    ManagePayment managePayment(ManagePaymentRequest request, String inspersId);
}
