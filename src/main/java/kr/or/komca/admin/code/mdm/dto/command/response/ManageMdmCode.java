package kr.or.komca.admin.code.mdm.dto.command.response;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;

/**
 * 매체 코드 통합 업데이트 응답 DTO
 */
@NoArgsConstructor
@AllArgsConstructor
@Getter
@Builder
public class ManageMdmCode {

	/** 생성된 행 수 */
	@Schema(description = "생성 행 수", example = "3")
	private int createAffectedRows;

	/** 수정된 행 수 */
	@Schema(description = "수정 행 수", example = "3")
	private int updateAffectedRows;

	/** 삭제된 행 수 */
	@Schema(description = "삭제 행 수", example = "3")
	private int deleteAffectedRows;
}
