package kr.or.komca.admin.code.payment.dto.command.request.sub;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Pattern;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.ToString;

/**
 * 지급공제 수정 요청
 */
@Getter
@Builder
@AllArgsConstructor
@ToString
@Schema(description = "지급공제 수정 요청")
public class UpdatePayment {

    /**
     * 지급공제 구분
     */
    @Schema(description = "지급공제 구분", example = "1")
    @NotBlank
    @Size(max = 10)
    private String suppDedctGbn;

    /**
     * 지급공제 코드
     */
    @Schema(description = "지급공제 코드", example = "101")
    @NotBlank
    @Size(max = 10)
    private String suppDedctCd;

    /**
     * 변경항목명
     */
    @Schema(description = "변경항목명", example = "기본급")
    @NotBlank
    @Size(max = 50)
    private String chgNm;

    /**
     * 퇴직적용여부
     */
    @Schema(description = "퇴직적용여부", example = "1")
    @NotBlank
    @Size(max = 1)
    private String retirYn;

    /**
     * 변동 금액
     */
    @Schema(description = "변동 금액", example = "0")
    @NotNull
    private Long chgAmt;

    /**
     * 고용보험포함여부
     */
    @Schema(description = "고용보험포함여부", example = "1")
    @NotBlank
    @Size(max = 1)
    private String empinsYn;

    /**
     * 1할계산여부
     */
    @Schema(description = "1할계산여부", example = "1")
    @NotBlank
    @Size(max = 1)
    private String perCaltnYn;

    /**
     * 단위계산
     */
    @Schema(description = "단위계산", example = "03")
    @NotBlank
    @Size(max = 256)
    private String unitCaltn;

    /**
     * 계정 코드
     */
    @Schema(description = "계정 코드", example = "0430")
    @NotBlank
    @Size(max = 256)
    private String acctCd;

    /**
     * 비고
     */
    @Schema(description = "비고", example = "인정경력 포함 계산(상여적용)")
    @Size(max = 256)
    private String remak;

    /**
     * 사용구분
     */
    @Schema(description = "사용구분", example = "Y")
    @NotBlank
    @Pattern(regexp = "^[YN]$")
    private String useYn;
}
