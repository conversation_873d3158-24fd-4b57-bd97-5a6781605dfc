package kr.or.komca.admin.code.mdm.mapper.command;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;

/**
 * 관리수수료율 Command 매퍼
 */
@Mapper
public interface MngComisCommandMapper {

    /**
     * 관리수수료율 존재 여부 확인
     *
     * @param largeClassCd 대분류코드
     * @param aveClassCd 중분류코드
     * @param smallClassCd 소분류코드
     * @param mdmCd 매체코드
     * @param applYrmn 적용년월 (YYYYMM)
     * @return 존재 여부
     */
    boolean existsMngComisRate(
            @Param("largeClassCd") String largeClassCd,
            @Param("aveClassCd") String aveClassCd,
            @Param("smallClassCd") String smallClassCd,
            @Param("mdmCd") String mdmCd,
            @Param("applYrmn") String applYrmn
    );

    /**
     * 관리수수료율 생성
     *
     * @param largeClassCd 대분류코드
     * @param aveClassCd 중분류코드
     * @param smallClassCd 소분류코드
     * @param mdmCd 매체코드
     * @param applYrmn 적용년월 (YYYYMM)
     * @param mngComisRate 관리수수료율
     * @return 영향받은 행 수
     */
    int insertMngComisRate(
            @Param("largeClassCd") String largeClassCd,
            @Param("aveClassCd") String aveClassCd,
            @Param("smallClassCd") String smallClassCd,
            @Param("mdmCd") String mdmCd,
            @Param("applYrmn") String applYrmn,
            @Param("mngComisRate") BigDecimal mngComisRate
    );

    /**
     * 관리수수료율 수정
     *
     * @param largeClassCd 대분류코드
     * @param aveClassCd 중분류코드
     * @param smallClassCd 소분류코드
     * @param mdmCd 매체코드
     * @param applYrmn 적용년월 (YYYYMM)
     * @param mngComisRate 관리수수료율
     * @return 영향받은 행 수
     */
    int updateMngComisRate(
            @Param("largeClassCd") String largeClassCd,
            @Param("aveClassCd") String aveClassCd,
            @Param("smallClassCd") String smallClassCd,
            @Param("mdmCd") String mdmCd,
            @Param("applYrmn") String applYrmn,
            @Param("mngComisRate") BigDecimal mngComisRate
    );
}