package kr.or.komca.admin.code.bank.api;


import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import kr.or.komca.admin.code.bank.dto.command.request.ManageBankRequest;
import kr.or.komca.admin.code.bank.dto.command.response.ManageBank;
import kr.or.komca.admin.code.bank.dto.query.condition.BankExcelCondition;
import kr.or.komca.admin.code.bank.dto.query.condition.BankSearchCondition;
import kr.or.komca.admin.code.bank.dto.query.response.Bank;
import kr.or.komca.admin.code.bank.dto.query.response.BankExcel;
import kr.or.komca.common.exception.response.error.CommonErrorResponse;
import kr.or.komca.common.exception.response.success.CommonSuccessResponse;
import kr.or.komca.common.utils.core.dto.response.PageListResponse;
import org.springframework.http.ResponseEntity;

@Tag(name = "Bank", description = "은행")
public interface BankApi {

    /** ======== Query API ======== */

    @Operation(
            summary = "은행 목록 조회",
            description = "검색 조건에 맞는 은행 목록을 조회합니다."
    )
    @ApiResponses({
            @ApiResponse(
                    responseCode = "200",
                    description = "조회 성공"
            ),
            @ApiResponse(
                    responseCode = "400",
                    description = "잘못된 요청",
                    content = @Content(schema = @Schema(implementation = CommonErrorResponse.class))
            ),
    })
    ResponseEntity<CommonSuccessResponse<PageListResponse<Bank>>> getBankList(
            @Parameter(description = "은행 검색 조건", required = true)
            BankSearchCondition condition
    );

    @Operation(
            summary = "은행 목록 조회",
            description = "검색 조건에 맞는 은행 목록을 엑셀 조회합니다."
    )
    @ApiResponses({
            @ApiResponse(
                    responseCode = "200",
                    description = "조회 성공"
            ),
            @ApiResponse(
                    responseCode = "400",
                    description = "잘못된 요청",
                    content = @Content(schema = @Schema(implementation = CommonErrorResponse.class))
            ),
    })
    ResponseEntity<CommonSuccessResponse<PageListResponse<BankExcel>>> getBankExcelList(
            @Parameter(description = "은행 검색 조건", required = true)
            BankExcelCondition condition
    );

    /** ======== Command API ======== */

    @Operation(
            summary = "은행 통합 관리",
            description = "은행를 일괄로 생성/수정/삭제합니다."
    )
    @ApiResponses({
            @ApiResponse(
                    responseCode = "200",
                    description = "처리 성공"
            ),
            @ApiResponse(
                    responseCode = "400",
                    description = "잘못된 요청",
                    content = @Content(schema = @Schema(implementation = CommonErrorResponse.class))
            ),
    })
    ResponseEntity<CommonSuccessResponse<ManageBank>> manageBank(
            @Parameter(description = "은행 통합 관리 요청", required = true)
            ManageBankRequest request
    );
}
