package kr.or.komca.admin.code.mdm.dto.command.request.sub;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Pattern;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.ToString;

/**
 * 대분류 코드 생성 요청
 */
@Getter
@Builder
@AllArgsConstructor
@ToString
@Schema(description = "대분류 코드 생성 요청")
public class CreateLargeCode {
    
    /** 대분류코드(1자) */
    @Schema(description = "대분류코드", example = "A", minLength = 1, maxLength = 1)
    @NotBlank
    @Size(min = 1, max = 1)
    private String largeClassCd;
    
    /** 대분류코드명(0-50자) */
    @Schema(description = "대분류코드명", example = "방송", minLength = 0, maxLength = 50)
    @NotBlank
    @Size(max = 50)
    private String largeClassNm;

    /** 사용여부 */
    @Schema(description = "사용여부", example = "Y", allowableValues = {"Y", "N"})
    @NotNull
    @Pattern(regexp = "^[YN]$")
    private String useYn;

    /** 정렬순서 */
    @Schema(description = "정렬순서", example = "1")
    @NotNull
    private Integer sortOrd;
}
