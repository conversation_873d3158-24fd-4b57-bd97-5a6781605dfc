package kr.or.komca.admin.code.payment.dto.query.response;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

import java.time.LocalDateTime;

/**
 * 지급공제 응답 DTO
 */
@ToString
@Getter
@AllArgsConstructor
@NoArgsConstructor
@Builder(toBuilder = true)
public class Payment {
    /**
     * 지급공제 구분
     */
    @Schema(description = "지급공제 구분", example = "1")
    private String suppDedctGbn;

    /**
     * 지급공제 코드
     */
    @Schema(description = "지급공제 코드", example = "101")
    private String suppDedctCd;

    /**
     * 변경항목명
     */
    @Schema(description = "변경항목명", example = "기본급")
    private String chgNm;

    /**
     * 퇴직적용여부
     */
    @Schema(description = "퇴직적용여부", example = "1")
    private String retirYn;

    /**
     * 변동 금액
     */
    @Schema(description = "변동 금액", example = "0")
    private Long chgAmt;

    /**
     * 고용보험포함여부
     */
    @Schema(description = "고용보험포함여부", example = "1")
    private String empinsYn;

    /**
     * 1할계산여부
     */
    @Schema(description = "1할계산여부", example = "1")
    private String perCaltnYn;

    /**
     * 단위계산
     */
    @Schema(description = "단위계산", example = "03")
    private String unitCaltn;

    /**
     * 계정 코드
     */
    @Schema(description = "계정 코드", example = "0430")
    private String acctCd;

    /**
     * 비고
     */
    @Schema(description = "비고", example = "인정경력 포함 계산(상여적용)")
    private String remak;

    /**
     * 사용구분
     */
    @Schema(description = "사용구분", example = "Y")
    private String useYn;

    /**
     * 계정명
     */
    @Schema(description = "계정명", example = "기본급")
    private String acctNm;

    /**
     * 등록자 ID
     */
    @Schema(description = "등록자 ID", example = "admin")
    private String inspersId;

    /**
     * 등록일시
     */
    @Schema(description = "등록일시", example = "2024-05-01T10:30:00")
    private LocalDateTime insDt;

    /**
     * 수정자 ID
     */
    @Schema(description = "수정자 ID", example = "admin")
    private String modpersId;

    /**
     * 수정일시
     */
    @Schema(description = "수정일시", example = "2024-05-02T15:45:00")
    private LocalDateTime modDt;

    /**
     * 지급공제명
     */
    @Schema(description = "지급공제명", example = "매입계산서")
    private String suppDedctCdNm;

    /**
     * 지급공제 구분명
     */
    @Schema(description = "지급공제 구분명", example = "지급")
    private String suppDedctGbnNm;

    /**
     * 단위계산 명
     */
    @Schema(description = "단위계산 명", example = "원단위절사")
    private String unitCaltnNm;
}