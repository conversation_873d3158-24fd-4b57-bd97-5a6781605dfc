package kr.or.komca.admin.code.payment.enums.errorcode;

import io.swagger.v3.oas.annotations.media.Schema;
import kr.or.komca.common.interfaces.response.code.ErrorCode;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpStatus;

/**
 * 공통 코드에 대한 ErrorEnum 처리
 * 1. 상수명: 대문자 스네이크 케이스로 작성 (예: INVALID_CODE_STATUS)
 * 2. code: 상수명과 동일하게 문자열로 작성
 * 3. status: 적절한 HTTP 상태 코드 설정 (HttpStatus enum 사용)
 */
@Getter
@RequiredArgsConstructor
public enum PaymentErrorCode implements ErrorCode {

    /**
     * 지급공제 관련 에러
     * 발생 상황:
     * - 존재하지 않는 지급공제에 접근할 때
     * - 이미 존재하는 지급공제를 생성하려 할 때
     */
    @Schema(description = "상위 코드를 찾을 수 없음")
    PAYMENT_NOT_FOUND("PAYMENT_NOT_FOUND", HttpStatus.NOT_FOUND),

    @Schema(description = "이미 존재하는 상위 코드")
    PAYMENT_ALREADY_EXISTS("PAYMENT_ALREADY_EXISTS", HttpStatus.CONFLICT);

    private final String code;
    private final HttpStatus status;
}
