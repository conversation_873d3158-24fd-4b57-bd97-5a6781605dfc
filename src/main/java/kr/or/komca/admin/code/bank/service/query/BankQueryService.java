package kr.or.komca.admin.code.bank.service.query;

import kr.or.komca.admin.code.bank.dto.query.condition.BankSearchCondition;
import kr.or.komca.admin.code.bank.dto.query.response.Bank;
import kr.or.komca.common.utils.core.dto.response.PageListResponse;

public interface BankQueryService {

    /**
     * 은행 목록 조회
     * @param condition 검색 조건
     * @return 은행 목록
     */
    PageListResponse<Bank> getBankList(BankSearchCondition condition);

    /**
     * 은행 존재 여부 확인
     * @param parCd 은행
     */
    void validateExistBank(String parCd);

    /**
     * 은행가 중복되지 않는지 검증
     * @param parCd 은행
     */
    void validateBankDuplicate(String parCd);
}
