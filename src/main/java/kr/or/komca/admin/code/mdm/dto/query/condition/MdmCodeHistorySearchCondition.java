package kr.or.komca.admin.code.mdm.dto.query.condition;

import io.swagger.v3.oas.annotations.media.Schema;
import kr.or.komca.admin.code.mdm.util.MdmCodeHistorySortColumnValidator;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.ToString;

/**
 * 매체코드 이력 조회에 대한 조건 DTO
 */
@ToString
@Getter
@AllArgsConstructor
@Builder(toBuilder = true)
@Schema(description = "매체코드 이력 조회에 대한 조건 DTO")
public class MdmCodeHistorySearchCondition {

    /** 페이지 번호 */
    @Schema(description = "페이지 번호", example = "1")
    private int page;

    /** 페이지 크기 */
    @Schema(description = "페이지 크기", example = "10")
    private int pageSize;

    /** 정렬할 컬럼 */
    @Schema(description = "정렬할 컬럼", example = "applyDt")
    private String sortColumn;

    /** 정렬 순서 1: asc. 2: desc */
    @Schema(description = "정렬 순서 (1: 오름차순, 2: 내림차순)", example = "2", allowableValues = {"1", "2"})
    private String sortOrder;

    /** 매체코드 검색 */
    @Schema(description = "매체코드 (부분 검색 가능)", example = "AA0103")
    private String mdmCd;

    /** 매체명 검색 */
    @Schema(description = "매체명 (부분 검색 가능)", example = "KBS")
    private String mdmNm;

    /** 변경구분 필터 */
    @Schema(description = "변경구분 (01: INSERT, 02: UPDATE, 03: DELETE)", example = "01")
    private String chgTypeCd;

    /** 시작일자 */
    @Schema(description = "조회 시작일자 (YYYY-MM-DD)", example = "2024-01-01")
    private String startDate;

    /** 종료일자 */
    @Schema(description = "조회 종료일자 (YYYY-MM-DD)", example = "2024-12-31")
    private String endDate;

    /** 변경자 ID */
    @Schema(description = "변경자 ID (부분 검색 가능)", example = "admin")
    private String applyId;

    /** 소분류코드 */
    @Schema(description = "소분류코드 (해당 소분류 하위의 모든 매체코드 조회)", example = "AA01")
    private String smallClassCd;

    public String getSortColumn() {
        String validatedColumn = MdmCodeHistorySortColumnValidator.validate(this.sortColumn);
        return validatedColumn != null ? validatedColumn : "APPLY_DT";
    }

    public String getSortOrder() {
        if ("1".equals(this.sortOrder)) return "ASC";
        if ("2".equals(this.sortOrder)) return "DESC";
        return "DESC";
    }
}