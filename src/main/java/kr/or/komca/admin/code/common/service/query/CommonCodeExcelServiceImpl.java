package kr.or.komca.admin.code.common.service.query;

import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import kr.or.komca.admin.code.common.dto.query.condition.DetailCodeExcelCondition;
import kr.or.komca.admin.code.common.dto.query.condition.ParCodeExcelCondition;
import kr.or.komca.admin.code.common.dto.query.response.DetailCodeExcel;
import kr.or.komca.admin.code.common.dto.query.response.ParCodeExcel;
import kr.or.komca.admin.code.common.mapper.query.CommonCodeExcelMapper;
import kr.or.komca.common.exception.core.CommonException;
import kr.or.komca.common.exception.response.error.code.CommonErrorCode;
import kr.or.komca.common.utils.core.code.common.service.query.CommonCodeModuleQueryService;
import kr.or.komca.common.utils.core.dto.response.PageListResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

@Slf4j
@RequiredArgsConstructor
@Service
public class CommonCodeExcelServiceImpl implements CommonCodeExcelService {

    private final CommonCodeExcelMapper commonCodeExcelMapper;
    private final CommonCodeModuleQueryService commonCodeModuleQueryService;

    // -------------- EXCEL ----------------

    @Override
    public PageListResponse<ParCodeExcel> getParCodeExcelList(ParCodeExcelCondition condition) {
        try (Page<ParCodeExcel> page = PageHelper.startPage(condition.getPage(), condition.getPageSize())) {
            List<ParCodeExcel> parCodeList = commonCodeExcelMapper.getParCodeExcelList(condition);

            PageInfo<ParCodeExcel> pageInfo = new PageInfo<>(parCodeList);

            return PageListResponse.<ParCodeExcel>builder()
                    .contents(pageInfo.getList())
                    .totalElements(pageInfo.getTotal())
                    .page(pageInfo.getPageNum())
                    .totalPages(pageInfo.getPages())
                    .pageSize(pageInfo.getPageSize())
                    .build();

        } catch (Exception e) {
            log.error("페이징 처리 오류 발생", e);
            throw new CommonException(CommonErrorCode.INTERNAL_SERVER_ERROR, "페이징 처리 오류 발생");
        }
    }

    @Override
    public PageListResponse<DetailCodeExcel> getDetailCodeExcelList(String parCd, DetailCodeExcelCondition condition) {

        commonCodeModuleQueryService.validateExistParCode(parCd);

        try (Page<ParCodeExcel> page = PageHelper.startPage(condition.getPage(), condition.getPageSize())) {
            List<DetailCodeExcel> detailCodes = commonCodeExcelMapper.getDetailCodeExcelList(parCd, condition);

            PageInfo<DetailCodeExcel> pageInfo = new PageInfo<>(detailCodes);

            return PageListResponse.<DetailCodeExcel>builder()
                    .contents(pageInfo.getList())
                    .totalElements(pageInfo.getTotal())
                    .page(pageInfo.getPageNum())
                    .totalPages(pageInfo.getPages())
                    .pageSize(pageInfo.getPageSize())
                    .build();
        }
    }
}
