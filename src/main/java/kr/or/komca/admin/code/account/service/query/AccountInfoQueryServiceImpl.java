package kr.or.komca.admin.code.account.service.query;

import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import kr.or.komca.admin.code.account.dto.query.condition.AccountInfoSearchCondition;
import kr.or.komca.admin.code.account.dto.query.response.AccountInfo;
import kr.or.komca.admin.code.account.enums.errorcode.AccountCodeErrorCode;
import kr.or.komca.admin.code.account.mapper.query.AccountInfoQueryMapper;
import kr.or.komca.admin.usermanagement.dto.query.response.UserManagement;
import kr.or.komca.common.exception.core.CommonException;
import kr.or.komca.common.exception.response.error.code.CommonErrorCode;
import kr.or.komca.common.utils.core.dto.response.ListResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Slf4j
@RequiredArgsConstructor
@Service
@Transactional(readOnly = true)
public class AccountInfoQueryServiceImpl implements AccountInfoQueryService {

    private final AccountInfoQueryMapper accountInfoQueryMapper;

    @Override
    public ListResponse<AccountInfo> getAccountInfoList(AccountInfoSearchCondition condition) {
        try (Page<UserManagement> page = PageHelper.startPage(1, Integer.MAX_VALUE, true)) {
            List<AccountInfo> accountList = accountInfoQueryMapper.getAccountInfoList(condition);

            long totalCount = page.getTotal();

            return ListResponse.<AccountInfo>builder()
                .contents(accountList)
                .totalElements(totalCount)
                .build();

        } catch (Exception e) {
            log.error("페이징 처리 오류 발생", e);
            throw new CommonException(CommonErrorCode.INTERNAL_SERVER_ERROR, "페이징 처리 오류 발생");
        }
    }

    @Override
    public void validateExistAccount(String acctCd) {
        AccountInfo account = accountInfoQueryMapper.getAccountByCd(acctCd);

        if (account == null) {
            throw new CommonException(
                CommonErrorCode.NOT_FOUND,
                String.format("존재하지 않는 계정입니다: %s", acctCd)
            );
        }
    }

    @Override
    public void validateAccountDuplicate(String acctCd) {
        AccountInfo account = accountInfoQueryMapper.getAccountByCd(acctCd);

        if (account != null) {
            throw new CommonException(
                AccountCodeErrorCode.CODE_ALREADY_EXISTS,
                String.format("이미 존재하는 계정입니다: %s", acctCd)
            );
        }
    }
}
