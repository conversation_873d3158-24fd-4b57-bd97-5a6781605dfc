package kr.or.komca.admin.code.account.util;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

/**
 * Sort Validator
 */
@Slf4j
@Component
public class AccountSortColumnValidator {

    /**
     * 허용된 정렬 컬럼 매핑 정보
     * Key: 프론트엔드 요청 파라미터명
     * Value: 실제 DB 테이블의 컬럼명
     */
    private static final Map<String, String> ALLOWED_SORT_COLUMNS_MAP = new HashMap<>() {
        {
            put("acctCd", "acct_cd");                       // 계정코드
            put("acctNm", "acct_nm");                       // 계정명
            put("parAcctCd", "par_acct_cd");                // 상위계정코드
            put("parAcctSeq", "par_acct_seq");              // 상위계정순번
            put("jobGbn", "job_gbn");                       // 업무구분
            put("jobGbnNm", "job_gbn_nm");                  // 업무구분명
            put("drCrGbn", "dr_cr_gbn");                    // 차대구분
            put("drCrGbnNm", "dr_cr_gbn_nm");               // 차대구분
            put("crovYn", "crov_yn");                       // 이월여부
            put("acctnGbnTrustYn", "acctn_gbn_trust_yn");   // 회계구분신탁여부
            put("acctnGbnNormalYn", "acctn_gbn_normal_yn"); // 회계구분일반여부
            put("acctnGbnHallYn", "acctn_gbn_hall_yn");     // 회계구분회관여부
            put("acctnGbnMbYn", "acctn_gbn_mb_yn");         // 회계구분회원여부
            put("acctnGbnElderYn", "acctn_gbn_elder_yn");   // 회계구분원로여부
            put("provYn", "prov_yn");                       // 충당여부
            put("provAmtLinkCd", "prov_amt_link_cd");       // 충당금연결코드
            put("comisRate", "comis_rate");                 // 수수료율
            put("insDt", "ins_dt");                         // 등록일시
            put("inspersId", "inspers_id");                 // 등록사원
            put("modDt", "mod_dt");                         // 수정일시
            put("modpersId", "modpers_id");                 // 수정사원
            put("useYn", "use_yn");                         // 사용여부
            put("acctClass", "acct_class");                 // 계정분류
            put("apprvYn", "apprv_yn");                     // 승인여부
            put("bgYn", "bg_yn");                           // 예산여부
            put("readOrd", "read_ord");                     // 조회순서
            put("settYn", "sett_yn");                       // 반제여부
            put("slipYn", "slip_yn");                           // 전표여부

        }
    };

    /**
     * 정렬 컬럼 유효성 검사 및 매핑
     */
    public static String validate(String sortColumn) {
        return ALLOWED_SORT_COLUMNS_MAP.get(sortColumn);
    }
}