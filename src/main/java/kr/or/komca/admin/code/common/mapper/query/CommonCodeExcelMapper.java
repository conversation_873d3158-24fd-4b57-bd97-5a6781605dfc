package kr.or.komca.admin.code.common.mapper.query;

import kr.or.komca.admin.code.common.dto.query.condition.DetailCodeExcelCondition;
import kr.or.komca.admin.code.common.dto.query.condition.ParCodeExcelCondition;
import kr.or.komca.admin.code.common.dto.query.response.DetailCodeExcel;
import kr.or.komca.admin.code.common.dto.query.response.ParCodeExcel;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface CommonCodeExcelMapper {

    /**
     * 상위 코드 목록 엑셀 조회
     * @param condition 검색 조건
     * @return 상위 코드 목록
     */
    List<ParCodeExcel> getParCodeExcelList(@Param("condition") ParCodeExcelCondition condition);

    /**
     * 세부 코드 목록 엑셀 조회
     * @param condition 검색 조건
     * @return 세부 코드 목록
     */
    List<DetailCodeExcel> getDetailCodeExcelList(@Param("parCd") String parCd, @Param("condition") DetailCodeExcelCondition condition);

}
