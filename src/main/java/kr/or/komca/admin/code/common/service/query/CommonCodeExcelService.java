package kr.or.komca.admin.code.common.service.query;

import kr.or.komca.admin.code.common.dto.query.condition.DetailCodeExcelCondition;
import kr.or.komca.admin.code.common.dto.query.condition.ParCodeExcelCondition;
import kr.or.komca.admin.code.common.dto.query.response.DetailCodeExcel;
import kr.or.komca.admin.code.common.dto.query.response.ParCodeExcel;
import kr.or.komca.common.utils.core.dto.response.PageListResponse;

public interface CommonCodeExcelService {

    /**
     * 상위 코드 목록 엑셀 조회
     *
     * @param condition 검색 조건
     * @return 상위 코드 리스트
     */
    PageListResponse<ParCodeExcel> getParCodeExcelList(ParCodeExcelCondition condition);

    /**
     * 하위 코드 목록 엑셀 조회
     *
     * @param parCd 상위 코드
     * @param condition 검색 조건
     * @return 하위 코드 리스트
     */
    PageListResponse<DetailCodeExcel> getDetailCodeExcelList(String parCd, DetailCodeExcelCondition condition);
}
