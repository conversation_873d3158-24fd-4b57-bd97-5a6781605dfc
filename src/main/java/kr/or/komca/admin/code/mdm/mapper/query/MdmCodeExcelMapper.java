package kr.or.komca.admin.code.mdm.mapper.query;

import kr.or.komca.admin.code.mdm.dto.query.condition.ServiceCodeExcelCondition;
import kr.or.komca.admin.code.mdm.dto.query.response.ServiceCodeExcel;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 매체코드 엑셀 관련 매퍼 인터페이스
 */
@Mapper
public interface MdmCodeExcelMapper {

    /**
     * 서비스코드 엑셀 목록 조회
     * 
     * @param mdmCd 매체코드
     * @param condition 검색 조건
     * @return 서비스코드 엑셀 목록
     */
    List<ServiceCodeExcel> getServiceCodeExcelList(@Param("mdmCd") String mdmCd, @Param("condition") ServiceCodeExcelCondition condition);
}
