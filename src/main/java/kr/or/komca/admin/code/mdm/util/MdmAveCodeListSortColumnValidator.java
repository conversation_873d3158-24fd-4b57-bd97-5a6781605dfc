package kr.or.komca.admin.code.mdm.util;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

/**
 * Sort Validator
 */
@Slf4j
@Component
public class MdmAveCodeListSortColumnValidator {

	/**
	 * 허용된 정렬 컬럼 매핑 정보
	 * Key: 프론트엔드 요청 파라미터명
	 * Value: 실제 DB 테이블의 컬럼명
	 */
	private static final Map<String, String> ALLOWED_SORT_COLUMNS_MAP = new HashMap<>() {{
		put("aveClassCd", "ave_class_cd");                      // 중분류코드
		put("aveClassNm", "ave_class_nm");                      // 중분류명
		put("sortOrd", "TO_NUMBER(sort_ord)");                  // 정렬순서
		put("useYn", "use_yn");                                 // 사용여부
		put("domAcctCd", "dom_acct_cd");                        // 국내계정코드
		put("abrAcctCd", "abr_acct_cd");                        // 국외계정코드
		put("domBranAcctCd", "dom_bran_acct_cd");               // 국내지부계정코드
		put("abrBranAcctCd", "abr_bran_acct_cd");               // 국외지부계정코드
	}};

	/**
	 * 정렬 컬럼 유효성 검사 및 매핑
	 */
	public static String validate(String sortColumn) {
		return ALLOWED_SORT_COLUMNS_MAP.get(sortColumn);
	}
}
