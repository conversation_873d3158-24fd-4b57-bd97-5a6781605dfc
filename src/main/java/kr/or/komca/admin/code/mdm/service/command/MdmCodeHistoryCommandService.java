package kr.or.komca.admin.code.mdm.service.command;

import kr.or.komca.admin.code.mdm.dto.command.request.sub.CreateMdmCode;
import kr.or.komca.admin.code.mdm.dto.command.request.sub.UpdateMdmCode;
import kr.or.komca.admin.code.mdm.dto.query.response.MdmCode;

import java.math.BigDecimal;

/**
 * 매체코드 이력 관리 Command 서비스 인터페이스
 */
public interface MdmCodeHistoryCommandService {

    /**
     * INSERT 이력 저장
     *
     * @param mdmCd 매체코드
     * @param applyId 이력 적용자 ID
     */
    void saveInsertHistory(String mdmCd, String applyId);

    /**
     * UPDATE 이력 저장 (변경된 현재 값 + 변경 컬럼 정보)
     *
     * @param mdmCd 매체코드
     * @param beforeData 수정 전 데이터
     * @param applyId 이력 적용자 ID
     */
    void saveUpdateHistory(String mdmCd, MdmCode beforeData, String applyId);

    /**
     * DELETE 이력 저장
     *
     * @param mdmCd 매체코드
     * @param applyId 이력 적용자 ID
     */
    void saveDeleteHistory(String mdmCd, MdmCode deletedCode, String applyId);
}