package kr.or.komca.admin.code.common.dto.command.request.sub;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Pattern;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.ToString;

/**
 * 상위 코드 수정 요청
 */
@Getter
@Builder
@AllArgsConstructor
@ToString
@Schema(description = "상위 코드 수정 요청")
public class UpdateParCode {

    /** 상위 코드 */
    @Schema(description = "상위 코드", example = "00001")
    @NotBlank
    @Size(max = 10)
    private String parCd;

    /** 상위 코드명 */
    @Schema(description = "상위 코드명", example = "회원 상태")
    @NotBlank
    @Size(max = 50)
    private String parCdNm;

    /** 비고 */
    @Schema(description = "비고", example = "회원 상태 코드")
    @Size(max = 4000)
    private String remak;

    /** 사용 여부 */
    @Schema(description = "사용 여부", example = "Y")
    @NotBlank
    @Pattern(regexp = "^[YN]$")
    private String useYn;
}
