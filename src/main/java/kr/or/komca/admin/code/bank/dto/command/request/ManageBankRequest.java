package kr.or.komca.admin.code.bank.dto.command.request;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.Valid;
import kr.or.komca.admin.code.bank.dto.command.request.sub.CreateBank;
import kr.or.komca.admin.code.bank.dto.command.request.sub.UpdateBank;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.ToString;

import java.util.List;

/**
 * 은행 통합 관리 요청 DTO
 */
@Getter
@Builder
@AllArgsConstructor
@ToString
@Schema(description = "은행 통합 관리 요청 (C/U/D)")
public class ManageBankRequest {

    @Schema(description = "생성 목록")
    private List<@Valid CreateBank> create;

    @Schema(description = "수정 목록")
    private List<@Valid UpdateBank> update;

    @Schema(description = "삭제 목록 (삭제 은행 리스트)")
    private List<String> delete;
}
