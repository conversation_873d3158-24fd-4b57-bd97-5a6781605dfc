package kr.or.komca.admin.code.mdm.service.query;


import kr.or.komca.admin.code.mdm.dto.query.condition.*;
import kr.or.komca.admin.code.mdm.dto.query.response.*;
import kr.or.komca.common.utils.core.dto.response.PageListResponse;

/**
 * 공통 코드 로직을 처리하는 서비스 인터페이스
 */
public interface MdmCodeQueryService {

	/**
	 * 매체코드(대분류) 조회
	 *
	 * @return 매체코드(대분류) 조회에 대한 결과
	 */
	PageListResponse<MdmLargeCode> getLargeMdmCodeList(
			MdmLargeCodeSearchCondition condition
	);

	/**
	 * 매체코드(중분류) 조회
	 *
	 * @param largeClassCd 대분류 코드
	 * @return 매체코드(중분류) 조회에 대한 결과
	 */
	PageListResponse<MdmAveCode> getAveMdmCodeList(
			MdmAveCodeSearchCondition condition,
			String largeClassCd
	);

	/**
	 * 매체코드(소분류) 조회
	 *
	 * @param aveClassCd   중분류 코드
	 * @return 매체코드(소분류) 조회에 대한 결과
	 */
	PageListResponse<MdmSmallCode> getSmallMdmCodeList(
			MdmSmallCodeSearchCondition condition,
			String aveClassCd
	);

	/**
	 * 매체코드(매체) 조회
	 *
	 * @param smallClassCd 소분류 코드
	 * @return 매체코드(매체) 조회에 대한 결과
	 */
	PageListResponse<MdmCode> getMdmCodeList(
			MdmCodeSearchCondition condition,
			String smallClassCd
	);


	/**
	 * 서비스 코드 조회
	 *
	 * @param mdmCd 매체 코드
	 * @return 서비스 코드 조회에 대한 결과
	 */
	PageListResponse<ServiceCode> getServiceCodeList(
			ServiceCodeSearchCondition condition,
			String mdmCd
	);
	
	/**
	 * 대분류 코드 존재 여부 확인
	 *
	 * @param largeClassCd 대분류 코드
	 * @return 존재 여부
	 */
	boolean existsLargeCode(String largeClassCd);
	
	/**
	 * 중분류 코드 존재 여부 확인
	 *
	 * @param aveClassCd 중분류 코드
	 * @return 존재 여부
	 */
	boolean existsAveCode(String aveClassCd);
	
	/**
	 * 소분류 코드 존재 여부 확인
	 *
	 * @param smallClassCd 소분류 코드
	 * @return 존재 여부
	 */
	boolean existsSmallCode(String smallClassCd);
	
	/**
	 * 매체 코드 존재 여부 확인
	 *
	 * @param mdmCd 매체 코드
	 * @return 존재 여부
	 */
	boolean existsMdmCode(String mdmCd);
	
	/**
	 * 서비스 코드 존재 여부 확인
	 *
	 * @param svcCd 서비스 코드
	 * @return 존재 여부
	 */
	boolean existsServiceCode(String svcCd);
	
	/**
	 * 대분류 코드가 존재하는지 검증
	 *
	 * @param largeClassCd 대분류 코드
	 */
	void validateLargeCodeExists(String largeClassCd);
	
	/**
	 * 대분류 코드가 중복되지 않는지 검증
	 *
	 * @param largeClassCd 대분류 코드
	 */
	void validateLargeCodeDuplicate(String largeClassCd);

	/**
	 * 중분류 코드가 존재하는지 검증
	 *
	 * @param aveClassCd 중분류 코드
	 */
	void validateAveCodeExists(String aveClassCd);

	/**
	 * 중분류 코드가 중복되지 않는지 검증
	 *
	 * @param aveClassCd 중분류 코드
	 */
	void validateAveCodeDuplicate(String aveClassCd);

	/**
	 * 소분류 코드가 존재하는지 검증
	 *
	 * @param smallClassCd 소분류 코드
	 */
	void validateSmallCodeExists(String smallClassCd);

	/**
	 * 소분류 코드가 중복되지 않는지 검증
	 *
	 * @param smallClassCd 소분류 코드
	 */
	void validateSmallCodeDuplicate(String smallClassCd);

	/**
	 * 매체 코드가 존재하는지 검증
	 *
	 * @param mdmCd 매체 코드
	 */
	void validateMdmCodeExists(String mdmCd);

	/**
	 * 매체 코드가 중복되지 않는지 검증
	 *
	 * @param mdmCd 매체 코드
	 */
	void validateMdmCodeDuplicate(String mdmCd);

	/**
	 * 서비스 코드가 존재하는지 검증
	 *
	 * @param svcCd 서비스 코드
	 */
	void validateServiceCodeExists(String svcCd);

	/**
	 * 서비스 코드가 중복되지 않는지 검증
	 *
	 * @param svcCd 서비스 코드
	 */
	void validateServiceCodeDuplicate(String svcCd);
	
	/**
	 * 서비스코드로 매체코드 조회
	 *
	 * @param svcCd 서비스코드
	 * @return 매체코드
	 */
	String findMdmCdByServiceCode(String svcCd);

	/**
	 * 매체코드 단건 조회
	 *
	 * @param mdmCd 매체코드
	 * @return 매체코드 정보
	 */
	MdmCode findMdmCodeByMdmCd(String mdmCd);

	/**
	 * 매체 코드 계층 구조 검증
	 *
	 * @param largeClassCd 대분류 코드
	 * @param aveClassCd 중분류 코드 (nullable)
	 * @param smallClassCd 소분류 코드 (nullable)
	 * @param mdmCd 매체 코드 (nullable)
	 * @param svcCd 서비스 코드 (nullable)
	 */
	void validateMdmCodeHierarchy(
			String largeClassCd,
			String aveClassCd,
			String smallClassCd,
			String mdmCd,
			String svcCd
	);

	/**
	 * 매체 코드 계층 무결성 검증
	 * null 값 이후의 모든 필드는 반드시 null이어야 함
	 *
	 * @param largeClassCd 대분류 코드 (nullable)
	 * @param aveClassCd 중분류 코드 (nullable)
	 * @param smallClassCd 소분류 코드 (nullable)
	 * @param mdmCd 매체 코드 (nullable)
	 * @param svcCd 서비스 코드 (nullable)
	 */
	void validateMdmCodeSequentialHierarchy(
			String largeClassCd,
			String aveClassCd,
			String smallClassCd,
			String mdmCd,
			String svcCd
	);
}
