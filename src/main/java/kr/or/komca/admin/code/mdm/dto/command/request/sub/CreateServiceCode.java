package kr.or.komca.admin.code.mdm.dto.command.request.sub;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Pattern;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.ToString;

import java.math.BigDecimal;

/**
 * 서비스 코드 생성 요청
 */
@Getter
@Builder
@AllArgsConstructor
@ToString
@Schema(description = "서비스 코드 생성 요청")
public class CreateServiceCode {

    /** 서비스코드 */
    @Schema(description = "서비스코드", example = "701", minLength = 8, maxLength = 8)
    @NotBlank
    @Size(max = 8)
    private String svcCd;

    /** 서비스명 */
    @Schema(description = "서비스명", example = "TBN 도로교통", minLength = 0, maxLength = 100)
    @NotBlank
    @Size(max = 100)
    private String svcNm;

    /** 거래처코드 */
    @Schema(description = "거래처코드", example = "G0129")
    private String bsconCd;

    /** 분배유형 [ common - 00303 ] */
    @Schema(description = "분배유형", example = "3")
    private String distrType;

    /** 사용료율 */
    @Schema(description = "사용료율(%)", example = "1.35")
    private BigDecimal rtalRate;

    /** 곡단가금액 */
    @Schema(description = "곡단가금액", example = "0")
    private BigDecimal tuneUncoAmt;

    /** 관리비율 */
    @Schema(description = "관리비율(%)", example = "100.0")
    private BigDecimal mngRate;

    /** 공제계수 */
    @Schema(description = "공제계수(%)", example = "70.0")
    private BigDecimal dedctCffnt;

    /** 조정계수 */
    @Schema(description = "조정계수(%)", example = "46.0")
    private BigDecimal adjCffnt;

    /** 침해가산율 */
    @Schema(description = "침해가산율(%)", example = "30.0")
    private BigDecimal ifmntAddRate;

    /** 할인율 */
    @Schema(description = "할인율(%)", example = "0.0")
    private BigDecimal dsctRate;

    /** 월정액금액 */
    @Schema(description = "월정액금액(원)", example = "0")
    private Long monFamtAmt;

    /** 하한가금액 */
    @Schema(description = "하한가금액(원)", example = "0")
    private Long minAmt;

    /** 산출산식 */
    @Schema(description = "산출산식", example = "매출액 * 사용료율 * 관리비율")
    private String caltnAtex;

    /** 징수규정 */
    @Schema(description = "징수규정", example = "매출액 * 사용료율 * 관리비율, 단가금액 * 가입자수 * 관리비율")
    private String levyPvsn;

    /** 부가세여부 */
    @Schema(description = "부가세여부(1:과세,0:비과세)", example = "1", allowableValues = {"1", "0"})
    @NotNull
    @Pattern(regexp = "^[01]$")
    private String ataxYn;

    /** 사용여부 */
    @Schema(description = "사용여부(Y:사용,N:미사용)", example = "Y", allowableValues = {"Y", "N"})
    @NotNull
    @Pattern(regexp = "^[YN]$")
    private String useYn;
}
