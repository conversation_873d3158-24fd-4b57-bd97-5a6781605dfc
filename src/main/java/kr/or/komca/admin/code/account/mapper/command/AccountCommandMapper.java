package kr.or.komca.admin.code.account.mapper.command;

import kr.or.komca.admin.code.account.dto.command.request.sub.CreateAccount;
import kr.or.komca.admin.code.account.dto.command.request.sub.UpdateAccount;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface AccountCommandMapper {

    /**
     * 계정 저장
     *
     * @param acctCd  계정 코드
     * @param command 저장할 계정 정보
     * @return 저장된 행 수
     */
    int createAccount(
        @Param("acctCd") String acctCd,
        @Param("inspersId") String inspersId,
        @Param("command") CreateAccount command
    );

    /**
     * 계정 수정
     *
     * @param acctCd  계정 코드
     * @param command 수정할 계정 정보
     * @return 수정된 행 수
     */
    int updateAccount(
        @Param("acctCd") String acctCd,
        @Param("modpersId") String modpersId,
        @Param("command") UpdateAccount command
    );

    /**
     * 계정 삭제
     *
     * @param acctCd 계정 코드
     * @return 삭제된 행 수
     */
    int deleteAccount(@Param("acctCd") String acctCd);
}
