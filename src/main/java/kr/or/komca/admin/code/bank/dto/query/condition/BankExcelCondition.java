package kr.or.komca.admin.code.bank.dto.query.condition;

import io.swagger.v3.oas.annotations.media.Schema;
import kr.or.komca.admin.code.bank.util.BankSortColumnValidator;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.ToString;

/**
 * 은행 검색 조건 DTO
 */
@ToString
@Getter
@AllArgsConstructor
@Builder(toBuilder = true)
public class BankExcelCondition {
    /** 은행코드 */
    @Schema(description = "은행코드", example = "001")
    private String bankCd;

    /** 은행명 */
    @Schema(description = "은행명", example = "한국은행")
    private String bankNm;

    /** 페이지 번호 */
    @Schema(description = "페이지 번호", example = "1")
    private int page;
    
    /** 페이지 크기 */
    @Schema(description = "페이지 크기", example = "10")
    private int pageSize;

    /** 정렬할 컬럼 **/
    @Schema(description = "정렬할 컬럼", example = "bankCd")
    private String sortColumn;

    /** 정렬 순서 1: asc. 2: desc  */
    @Schema(description = "정렬 순서 (1: asc, 2: desc)", example = "1")
    private String sortOrder;

    public String getSortColumn() {
        return BankSortColumnValidator.validate(this.sortColumn);
    }

    public String getSortOrder() {
        if ("1".equals(this.sortOrder)) return "ASC";
        if ("2".equals(this.sortOrder)) return "DESC";
        return "";
    }
}