package kr.or.komca.admin.code.mdm.mapper.query;

import kr.or.komca.admin.code.mdm.dto.query.condition.MdmCodeHistorySearchCondition;
import kr.or.komca.admin.code.mdm.dto.query.response.MdmCodeHistory;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 매체코드 이력 조회 매퍼
 */
@Mapper
public interface MdmCodeHistoryQueryMapper {

    /**
     * 매체코드 이력 목록을 페이징으로 조회합니다.
     *
     * @param condition 검색 조건
     * @return 매체코드 이력 목록
     */
    List<MdmCodeHistory> getMdmCodeHistoryList(@Param("condition") MdmCodeHistorySearchCondition condition);

}