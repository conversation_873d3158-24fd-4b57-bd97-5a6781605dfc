package kr.or.komca.admin.code.mdm.service.query;

import kr.or.komca.admin.code.mdm.dto.query.condition.MdmCodeHistorySearchCondition;
import kr.or.komca.admin.code.mdm.dto.query.response.MdmCodeHistory;
import kr.or.komca.common.utils.core.dto.response.PageListResponse;

/**
 * 매체코드 이력 조회 서비스 인터페이스
 */
public interface MdmCodeHistoryQueryService {

    /**
     * 매체코드 이력 목록을 페이징으로 조회합니다.
     *
     * @param condition 검색 조건
     * @return 매체코드 이력 목록
     */
    PageListResponse<MdmCodeHistory> getMdmCodeHistoryList(MdmCodeHistorySearchCondition condition);
}