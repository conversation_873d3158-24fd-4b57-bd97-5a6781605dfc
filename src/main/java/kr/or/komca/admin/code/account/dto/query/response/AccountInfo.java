package kr.or.komca.admin.code.account.dto.query.response;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

/**
 * 계정 응답 DTO
 */
@ToString
@Getter
@AllArgsConstructor
@NoArgsConstructor
@Builder(toBuilder = true)
public class AccountInfo {
    /**
     * 계정코드
     */
    @Schema(description = "계정코드", example = "0002")
    private String acctCd;

    /**
     * 계정명
     */
    @Schema(description = "계정명", example = "유동자산")
    private String acctNm;

    /**
     * 상위계정코드
     */
    @Schema(description = "상위계정코드", example = "0001")
    private String parAcctCd;

    /**
     * 상위계정순번
     */
    @Schema(description = "상위계정순번", example = "3")
    private String parAcctSeq;

    /**
     * 업무구분
     */
    @Schema(description = "업무구분", example = "001")
    private String jobGbn;

    /**
     * 업무구분명
     */
    @Schema(description = "업무구분명", example = "손익계산서")
    private String jobGbnNm;

    /**
     * 차대구분
     */
    @Schema(description = "차대구분", example = "001")
    private String drCrGbn;

    /**
     * 차대구분 명
     */
    @Schema(description = "차대구분 명", example = "차변")
    private String drCrGbnNm;

    /**
     * 이월여부
     */
    @Schema(description = "이월여부", example = "Y")
    private String crovYn;

    /**
     * 회계구분신탁여부
     */
    @Schema(description = "회계구분신탁여부", example = "Y")
    private String acctnGbnTrustYn;

    /**
     * 회계구분일반여부
     */
    @Schema(description = "회계구분일반여부", example = "Y")
    private String acctnGbnNormalYn;

    /**
     * 회계구분회관여부
     */
    @Schema(description = "회계구분회관여부", example = "Y")
    private String acctnGbnHallYn;

    /**
     * 회계구분회원여부
     */
    @Schema(description = "회계구분회원여부", example = "Y")
    private String acctnGbnMbYn;

    /**
     * 회계구분원로여부
     */
    @Schema(description = "회계구분원로여부", example = "Y")
    private String acctnGbnElderYn;

    /**
     * 충당여부
     */
    @Schema(description = "충당여부", example = "Y")
    private String provYn;

    /**
     * 충당금연결코드
     */
    @Schema(description = "충당금연결코드", example = "0086")
    private String provAmtLinkCd;

    /**
     * 수수료율
     */
    @Schema(description = "수수료율", example = "0.00")
    private String comisRate;

    /**
     * 계정분류
     */
    @Schema(description = "계정분류", example = "002")
    private String acctClass;

    /**
     * 승인여부
     */
    @Schema(description = "승인여부", example = "Y")
    private String apprvYn;

    /**
     * 조회순서
     */
    @Schema(description = "조회순서", example = "1")
    private String readOrd;

    /**
     * 전표여부
     */
    @Schema(description = "전표여부", example = "N")
    private String slipYn;
}