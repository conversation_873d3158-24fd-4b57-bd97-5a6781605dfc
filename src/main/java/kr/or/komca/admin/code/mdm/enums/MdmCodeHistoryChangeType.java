package kr.or.komca.admin.code.mdm.enums;

import lombok.Getter;

/**
 * 매체코드 이력 변경 타입
 */
@Getter
public enum MdmCodeHistoryChangeType {
    
    INSERT("01", "INSERT"),
    UPDATE("02", "UPDATE"), 
    DELETE("03", "DELETE");
    
    private final String code;
    private final String name;
    
    MdmCodeHistoryChangeType(String code, String name) {
        this.code = code;
        this.name = name;
    }

}