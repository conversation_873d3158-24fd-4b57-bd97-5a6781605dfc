package kr.or.komca.admin.code.common.mapper.query;

import kr.or.komca.admin.code.common.dto.query.condition.DetailCodeSearchCondition;
import kr.or.komca.admin.code.common.dto.query.condition.ParCodeSearchCondition;
import kr.or.komca.admin.code.common.dto.query.response.DetailCode;
import kr.or.komca.admin.code.common.dto.query.response.ParCode;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface CommonCodeQueryMapper {

    /**
     * 상위 코드 목록 조회
     * @param condition 검색 조건
     * @return 상위 코드 목록
     */
    List<ParCode> getParCodeList(@Param("condition") ParCodeSearchCondition condition);

    /**
     * id로 상위 코드 조회
     * @param parCd 상위 코드
     * @return 단일 상위 코드
     */
    ParCode getParCodeById(@Param("parCd") String parCd);
    
    /**
     * 세부 코드 목록 조회
     * @param condition 검색 조건
     * @return 세부 코드 목록
     */
    List<DetailCode> getDetailCodeList(@Param("parCd") String parCd, @Param("condition") DetailCodeSearchCondition condition);

    /**
     * 세부 코드 개수 조회
     * @param parCd 상위 코드
     * @return 세부 코드 개수
     */
    int countDetailCode(@Param("parCd") String parCd);

    /**
     * id로 세부 코드 조회
     * @param parCd 상위 코드
     * @param cd 세부 코드
     * @return 단일 세부 코드
     */
    DetailCode getDetailCodeById(@Param("parCd") String parCd,  @Param("cd") String cd);
    
    /**
     * 상위 코드 전체 개수 조회
     * @param condition 검색 조건
     * @return 상위 코드 전체 개수
     */
    int countParCode(@Param("condition") ParCodeSearchCondition condition);

    String getNextParCd();

    String getNextDetailCd(@Param("parCd") String parCd);

    /**
     * 상위 코드 부서 매핑 존재 여부 확인
     * @param parCd 상위 코드
     * @param deptCd 부서 코드
     * @return 존재 여부
     */
    boolean existsParCodeGroupUser(@Param("parCd") String parCd, @Param("deptCd") String deptCd);
}
