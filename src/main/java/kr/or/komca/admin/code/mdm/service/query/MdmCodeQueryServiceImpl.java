package kr.or.komca.admin.code.mdm.service.query;

import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import kr.or.komca.admin.code.mdm.dto.query.condition.*;
import kr.or.komca.admin.code.mdm.dto.query.response.*;
import kr.or.komca.admin.code.mdm.enums.errorcode.MdmCodeErrorCode;
import kr.or.komca.admin.code.mdm.mapper.query.MdmCodeQueryMapper;
import kr.or.komca.common.exception.core.CommonException;
import kr.or.komca.common.utils.core.dto.response.PageListResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Slf4j
@Service
@RequiredArgsConstructor
public class MdmCodeQueryServiceImpl implements MdmCodeQueryService {

	private final MdmCodeQueryMapper mdmCodeQueryMapper;

	/**
	 * 매체코드(대분류) 조회
	 */
	@Override
	@Transactional(readOnly = true)
	public PageListResponse<MdmLargeCode> getLargeMdmCodeList(MdmLargeCodeSearchCondition condition) {
		log.info("대분류 매체코드 목록 조회 요청 - page: {}, pageSize: {}", condition.getPage(), condition.getPageSize());

		try (Page<MdmLargeCode> page = PageHelper.startPage(condition.getPage(), condition.getPageSize())) {
			List<MdmLargeCode> mdmLargeCodeList = mdmCodeQueryMapper.getLargeMdmCodeList(condition);

			PageInfo<MdmLargeCode> pageInfo = new PageInfo<>(mdmLargeCodeList);

			PageListResponse<MdmLargeCode> result = PageListResponse.<MdmLargeCode>builder()
					.contents(pageInfo.getList())
					.totalElements(pageInfo.getTotal())
					.page(pageInfo.getPageNum())
					.totalPages(pageInfo.getPages())
					.pageSize(pageInfo.getPageSize())
					.build();
					
			return result;
		}
	}

	/**
	 * 매체코드(중분류) 조회
	 */
	@Override
	@Transactional(readOnly = true)
	public PageListResponse<MdmAveCode> getAveMdmCodeList(MdmAveCodeSearchCondition condition, String largeClassCd) {
		log.info("중분류 매체코드 목록 조회 요청 - largeClassCd: {}, page: {}, pageSize: {}", largeClassCd, condition.getPage(), condition.getPageSize());

		try (Page<MdmAveCode> page = PageHelper.startPage(condition.getPage(), condition.getPageSize(), true)) {
			List<MdmAveCode> mdmAveCodeList = mdmCodeQueryMapper.getAveMdmCodeList(condition, largeClassCd);

			PageInfo<MdmAveCode> pageInfo = new PageInfo<>(mdmAveCodeList);

			return PageListResponse.<MdmAveCode>builder()
					.contents(pageInfo.getList())
					.totalElements(pageInfo.getTotal())
					.page(pageInfo.getPageNum())
					.totalPages(pageInfo.getPages())
					.pageSize(pageInfo.getPageSize())
					.build();
		}
	}

	/**
	 * 매체코드(소분류) 조회
	 */
	@Override
	@Transactional(readOnly = true)
	public PageListResponse<MdmSmallCode> getSmallMdmCodeList(MdmSmallCodeSearchCondition condition, String aveClassCd) {
		log.info("소분류 매체코드 목록 조회 요청 - aveClassCd: {}, page: {}, pageSize: {}", aveClassCd, condition.getPage(), condition.getPageSize());

		try (Page<MdmSmallCode> page = PageHelper.startPage(condition.getPage(), condition.getPageSize())) {
			List<MdmSmallCode> mdmSmallCodeList = mdmCodeQueryMapper.getSmallMdmCodeList(condition, aveClassCd);

			PageInfo<MdmSmallCode> pageInfo = new PageInfo<>(mdmSmallCodeList);

			return PageListResponse.<MdmSmallCode>builder()
					.contents(pageInfo.getList())
					.totalElements(pageInfo.getTotal())
					.page(pageInfo.getPageNum())
					.totalPages(pageInfo.getPages())
					.pageSize(pageInfo.getPageSize())
					.build();
		}
	}

	/**
	 * 매체코드(매체) 조회
	 */
	@Override
	@Transactional(readOnly = true)
	public PageListResponse<MdmCode> getMdmCodeList(MdmCodeSearchCondition condition, String smallClassCd) {
		log.info("매체코드 목록 조회 요청 - smallClassCd: {}, page: {}, pageSize: {}", smallClassCd, condition.getPage(), condition.getPageSize());

		try (Page<MdmCode> page = PageHelper.startPage(condition.getPage(), condition.getPageSize())) {
			List<MdmCode> mdmCodeList = mdmCodeQueryMapper.getMdmCodeList(
					condition, smallClassCd
			);

			PageInfo<MdmCode> pageInfo = new PageInfo<>(mdmCodeList);

			return PageListResponse.<MdmCode>builder()
					.contents(pageInfo.getList())
					.totalElements(pageInfo.getTotal())
					.page(pageInfo.getPageNum())
					.totalPages(pageInfo.getPages())
					.pageSize(pageInfo.getPageSize())
					.build();
		}
	}

	@Override
	@Transactional(readOnly = true)
	public PageListResponse<ServiceCode> getServiceCodeList(
			ServiceCodeSearchCondition condition,
			String mdmCd
	) {
		log.info("서비스코드 목록 조회 요청 - mdmCd: {}, page: {}, pageSize: {}", mdmCd, condition.getPage(), condition.getPageSize());

		try (Page<ServiceCode> page = PageHelper.startPage(condition.getPage(), condition.getPageSize())) {

			List<ServiceCode> mdmCodeList = mdmCodeQueryMapper.getServiceCodeList(
					condition, mdmCd
			);

			PageInfo<ServiceCode> pageInfo = new PageInfo<>(mdmCodeList);

			return PageListResponse.<ServiceCode>builder()
					.contents(pageInfo.getList())
					.totalElements(pageInfo.getTotal())
					.page(pageInfo.getPageNum())
					.totalPages(pageInfo.getPages())
					.pageSize(pageInfo.getPageSize())
					.build();
		}
	}

	/**
	 * 대분류 코드 존재 여부 확인
	 */
	@Override
	@Transactional(readOnly = true)
	public boolean existsLargeCode(String largeClassCd) {
		return mdmCodeQueryMapper.existsLargeCode(largeClassCd);
	}

	/**
	 * 중분류 코드 존재 여부 확인
	 */
	@Override
	@Transactional(readOnly = true)
	public boolean existsAveCode(String aveClassCd) {
		return mdmCodeQueryMapper.existsAveCode(aveClassCd);
	}

	/**
	 * 소분류 코드 존재 여부 확인
	 */
	@Override
	@Transactional(readOnly = true)
	public boolean existsSmallCode(String smallClassCd) {
		return mdmCodeQueryMapper.existsSmallCode(smallClassCd);
	}

	/**
	 * 매체 코드 존재 여부 확인
	 */
	@Override
	@Transactional(readOnly = true)
	public boolean existsMdmCode(String mdmCd) {
		return mdmCodeQueryMapper.existsMdmCode(mdmCd);
	}

	/**
	 * 서비스 코드 존재 여부 확인
	 */
	@Override
	@Transactional(readOnly = true)
	public boolean existsServiceCode(String svcCd) {
		return mdmCodeQueryMapper.existsServiceCode(svcCd);
	}

	/**
	 * 대분류 코드가 존재하는지 검증
	 */
	@Override
	@Transactional(readOnly = true)
	public void validateLargeCodeExists(String largeClassCd) {
		if (!existsLargeCode(largeClassCd)) {
			throw new CommonException(MdmCodeErrorCode.LARGE_CODE_NOT_FOUND,
					String.format("대분류 코드를 찾을 수 없습니다. largeClassCd: %s", largeClassCd));
		}
	}

	/**
	 * 대분류 코드가 중복되지 않는지 검증
	 */
	@Override
	@Transactional(readOnly = true)
	public void validateLargeCodeDuplicate(String largeClassCd) {
		if (existsLargeCode(largeClassCd)) {
			throw new CommonException(MdmCodeErrorCode.LARGE_CODE_ALREADY_EXISTS,
					String.format("이미 존재하는 대분류 코드입니다. largeClassCd: %s", largeClassCd));
		}
	}

	/**
	 * 중분류 코드가 존재하는지 검증
	 */
	@Override
	@Transactional(readOnly = true)
	public void validateAveCodeExists(String aveClassCd) {
		if (!existsAveCode(aveClassCd)) {
			throw new CommonException(MdmCodeErrorCode.AVE_CODE_NOT_FOUND,
					String.format("중분류 코드를 찾을 수 없습니다. aveClassCd: %s", aveClassCd));
		}
	}

	/**
	 * 중분류 코드가 중복되지 않는지 검증
	 */
	@Override
	@Transactional(readOnly = true)
	public void validateAveCodeDuplicate(String aveClassCd) {
		if (existsAveCode(aveClassCd)) {
			throw new CommonException(MdmCodeErrorCode.AVE_CODE_ALREADY_EXISTS,
					String.format("이미 존재하는 중분류 코드입니다. aveClassCd: %s", aveClassCd));
		}
	}

	/**
	 * 소분류 코드가 존재하는지 검증
	 */
	@Override
	@Transactional(readOnly = true)
	public void validateSmallCodeExists(String smallClassCd) {
		if (!existsSmallCode(smallClassCd)) {
			throw new CommonException(MdmCodeErrorCode.SMALL_CODE_NOT_FOUND,
					String.format("소분류 코드를 찾을 수 없습니다. smallClassCd: %s", smallClassCd));
		}
	}

	/**
	 * 소분류 코드가 중복되지 않는지 검증
	 */
	@Override
	@Transactional(readOnly = true)
	public void validateSmallCodeDuplicate(String smallClassCd) {
		if (existsSmallCode(smallClassCd)) {
			throw new CommonException(MdmCodeErrorCode.SMALL_CODE_ALREADY_EXISTS,
					String.format("이미 존재하는 소분류 코드입니다. smallClassCd: %s", smallClassCd));
		}
	}

	/**
	 * 매체 코드가 존재하는지 검증
	 */
	@Override
	@Transactional(readOnly = true)
	public void validateMdmCodeExists(String mdmCd) {
		if (!existsMdmCode(mdmCd)) {
			throw new CommonException(MdmCodeErrorCode.MDM_CODE_NOT_FOUND,
					String.format("매체 코드를 찾을 수 없습니다. mdmCd: %s", mdmCd));
		}
	}

	/**
	 * 매체 코드가 중복되지 않는지 검증
	 */
	@Override
	@Transactional(readOnly = true)
	public void validateMdmCodeDuplicate(String mdmCd) {
		if (existsMdmCode(mdmCd)) {
			throw new CommonException(MdmCodeErrorCode.MDM_CODE_ALREADY_EXISTS,
					String.format("이미 존재하는 매체 코드입니다. mdmCd: %s", mdmCd));
		}
	}

	/**
	 * 서비스 코드가 존재하는지 검증
	 */
	@Override
	@Transactional(readOnly = true)
	public void validateServiceCodeExists(String svcCd) {
		if (!existsServiceCode(svcCd)) {
			throw new CommonException(MdmCodeErrorCode.SVC_CODE_NOT_FOUND,
					String.format("서비스 코드를 찾을 수 없습니다. svcCd: %s", svcCd));
		}
	}

	/**
	 * 서비스 코드가 중복되지 않는지 검증
	 */
	@Override
	@Transactional(readOnly = true)
	public void validateServiceCodeDuplicate(String svcCd) {
		if (existsServiceCode(svcCd)) {
			throw new CommonException(MdmCodeErrorCode.SVC_CODE_ALREADY_EXISTS,
					String.format("이미 존재하는 서비스 코드입니다. svcCd: %s", svcCd));
		}
	}

	/**
	 * 서비스코드로 매체코드 조회
	 */
	@Override
	@Transactional(readOnly = true)
	public String findMdmCdByServiceCode(String svcCd) {
		return mdmCodeQueryMapper.findMdmCdByServiceCode(svcCd);
	}

	/**
	 * 매체코드 단건 조회
	 */
	@Override
	@Transactional(readOnly = true)
	public MdmCode findMdmCodeByMdmCd(String mdmCd) {
		return mdmCodeQueryMapper.findMdmCodeByMdmCd(mdmCd);
	}

	/**
	 * 매체 코드 계층 구조 검증
	 * 각 코드가 존재하는지 확인하고 상위-하위 관계가 올바른지 검증
	 */
	@Override
	@Transactional(readOnly = true)
	public void validateMdmCodeHierarchy(
			String largeClassCd,
			String aveClassCd,
			String smallClassCd,
			String mdmCd,
			String svcCd
	) {
		// 대분류 코드 검증 (필수)
		validateLargeCodeExists(largeClassCd);

		// 중분류 코드 검증 (있는 경우만)
		if (aveClassCd != null && !aveClassCd.trim().isEmpty()) {
			// 중분류 코드 유효성 확인
			validateAveCodeExists(aveClassCd);
			// 중분류가 대분류에 속하는지 확인
			if (!mdmCodeQueryMapper.existsAveCodeInLargeClass(largeClassCd, aveClassCd)) {
				throw new CommonException(MdmCodeErrorCode.INVALID_HIERARCHY_STRUCTURE,
						String.format("중분류 코드가 대분류에 속하지 않습니다. largeClassCd=%s, aveClassCd=%s", largeClassCd, aveClassCd));
			}
		}

		// 소분류 코드 검증 (있는 경우만)
		if (smallClassCd != null && !smallClassCd.trim().isEmpty()) {
			// 소분류 코드 유효성 확인
			validateSmallCodeExists(smallClassCd);
			// 소분류가 중분류에 속하는지 확인 (중분류가 있는 경우만)
			if (aveClassCd != null && !aveClassCd.trim().isEmpty()) {
				if (!mdmCodeQueryMapper.existsSmallCodeInAveClass(aveClassCd, smallClassCd)) {
					throw new CommonException(MdmCodeErrorCode.INVALID_HIERARCHY_STRUCTURE,
							String.format("소분류 코드가 중분류에 속하지 않습니다. aveClassCd=%s, smallClassCd=%s", aveClassCd, smallClassCd));
				}
			}
		}

		// 매체 코드 검증 (있는 경우만)
		if (mdmCd != null && !mdmCd.trim().isEmpty()) {
			// 매체 코드 유효성 확인
			validateMdmCodeExists(mdmCd);
			// 매체코드가 소분류에 속하는지 확인 (소분류가 있는 경우만)
			if (smallClassCd != null && !smallClassCd.trim().isEmpty()) {
				if (!mdmCodeQueryMapper.existsMdmCodeInSmallClass(smallClassCd, mdmCd)) {
					throw new CommonException(MdmCodeErrorCode.INVALID_HIERARCHY_STRUCTURE,
							String.format("매체 코드가 소분류에 속하지 않습니다. smallClassCd=%s, mdmCd=%s", smallClassCd, mdmCd));
				}
			}
		}

		// 서비스 코드 검증 (있는 경우만)
		if (svcCd != null && !svcCd.trim().isEmpty()) {
			// 서비스 코드 유효성 확인
			validateServiceCodeExists(svcCd);
			// 서비스코드가 매체코드에 속하는지 확인 (매체코드가 있는 경우만)
			if (mdmCd != null && !mdmCd.trim().isEmpty()) {
				if (!mdmCodeQueryMapper.existsServiceCodeInMdmCode(mdmCd, svcCd)) {
					throw new CommonException(MdmCodeErrorCode.INVALID_HIERARCHY_STRUCTURE,
							String.format("서비스 코드가 매체 코드에 속하지 않습니다. mdmCd=%s, svcCd=%s", mdmCd, svcCd));
				}
			}
		}
	}

	/**
	 * 매체 코드 계층 무결성 검증
	 * null 값 이후의 모든 필드는 반드시 null이어야 함
	 */
	@Override
	@Transactional(readOnly = true)
	public void validateMdmCodeSequentialHierarchy(
			String largeClassCd,
			String aveClassCd,
			String smallClassCd,
			String mdmCd,
			String svcCd
	) {
		// Null 발견 flag
		boolean foundNull = false;

		// 대분류 코드 검증
		if (largeClassCd == null || largeClassCd.trim().isEmpty()) {
			foundNull = true;
		}

		// 중분류 코드 검증
		if (aveClassCd == null || aveClassCd.trim().isEmpty()) {
			foundNull = true;
		} else if (foundNull) {
			throw new CommonException(
					MdmCodeErrorCode.INVALID_HIERARCHY_STRUCTURE,
					"상위 계층이 null인 경우 하위 계층은 모두 null이어야 합니다."
			);
		}

		// 소분류 코드 검증
		if (smallClassCd == null || smallClassCd.trim().isEmpty()) {
			foundNull = true;
		} else if (foundNull) {
			throw new CommonException(
					MdmCodeErrorCode.INVALID_HIERARCHY_STRUCTURE,
					"상위 계층이 null인 경우 하위 계층은 모두 null이어야 합니다."
			);
		}

		// 매체 코드 검증
		if (mdmCd == null || mdmCd.trim().isEmpty()) {
			foundNull = true;
		} else if (foundNull) {
			throw new CommonException(
					MdmCodeErrorCode.INVALID_HIERARCHY_STRUCTURE,
					"상위 계층이 null인 경우 하위 계층은 모두 null이어야 합니다."
			);
		}

		// 서비스 코드 검증
		if ((svcCd != null && !svcCd.trim().isEmpty()) && foundNull) {
			throw new CommonException(
					MdmCodeErrorCode.INVALID_HIERARCHY_STRUCTURE,
					"상위 계층이 null인 경우 하위 계층은 모두 null이어야 합니다."
			);
		}
	}
}
