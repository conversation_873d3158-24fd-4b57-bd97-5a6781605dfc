package kr.or.komca.admin.code.bank.service.query;

import kr.or.komca.admin.code.bank.dto.query.condition.BankExcelCondition;
import kr.or.komca.admin.code.bank.dto.query.response.BankExcel;
import kr.or.komca.common.utils.core.dto.response.PageListResponse;

public interface BankExcelService {

    /**
     * 은행 목록 엑셀 조회
     *
     * @param condition 검색 조건
     * @return 은행 리스트
     */
    PageListResponse<BankExcel> getBankExcelList(BankExcelCondition condition);
}
