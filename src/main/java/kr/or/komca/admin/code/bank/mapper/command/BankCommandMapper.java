package kr.or.komca.admin.code.bank.mapper.command;

import kr.or.komca.admin.code.bank.dto.command.request.sub.CreateBank;
import kr.or.komca.admin.code.bank.dto.command.request.sub.UpdateBank;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface BankCommandMapper {

    /**
     * 은행 저장
     *
     * @param bankCd 은행 코드
     * @param command 저장할 은행 정보
     * @return 저장된 행 수
     */
    int createBank(
            @Param("bankCd") String bankCd,
            @Param("inspersId") String inspersId,
            @Param("command") CreateBank command
    );
    
    /**
     * 은행 수정
     * @param bankCd 은행 코드
     * @param command 수정할 은행 정보
     * @return 수정된 행 수
     */
    int updateBank(
            @Param("bankCd") String bankCd,
            @Param("modpersId") String modpersId,
            @Param("command") UpdateBank command
    );
    
    /**
     * 은행 삭제
     * @param bankCd 은행 코드
     * @return 삭제된 행 수
     */
    int deleteBank(@Param("bankCd") String bankCd);
}
