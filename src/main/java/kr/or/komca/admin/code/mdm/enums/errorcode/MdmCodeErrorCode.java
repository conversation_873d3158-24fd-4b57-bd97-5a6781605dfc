package kr.or.komca.admin.code.mdm.enums.errorcode;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpStatus;
import kr.or.komca.common.interfaces.response.code.ErrorCode;

/**
 * 매체코드 관리에 대한 ErrorEnum 처리
 * 1. 상수명: 대문자 스네이크 케이스로 작성 (예: INVALID_CODE_STATUS)
 * 2. code: 상수명과 동일하게 문자열로 작성
 * 3. status: 적절한 HTTP 상태 코드 설정 (HttpStatus enum 사용)
 */
@Getter
@RequiredArgsConstructor
public enum MdmCodeErrorCode implements ErrorCode {

    /**
     * 매체코드의 상태가 유효하지 않은 경우 발생하는 예외
     * 발생 상황:
     * - 이미 삭제된 매체코드에 접근할 때
     * - 처리 불가능한 상태의 매체코드를 수정하려 할 때
     */
    @Schema(description = "유효하지 않은 코드 상태")
    INVALID_CODE_STATUS("INVALID_CODE_STATUS", HttpStatus.BAD_REQUEST),

    /**
     * 코드를 찾을 수 없는 경우 발생하는 예외
     * 발생 상황:
     * - 존재하지 않는 코드에 접근할 때
     */
    @Schema(description = "대분류 코드를 찾을 수 없음")
    LARGE_CODE_NOT_FOUND("LARGE_CODE_NOT_FOUND", HttpStatus.NOT_FOUND),

    @Schema(description = "중분류 코드를 찾을 수 없음")
    AVE_CODE_NOT_FOUND("AVE_CODE_NOT_FOUND", HttpStatus.NOT_FOUND),

    @Schema(description = "소분류 코드를 찾을 수 없음")
    SMALL_CODE_NOT_FOUND("SMALL_CODE_NOT_FOUND", HttpStatus.NOT_FOUND),

    @Schema(description = "서비스 코드를 찾을 수 없음")
    SVC_CODE_NOT_FOUND("SVC_CODE_NOT_FOUND", HttpStatus.NOT_FOUND),

    @Schema(description = "매체 코드를 찾을 수 없음")
    MDM_CODE_NOT_FOUND("MDM_CODE_NOT_FOUND", HttpStatus.NOT_FOUND),

    /**
     * 코드가 이미 존재하는 경우 발생하는 예외
     * 발생 상황:
     * - 이미 존재하는 코드를 생성하려 할 때
     */
    @Schema(description = "이미 존재하는 대분류 코드")
    LARGE_CODE_ALREADY_EXISTS("LARGE_CODE_ALREADY_EXISTS", HttpStatus.CONFLICT),

    @Schema(description = "이미 존재하는 중분류 코드")
    AVE_CODE_ALREADY_EXISTS("AVE_CODE_ALREADY_EXISTS", HttpStatus.CONFLICT),

    @Schema(description = "이미 존재하는 소분류 코드")
    SMALL_CODE_ALREADY_EXISTS("SMALL_CODE_ALREADY_EXISTS", HttpStatus.CONFLICT),

    @Schema(description = "이미 존재하는 서비스 코드")
    SVC_CODE_ALREADY_EXISTS("SVC_CODE_ALREADY_EXISTS", HttpStatus.CONFLICT),

    @Schema(description = "이미 존재하는 매체 코드")
    MDM_CODE_ALREADY_EXISTS("MDM_CODE_ALREADY_EXISTS", HttpStatus.CONFLICT),

    /**
     * 계층 구조가 올바르지 않은 경우 발생하는 예외
     * 발생 상황:
     * - 매체코드 계층 구조 무결성 위반 시
     */
    @Schema(description = "유효하지 않은 계층 구조")
    INVALID_HIERARCHY_STRUCTURE("INVALID_HIERARCHY_STRUCTURE", HttpStatus.BAD_REQUEST);

    private final String code;
    private final HttpStatus status;

}
