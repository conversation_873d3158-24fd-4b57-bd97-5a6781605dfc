package kr.or.komca.admin.code.mdm.service.command;

import kr.or.komca.admin.code.mdm.dto.query.response.MdmCode;
import kr.or.komca.admin.code.mdm.mapper.command.MngComisCommandMapper;
import kr.or.komca.common.exception.core.CommonException;
import kr.or.komca.common.exception.response.error.code.CommonErrorCode;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

/**
 * 관리수수료율 Command 서비스 구현체
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class MngComisCommandServiceImpl implements MngComisCommandService {

    private final MngComisCommandMapper mngComisCommandMapper;

    @Override
    @Transactional
    public int upsertMngComisRate(MdmCode mdmCode) {
        String largeClassCd = mdmCode.getLargeClassCd();
        String aveClassCd = mdmCode.getAveClassCd();
        String smallClassCd = mdmCode.getSmallClassCd();
        String mdmCd = mdmCode.getMdmCd();
        BigDecimal mngComisRate = mdmCode.getMngComisRate();
        
        log.info("관리수수료율 UPSERT 시작 - 매체코드: {}, 관리수수료율: {}", mdmCd, mngComisRate);

        // 현재 년월 생성 (TENV_MNG_COMIS의 YYYYMM 형식)
        String applYrmn = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMM"));
        log.info("적용년월: {}", applYrmn);

        // 기존 데이터 존재 여부 확인
        boolean exists = mngComisCommandMapper.existsMngComisRate(largeClassCd, aveClassCd, smallClassCd, mdmCd, applYrmn);

        int affectedRows;
        if (exists) {
            // 기존 데이터가 존재하면 UPDATE
            log.info("기존 관리수수료율 데이터 UPDATE - 매체코드: {}, 적용년월: {}", mdmCd, applYrmn);
            affectedRows = mngComisCommandMapper.updateMngComisRate(largeClassCd, aveClassCd, smallClassCd, mdmCd, applYrmn, mngComisRate);
        } else {
            // 기존 데이터가 없으면 INSERT
            log.info("새로운 관리수수료율 데이터 INSERT - 매체코드: {}, 적용년월: {}", mdmCd, applYrmn);
            affectedRows = mngComisCommandMapper.insertMngComisRate(largeClassCd, aveClassCd, smallClassCd, mdmCd, applYrmn, mngComisRate);
        }

        // 관리수수료율 변경사항 요청 있는경우에만 호출되므로 실제 변경사항이 없다면 오류 발생
        if (affectedRows <= 0) {
            throw new CommonException(
                    CommonErrorCode.INTERNAL_SERVER_ERROR,
                    String.format("관리수수료율 저장에 실패했습니다. mdmCd: %s, applYrmn: %s", mdmCd, applYrmn)
            );
        }

        log.info("관리수수료율 UPSERT 완료 - 매체코드: {}, 적용년월: {}, 영향받은 행: {}", mdmCd, applYrmn, affectedRows);

        return affectedRows;
    }
}