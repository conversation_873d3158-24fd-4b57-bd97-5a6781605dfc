package kr.or.komca.admin.code.mdm.service.query;

import kr.or.komca.admin.code.mdm.dto.query.condition.ServiceCodeExcelCondition;
import kr.or.komca.admin.code.mdm.dto.query.response.ServiceCodeExcel;
import kr.or.komca.common.utils.core.dto.response.PageListResponse;

/**
 * 매체코드 엑셀 관련 서비스 인터페이스
 */
public interface MdmCodeExcelService {

    /**
     * 서비스코드 엑셀 목록 조회
     *
     * @param mdmCd 매체코드
     * @param condition 검색 조건
     * @return 서비스코드 엑셀 리스트
     */
    PageListResponse<ServiceCodeExcel> getServiceCodeExcelList(String mdmCd, ServiceCodeExcelCondition condition);
}
