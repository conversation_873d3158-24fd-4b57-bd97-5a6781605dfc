package kr.or.komca.admin.code.account.api;


import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import kr.or.komca.admin.code.account.dto.command.request.ManageAccountRequest;
import kr.or.komca.admin.code.account.dto.command.response.ManageAccount;
import kr.or.komca.admin.code.account.dto.query.condition.AccountInfoSearchCondition;
import kr.or.komca.admin.code.account.dto.query.condition.AccountSearchCondition;
import kr.or.komca.admin.code.account.dto.query.response.Account;
import kr.or.komca.admin.code.account.dto.query.response.AccountInfo;
import kr.or.komca.common.exception.response.error.CommonErrorResponse;
import kr.or.komca.common.exception.response.success.CommonSuccessResponse;
import kr.or.komca.common.utils.core.dto.response.ListResponse;
import org.springframework.http.ResponseEntity;

@Tag(name = "Account", description = "계정")
public interface AccountApi {

    /**
     * ======== Query API ========
     */

    @Operation(
        summary = "계정 목록 조회",
        description = "검색 조건에 맞는 계정 목록을 조회합니다."
    )
    @ApiResponses({
        @ApiResponse(
            responseCode = "200",
            description = "조회 성공"
        ),
        @ApiResponse(
            responseCode = "400",
            description = "잘못된 요청",
            content = @Content(schema = @Schema(implementation = CommonErrorResponse.class))
        ),
    })
    ResponseEntity<CommonSuccessResponse<ListResponse<Account>>> getAccountList(
        @Parameter(description = "계정 검색 조건", required = true)
        AccountSearchCondition condition
    );

    @Operation(
        summary = "계정 정보 조회",
        description = "검색 조건에 맞는 계정 정보를 조회합니다."
    )
    @ApiResponses({
        @ApiResponse(
            responseCode = "200",
            description = "조회 성공"
        ),
        @ApiResponse(
            responseCode = "400",
            description = "잘못된 요청",
            content = @Content(schema = @Schema(implementation = CommonErrorResponse.class))
        ),
    })
    ResponseEntity<CommonSuccessResponse<ListResponse<AccountInfo>>> getAccountInfoList(
        @Parameter(description = "계정 검색 조건", required = true)
        AccountInfoSearchCondition condition
    );

    /**
     * ======== Command API ========
     */

    @Operation(
        summary = "계정 통합 관리",
        description = "계정를 일괄로 생성/수정/삭제합니다."
    )
    @ApiResponses({
        @ApiResponse(
            responseCode = "200",
            description = "처리 성공"
        ),
        @ApiResponse(
            responseCode = "400",
            description = "잘못된 요청",
            content = @Content(schema = @Schema(implementation = CommonErrorResponse.class))
        ),
    })
    ResponseEntity<CommonSuccessResponse<ManageAccount>> manageAccount(
        @Parameter(description = "계정 통합 관리 요청", required = true)
        ManageAccountRequest request
    );
}
