package kr.or.komca.admin.code.account.enums.errorcode;

import kr.or.komca.common.interfaces.response.code.ErrorCode;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpStatus;

/**
 * 공통 코드에 대한 ErrorEnum 처리
 * 1. 상수명: 대문자 스네이크 케이스로 작성 (예: INVALID_CODE_STATUS)
 * 2. code: 상수명과 동일하게 문자열로 작성
 * 3. status: 적절한 HTTP 상태 코드 설정 (HttpStatus enum 사용)
 */
@Getter
@RequiredArgsConstructor
public enum AccountCodeErrorCode implements ErrorCode {

    /**
     * 에러
     */
    CODE_ALREADY_EXISTS("CODE_ALREADY_EXISTS", HttpStatus.CONFLICT);

    private final String code;
    private final HttpStatus status;
}
