package kr.or.komca.admin.code.mdm.util;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

/**
 * Sort Validator
 */
@Slf4j
@Component
public class MdmLargeCodeSortColumnValidator {

	/**
	 * 허용된 정렬 컬럼 매핑 정보
	 * Key: 프론트엔드 요청 파라미터명
	 * Value: 실제 DB 테이블의 컬럼명
	 */
	private static final Map<String, String> ALLOWED_SORT_COLUMNS_MAP = new HashMap<>() {{
		put("largeClassCd", "large_class_cd");                      // 대분류코드
		put("largeClassNm", "large_class_nm");                      // 대분류코드명
		put("useYn", "use_yn");                                     // 사용여부
		put("sortOrd", "TO_NUMBER(sort_ord)");                     // 정렬순서
	}};

	/**
	 * 정렬 컬럼 유효성 검사 및 매핑
	 */
	public static String validate(String sortColumn) {
		return ALLOWED_SORT_COLUMNS_MAP.get(sortColumn);
	}
}
