package kr.or.komca.admin.code.common.service.query;

import kr.or.komca.admin.code.common.dto.query.condition.DetailCodeSearchCondition;
import kr.or.komca.admin.code.common.dto.query.condition.ParCodeSearchCondition;
import kr.or.komca.admin.code.common.dto.query.response.DetailCode;
import kr.or.komca.admin.code.common.dto.query.response.ParCode;
import kr.or.komca.common.utils.core.dto.response.PageListResponse;

public interface CommonCodeQueryService {

    /**
     * 상위 코드 목록 조회
     * @param condition 검색 조건
     * @return 상위 코드 목록
     */
    PageListResponse<ParCode> getParCodeList(ParCodeSearchCondition condition);

    /**
     * 상위 코드 단일 조회
     *
     * @param parCd 상위 코드
     * @return 상위 코드 정보
     */
    ParCode getParCodeById(String parCd);

    /**
     * 세부 코드 목록 조회
     * @param parCd 상위 코드
     * @param condition 검색 조건
     * @return 세부 코드 목록
     */
    PageListResponse<DetailCode> getDetailCodeList(String parCd, DetailCodeSearchCondition condition);

    /**
     * 하위 코드 조회
     *
     * @param parCd 상위 코드
     * @param cd 하위 코드
     * @return 하위 코드 정보
     */
    DetailCode getDetailCodeById(String parCd, String cd);

    /**
     * 상위 코드 존재 여부 확인
     * @param parCd 상위 코드
     */
    void validateParCodeExists(String parCd);

    /**
     * 상위 코드가 중복되지 않는지 검증
     * @param parCd 상위 코드
     */
    void validateParCodeDuplicate(String parCd);

    /**
     * 세부 코드가 없는지 확인
     * @param parCd 상위 코드
     */
    void validateNoDetailCode(String parCd);

    /**
     * 세부 코드 존재 여부 확인
     * @param parCd 상위 코드
     * @param cd 코드 값
     */
    void validateDetailCodeExists(String parCd, String cd);

    /**
     * 세부 코드가 중복되지 않는지 검증
     * @param parCd 상위 코드
     * @param cd 코드 값
     */
    void validateDetailCodeDuplicate(String parCd, String cd);

    /**
     * 상위 코드 부서 매핑 존재 여부 확인
     * @param parCd 상위 코드
     * @param deptCd 부서 코드
     * @return 존재 여부
     */
    boolean existsParCodeDept(String parCd, String deptCd);

    /**
     * 상위 코드 부서 매핑 중복 검증
     * @param parCd 상위 코드
     * @param deptCd 부서 코드
     */
    void validateParCodeGroupUserDuplicate(String parCd, String deptCd);

}
