package kr.or.komca.admin.code.account.service.command;

import kr.or.komca.admin.code.account.dto.command.request.ManageAccountRequest;
import kr.or.komca.admin.code.account.dto.command.request.sub.CreateAccount;
import kr.or.komca.admin.code.account.dto.command.request.sub.UpdateAccount;
import kr.or.komca.admin.code.account.dto.command.response.ManageAccount;
import kr.or.komca.admin.code.account.mapper.command.AccountCommandMapper;
import kr.or.komca.admin.code.account.service.query.AccountInfoQueryService;
import kr.or.komca.common.exception.core.CommonException;
import kr.or.komca.common.exception.response.error.code.CommonErrorCode;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Slf4j
@RequiredArgsConstructor
@Service
public class AccountCommandServiceImpl implements AccountCommandService {

    private final AccountCommandMapper accountCommandMapper;
    private final AccountInfoQueryService accountInfoQueryService;

    @Override
    @Transactional
    public ManageAccount manageAccount(ManageAccountRequest request, String inspersId) {
        log.info("계정 통합 관리 요청: {}", request);

        // 각 작업 수행 결과
        int createAffectedRows = 0;
        int updateAffectedRows = 0;
        int deleteAffectedRows = 0;

        List<CreateAccount> create = request.getCreate();

        // create 요청이 존재한다면 계정 추가
        if (create != null && !create.isEmpty()) {
            for (CreateAccount createAccount : create) {
                createAffectedRows += createAccount(createAccount, inspersId);
            }
        }

        List<UpdateAccount> update = request.getUpdate();

        // update 요청이 존재한다면 계정 수정
        if (update != null && !update.isEmpty()) {
            for (UpdateAccount updateAccount : update) {
                updateAffectedRows += updateAccount(updateAccount.getAcctCd(), inspersId, updateAccount);
            }
        }

        List<String> delete = request.getDelete();

        // delete 요청이 존재한다면 계정 삭제
        if (delete != null && !delete.isEmpty()) {
            for (String acctCd : delete) {
                deleteAffectedRows += deleteAccount(acctCd);
            }
        }

        return ManageAccount.builder()
            .createAffectedRows(createAffectedRows)
            .updateAffectedRows(updateAffectedRows)
            .deleteAffectedRows(deleteAffectedRows)
            .build();
    }

    // ================== Private Create Methods ==================

    private int createAccount(CreateAccount request, String inspersId) {
        log.info("계정 생성: {}", request);

        String acctCd = request.getAcctCd();

        // 계정 중복 확인
        accountInfoQueryService.validateAccountDuplicate(acctCd);

        int affectedRows = accountCommandMapper.createAccount(acctCd, inspersId, request);

        if (affectedRows <= 0) {
            throw new CommonException(CommonErrorCode.INTERNAL_SERVER_ERROR,
                String.format("계정 생성에 실패했습니다. acctCd: %s", acctCd));
        }

        return affectedRows;
    }

    private int updateAccount(String acctCd, String modpersId, UpdateAccount request) {
        log.info("계정 수정: acctCd={}, request={}", acctCd, request);

        // 존재하는 코드인지 확인
        accountInfoQueryService.validateExistAccount(acctCd);

        int affectedRows = accountCommandMapper.updateAccount(acctCd, modpersId, request);

        if (affectedRows <= 0) {
            throw new CommonException(
                CommonErrorCode.INTERNAL_SERVER_ERROR,
                String.format("계정 수정에 실패했습니다. acctCd: %s", acctCd)
            );
        }

        return affectedRows;
    }

    // ================== Private Delete Methods ==================

    private int deleteAccount(String acctCd) {
        log.info("계정 삭제: acctCd={}", acctCd);

        // 존재하는 코드인지 확인
        accountInfoQueryService.validateExistAccount(acctCd);

        int affectedRows = accountCommandMapper.deleteAccount(acctCd);

        if (affectedRows <= 0) {
            throw new CommonException(
                CommonErrorCode.INTERNAL_SERVER_ERROR,
                String.format("계정 삭제에 실패했습니다. acctCd: %s", acctCd)
            );
        }

        return affectedRows;
    }
}
