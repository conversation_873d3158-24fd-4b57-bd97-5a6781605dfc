package kr.or.komca.admin.code.mdm.mapper.command;

import kr.or.komca.admin.code.mdm.dto.command.request.MdmCodeHistoryRequest;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 매체코드 이력 Command Mapper 인터페이스
 */
@Mapper
public interface MdmCodeHistoryCommandMapper {

    /**
     * 매체코드 이력 저장
     *
     * @param histSeq 이력 시퀀스
     * @param historyData 이력 저장용 데이터
     * @param chgTypeCd 변경 유형 (01: INSERT, 02: UPDATE, 03: DELETE)
     * @param applyId 이력 적용자 ID
     * @return 저장된 행 수
     */
    int createMdmCodeHistory(
            @Param("histSeq") Long histSeq,
            @Param("historyData") MdmCodeHistoryRequest historyData,
            @Param("chgTypeCd") String chgTypeCd,
            @Param("applyId") String applyId
    );

    /**
     * 매체코드 컬럼 이력 저장
     *
     * @param histSeq 매체코드 이력 시퀀스
     * @param columnName 변경된 컬럼명
     * @return 저장된 행 수
     */
    int createMdmCodeColumnHistory(
            @Param("histSeq") Long histSeq,
            @Param("columnName") String columnName
    );

    /**
     * 다음 매체코드 이력 시퀀스 조회
     *
     * @return 매체코드 이력 시퀀스
     */
    Long getNextMdmCodeHistorySequence();
}