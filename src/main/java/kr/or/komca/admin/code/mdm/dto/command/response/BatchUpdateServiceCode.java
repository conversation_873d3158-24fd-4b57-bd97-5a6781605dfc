package kr.or.komca.admin.code.mdm.dto.command.response;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;

/**
 * 서비스코드 일괄 변경 응답 DTO
 */
@Getter
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "서비스코드 일괄 변경 응답")
public class BatchUpdateServiceCode {

    /** 기준 서비스코드 */
    @Schema(description = "기준 서비스코드", example = "AA010101")
    private String baseSvcCd;
    
    /** 기준 서비스코드가 속한 매체코드 */
    @Schema(description = "매체코드", example = "AA0101")
    private String mdmCd;
    
    /** 변경된 서비스코드 개수 */
    @Schema(description = "변경된 서비스코드 개수", example = "5")
    private int affectedRows;
}
