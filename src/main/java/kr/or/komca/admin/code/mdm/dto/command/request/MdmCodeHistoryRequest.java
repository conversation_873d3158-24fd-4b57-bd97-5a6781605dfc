package kr.or.komca.admin.code.mdm.dto.command.request;

import io.swagger.v3.oas.annotations.media.Schema;
import kr.or.komca.admin.code.mdm.dto.command.request.sub.CreateMdmCode;
import kr.or.komca.admin.code.mdm.dto.command.request.sub.UpdateMdmCode;
import kr.or.komca.admin.code.mdm.dto.query.response.MdmCode;
import kr.or.komca.admin.code.mdm.enums.MdmCodeHistoryChangeType;
import lombok.Builder;
import lombok.Getter;
import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 매체코드 이력 저장 요청 DTO
 */
@Getter
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Schema(description = "매체코드 이력 저장 요청 DTO")
public class MdmCodeHistoryRequest {

    /** 대분류코드 */
    @Schema(description = "대분류코드", example = "A")
    private String largeClassCd;

    /** 중분류코드 */
    @Schema(description = "중분류코드", example = "AA")
    private String aveClassCd;

    /** 소분류코드 */
    @Schema(description = "소분류코드", example = "AA01")
    private String smallClassCd;

    /** 매체코드 */
    @Schema(description = "매체코드", example = "AA0103")
    private String mdmCd;

    /** 매체명 */
    @Schema(description = "매체명", example = "KBS")
    private String mdmNm;

    /** 비고 */
    @Schema(description = "비고", example = "비고 예시")
    private String remak;

    /** 정렬순서 */
    @Schema(description = "정렬순서", example = "1")
    private Integer sortOrd;

    /** 사용여부 */
    @Schema(description = "사용여부", example = "Y")
    private String useYn;

    /** 국내계정코드 */
    @Schema(description = "국내계정코드", example = "0845")
    private String domAcctCd;

    /** 국외계정코드 */
    @Schema(description = "국외계정코드", example = "0846")
    private String abrAcctCd;

    /** 국내지부계정코드 */
    @Schema(description = "국내지부계정코드", example = "0845")
    private String domBranAcctCd;

    /** 국외지부계정코드 */
    @Schema(description = "국외지부계정코드", example = "0846")
    private String abrBranAcctCd;

    /** 부가세여부 */
    @Schema(description = "부가세여부", example = "1")
    private String ataxYn;

    /** 관리수수료율 */
    @Schema(description = "관리수수료율", example = "3.5")
    private BigDecimal mngComisRate;

    /** 등록일시 */
    @Schema(description = "등록일시")
    private LocalDateTime insDt;

    /** 등록자ID */
    @Schema(description = "등록자ID", example = "admin")
    private String inspersId;

    /** 수정일시 */
    @Schema(description = "수정일시")
    private LocalDateTime modDt;

    /** 수정자ID */
    @Schema(description = "수정자ID", example = "admin")
    private String modpersId;

    /** 변경구분코드 */
    @Schema(description = "변경구분코드", example = "01")
    private String changeType;

    /** 편집자ID */
    @Schema(description = "편집자ID", example = "admin")
    private String editpersId;

    /**
     * INSERT 이력 데이터 생성
     */
    public static MdmCodeHistoryRequest forInsertWithSavedData(MdmCode savedData, String applyId) {
        return MdmCodeHistoryRequest.builder()
                .largeClassCd(savedData.getLargeClassCd())
                .aveClassCd(savedData.getAveClassCd())
                .smallClassCd(savedData.getSmallClassCd())
                .mdmCd(savedData.getMdmCd())
                .mdmNm(savedData.getMdmNm())
                .remak(savedData.getRemak())
                .sortOrd(savedData.getSortOrd())
                .useYn(savedData.getUseYn())
                .domAcctCd(savedData.getDomAcctCd())
                .abrAcctCd(savedData.getAbrAcctCd())
                .domBranAcctCd(savedData.getDomBranAcctCd())
                .abrBranAcctCd(savedData.getAbrBranAcctCd())
                .ataxYn(savedData.getAtaxYn())
                .insDt(savedData.getInsDt())
                .inspersId(savedData.getInspersId())
                .mngComisRate(savedData.getMngComisRate())
                .changeType(MdmCodeHistoryChangeType.INSERT.getCode())
                .editpersId(applyId)
                .build();
    }

    /**
     * UPDATE 이력 데이터 생성
     */
    public static MdmCodeHistoryRequest forUpdateWithAfterData(MdmCode afterData, String applyId) {
        return MdmCodeHistoryRequest.builder()
                .largeClassCd(afterData.getLargeClassCd())
                .aveClassCd(afterData.getAveClassCd())
                .smallClassCd(afterData.getSmallClassCd())
                .mdmCd(afterData.getMdmCd())
                .mdmNm(afterData.getMdmNm())
                .remak(afterData.getRemak())
                .sortOrd(afterData.getSortOrd())
                .useYn(afterData.getUseYn())
                .domAcctCd(afterData.getDomAcctCd())
                .abrAcctCd(afterData.getAbrAcctCd())
                .domBranAcctCd(afterData.getDomBranAcctCd())
                .abrBranAcctCd(afterData.getAbrBranAcctCd())
                .ataxYn(afterData.getAtaxYn())
                .insDt(afterData.getInsDt())
                .inspersId(afterData.getInspersId())
                .modDt(afterData.getModDt())
                .modpersId(afterData.getModpersId())
                .mngComisRate(afterData.getMngComisRate())
                .changeType(MdmCodeHistoryChangeType.UPDATE.getCode())
                .editpersId(applyId)
                .build();
    }

    /**
     * DELETE 이력 데이터 생성
     */
    public static MdmCodeHistoryRequest forDelete(MdmCode deleteData, String applyId) {
        return MdmCodeHistoryRequest.builder()
                .largeClassCd(deleteData.getLargeClassCd())
                .aveClassCd(deleteData.getAveClassCd())
                .smallClassCd(deleteData.getSmallClassCd())
                .mdmCd(deleteData.getMdmCd())
                .mdmNm(deleteData.getMdmNm())
                .remak(deleteData.getRemak())
                .sortOrd(deleteData.getSortOrd())
                .useYn(deleteData.getUseYn())
                .domAcctCd(deleteData.getDomAcctCd())
                .abrAcctCd(deleteData.getAbrAcctCd())
                .domBranAcctCd(deleteData.getDomBranAcctCd())
                .abrBranAcctCd(deleteData.getAbrBranAcctCd())
                .ataxYn(deleteData.getAtaxYn())
                .mngComisRate(deleteData.getMngComisRate())
                .insDt(deleteData.getInsDt())
                .inspersId(deleteData.getInspersId())
                .modDt(deleteData.getModDt())
                .modpersId(deleteData.getModpersId())
                .changeType(MdmCodeHistoryChangeType.DELETE.getCode())
                .editpersId(applyId)
                .build();
    }
}