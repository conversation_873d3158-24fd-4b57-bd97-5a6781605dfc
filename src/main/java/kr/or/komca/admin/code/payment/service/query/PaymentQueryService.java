package kr.or.komca.admin.code.payment.service.query;

import kr.or.komca.admin.code.payment.dto.query.condition.PaymentSearchCondition;
import kr.or.komca.admin.code.payment.dto.query.response.Payment;
import kr.or.komca.common.utils.core.dto.response.PageListResponse;

public interface PaymentQueryService {

    /**
     * 지급공제 목록 조회
     * @param condition 검색 조건
     * @return 지급공제 목록
     */
    PageListResponse<Payment> getPaymentList(PaymentSearchCondition condition);

    /**
     * 지급공제 존재 여부 확인
     * @param parCd 지급공제
     */
    void validateExistPayment(String parCd);

    /**
     * 지급공제가 중복되지 않는지 검증
     * @param parCd 지급공제
     */
    void validatePaymentDuplicate(String parCd);
}
