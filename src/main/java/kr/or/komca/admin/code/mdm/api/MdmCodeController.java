package kr.or.komca.admin.code.mdm.api;

import jakarta.validation.Valid;
import kr.or.komca.admin.code.mdm.dto.command.request.*;
import kr.or.komca.admin.code.mdm.dto.command.response.*;
import kr.or.komca.admin.code.mdm.dto.query.condition.*;
import kr.or.komca.admin.code.mdm.dto.query.response.*;
import kr.or.komca.admin.code.mdm.service.command.MdmCodeCommandService;
import kr.or.komca.admin.code.mdm.service.query.MdmCodeExcelService;
import kr.or.komca.admin.code.mdm.service.query.MdmCodeHistoryQueryService;
import kr.or.komca.admin.code.mdm.service.query.MdmCodeQueryService;
import kr.or.komca.common.auth.support.utils.context.UserContextHolder;
import kr.or.komca.common.exception.response.success.CommonSuccessResponse;
import kr.or.komca.common.utils.core.dto.response.PageListResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

/**
 * 매체 코드 관리 REST API Controller
 */
@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("/api/v1/code/mdm")
public class MdmCodeController implements MdmCodeApi {

	private final MdmCodeQueryService mdmCodeQueryService;
	private final MdmCodeCommandService mdmCodeCommandService;
	private final MdmCodeExcelService mdmCodeExcelService;
	private final MdmCodeHistoryQueryService mdmCodeHistoryQueryService;

	/** ======== Query API ======== */

	@Override
	@GetMapping("/large")
	public ResponseEntity<CommonSuccessResponse<PageListResponse<MdmLargeCode>>> getLargeCodes(
			@ModelAttribute @Valid MdmLargeCodeSearchCondition condition
	) {
		log.info("대분류 매체코드 목록 조회 요청: {}", condition);
		
		PageListResponse<MdmLargeCode> result = mdmCodeQueryService.getLargeMdmCodeList(condition);
		return CommonSuccessResponse.ok(result);
	}

	@Override
	@GetMapping("/ave/{largeClassCd}")
	public ResponseEntity<CommonSuccessResponse<PageListResponse<MdmAveCode>>> getAveCodes(
			@PathVariable String largeClassCd,
			@ModelAttribute @Valid MdmAveCodeSearchCondition condition
	) {
		log.info("중분류 매체코드 목록 조회 요청 - largeClassCd: {}, condition: {}", largeClassCd, condition);
		
		PageListResponse<MdmAveCode> result = mdmCodeQueryService.getAveMdmCodeList(condition, largeClassCd);
		return CommonSuccessResponse.ok(result);
	}

	@Override
	@GetMapping("/small/{aveClassCd}")
	public ResponseEntity<CommonSuccessResponse<PageListResponse<MdmSmallCode>>> getSmallCodes(
			@PathVariable String aveClassCd,
			@ModelAttribute @Valid MdmSmallCodeSearchCondition condition
	) {
		log.info("소분류 코드 조회 요청 - aveClassCd: {}, condition: {}", aveClassCd, condition);
		return CommonSuccessResponse.ok(mdmCodeQueryService.getSmallMdmCodeList(condition, aveClassCd));
	}

	@Override
	@GetMapping("/mdm/{smallClassCd}")
	public ResponseEntity<CommonSuccessResponse<PageListResponse<MdmCode>>> getMdmCodes(
			@PathVariable String smallClassCd,
			@ModelAttribute @Valid MdmCodeSearchCondition condition
	) {
		log.info("매체 코드 조회 요청 - smallClassCd: {}, condition: {}", smallClassCd, condition);
		return CommonSuccessResponse.ok(mdmCodeQueryService.getMdmCodeList(condition, smallClassCd));
	}

	@Override
	@GetMapping("/svc/{mdmCd}")
	public ResponseEntity<CommonSuccessResponse<PageListResponse<ServiceCode>>> getServiceCodes(
			@PathVariable String mdmCd,
			@ModelAttribute @Valid ServiceCodeSearchCondition condition
	) {
		log.info("서비스 코드 조회 요청 - mdmCd: {}, condition: {}", mdmCd, condition);
		return CommonSuccessResponse.ok(mdmCodeQueryService.getServiceCodeList(condition, mdmCd));
	}

	@Override
	@GetMapping("/svc/{mdmCd}/excel")
	public ResponseEntity<CommonSuccessResponse<PageListResponse<ServiceCodeExcel>>> getServiceCodeExcelList(
			@PathVariable String mdmCd,
			@ModelAttribute @Valid ServiceCodeExcelCondition condition
	) {
		log.info("서비스 코드 엑셀 조회 요청 - mdmCd: {}, condition: {}", mdmCd, condition);
		return CommonSuccessResponse.ok(mdmCodeExcelService.getServiceCodeExcelList(mdmCd, condition));
	}

	/** ======== 이력관리 API ======== */

	@Override
	@GetMapping("/history")
	public ResponseEntity<CommonSuccessResponse<PageListResponse<MdmCodeHistory>>> getMdmCodeHistoryList(
			@ModelAttribute @Valid MdmCodeHistorySearchCondition condition
	) {
		log.info("매체코드 이력 목록 조회 요청 - condition: {}", condition);

		PageListResponse<MdmCodeHistory> result = mdmCodeHistoryQueryService.getMdmCodeHistoryList(condition);
		return CommonSuccessResponse.ok(result);
	}


	/** ======== Command API ======== */

	@Override
	@PostMapping("/large")
	public ResponseEntity<CommonSuccessResponse<ManageLargeCode>> manageLargeCodes(
			@Valid @RequestBody ManageLargeCodeRequest request
	) {
		log.info("대분류 코드 통합 관리 요청: {}", request);

		String editpersId = UserContextHolder.getContext().getUserId();

		ManageLargeCode result = mdmCodeCommandService.manageLargeCode(request, editpersId);
		return CommonSuccessResponse.ok(result);
	}

	@Override
	@PostMapping("/ave")
	public ResponseEntity<CommonSuccessResponse<ManageAveCode>> manageAveCodes(
			@Valid @RequestBody ManageAveCodeRequest request
	) {
		log.info("중분류 코드 통합 관리 요청: {}", request);

		String editpersId = UserContextHolder.getContext().getUserId();

		ManageAveCode result = mdmCodeCommandService.manageAveCode(request, editpersId);
		return CommonSuccessResponse.ok(result);
	}

	@Override
	@PostMapping("/small")
	public ResponseEntity<CommonSuccessResponse<ManageSmallCode>> manageSmallCodes(
			@Valid @RequestBody ManageSmallCodeRequest request
	) {
		log.info("소분류 코드 통합 관리 요청: {}", request);

		String editpersId = UserContextHolder.getContext().getUserId();

		ManageSmallCode result = mdmCodeCommandService.manageSmallCode(request, editpersId);
		return CommonSuccessResponse.ok(result);
	}

	@Override
	@PostMapping("/mdm")
	public ResponseEntity<CommonSuccessResponse<ManageMdmCode>> manageMdmCodes(
			@Valid @RequestBody ManageMdmCodeRequest request
	) {
		log.info("매체 코드 통합 관리 요청: {}", request);

		String editpersId = UserContextHolder.getContext().getUserId();

		ManageMdmCode result = mdmCodeCommandService.manageMdmCode(request, editpersId);
		return CommonSuccessResponse.ok(result);
	}

	@Override
	@PostMapping("/svc")
	public ResponseEntity<CommonSuccessResponse<ManageServiceCode>> manageServiceCodes(
			@Valid @RequestBody ManageServiceCodeRequest request
	) {
		log.info("서비스 코드 통합 관리 요청: {}", request);

		String editpersId = UserContextHolder.getContext().getUserId();

		ManageServiceCode result = mdmCodeCommandService.manageServiceCode(request, editpersId);
		return CommonSuccessResponse.ok(result);
	}

	@Override
	@PostMapping("/svc/{baseSvcCd}/sync")
	public ResponseEntity<CommonSuccessResponse<BatchUpdateServiceCode>> syncServiceCodes(
			@PathVariable String baseSvcCd
	) {
		log.info("서비스 코드 일괄 동기화 요청 - baseSvcCd: {}", baseSvcCd);

		String editpersId = UserContextHolder.getContext().getUserId();

		BatchUpdateServiceCode result = mdmCodeCommandService.batchUpdateServiceCode(baseSvcCd, editpersId);
		return CommonSuccessResponse.ok(result);
	}
}
