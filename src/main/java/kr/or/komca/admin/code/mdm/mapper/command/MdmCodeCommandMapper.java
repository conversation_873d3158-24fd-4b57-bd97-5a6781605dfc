package kr.or.komca.admin.code.mdm.mapper.command;

import kr.or.komca.admin.code.mdm.dto.command.request.sub.*;
import kr.or.komca.admin.code.mdm.dto.query.response.MdmCode;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 매체코드 Command Mapper 인터페이스
 */
@Mapper
public interface MdmCodeCommandMapper {

    /**
     * 대분류 코드 생성
     *
     * @param command 생성 요청
     * @return 생성된 행 수
     */
    int createLargeCode(
            @Param("command") CreateLargeCode command
    );

    /**
     * 대분류 코드 수정
     *
     * @param command 수정 요청
     * @return 수정된 행 수
     */
    int updateLargeCode(
            @Param("command") UpdateLargeCode command
    );

    /**
     * 대분류 코드 삭제 (논리적 삭제)
     *
     * @param largeClassCd 대분류 코드
     * @param delpersId 삭제자 ID
     * @return 삭제된 행 수
     */
    int deleteLargeCode(
            @Param("largeClassCd") String largeClassCd,
            @Param("delpersId") String delpersId
    );

    /**
     * 중분류 코드 생성
     *
     * @param largeClassCd 대분류 코드 (공통)
     * @param command 생성 요청
     * @return 생성된 행 수
     */
    int createAveCode(
            @Param("largeClassCd") String largeClassCd,
            @Param("command") CreateAveCode command
    );

    /**
     * 중분류 코드 수정
     *
     * @param aveClassCd 중분류 코드
     * @param command 수정 요청
     * @return 수정된 행 수
     */
    int updateAveCode(
            @Param("aveClassCd") String aveClassCd,
            @Param("command") UpdateAveCode command
    );

    /**
     * 중분류 코드 삭제 (논리적 삭제)
     *
     * @param aveClassCd 중분류 코드
     * @param delpersId 삭제자 ID
     * @return 삭제된 행 수
     */
    int deleteAveCode(
            @Param("aveClassCd") String aveClassCd,
            @Param("delpersId") String delpersId
    );

    /**
     * 소분류 코드 생성
     *
     * @param largeClassCd 대분류 코드 (공통)
     * @param aveClassCd 중분류 코드 (공통)
     * @param command 생성 요청
     * @return 생성된 행 수
     */
    int createSmallCode(
            @Param("largeClassCd") String largeClassCd,
            @Param("aveClassCd") String aveClassCd,
            @Param("command") CreateSmallCode command
    );

    /**
     * 소분류 코드 수정
     *
     * @param smallClassCd 소분류 코드
     * @param command 수정 요청
     * @return 수정된 행 수
     */
    int updateSmallCode(
            @Param("smallClassCd") String smallClassCd,
            @Param("command") UpdateSmallCode command
    );

    /**
     * 소분류 코드 삭제 (논리적 삭제)
     *
     * @param smallClassCd 소분류 코드
     * @param delpersId 삭제자 ID
     * @return 삭제된 행 수
     */
    int deleteSmallCode(
            @Param("smallClassCd") String smallClassCd,
            @Param("delpersId") String delpersId
    );

    /**
     * 매체 코드 생성
     *
     * @param largeClassCd 대분류 코드 (공통)
     * @param aveClassCd 중분류 코드 (공통)  
     * @param smallClassCd 소분류 코드 (공통)
     * @param inspersId 생성자
     * @param command 생성 요청
     * @return 생성된 행 수
     */
    int createMdmCode(
            @Param("largeClassCd") String largeClassCd,
            @Param("aveClassCd") String aveClassCd,
            @Param("smallClassCd") String smallClassCd,
            @Param("inspersId") String inspersId,
            @Param("command") CreateMdmCode command
    );

    /**
     * 매체 코드 수정
     *
     * @param mdmCd 매체 코드
     * @param command 수정 요청
     * @return 수정된 행 수
     */
    int updateMdmCode(
            @Param("mdmCd") String mdmCd,
            @Param("modpersId") String modpersId,
            @Param("command") UpdateMdmCode command
    );

    /**
     * 매체 코드 삭제 (논리적 삭제)
     *
     * @param mdmCd 매체 코드
     * @param delpersId 삭제자 ID
     * @return 삭제된 행 수
     */
    int deleteMdmCode(
            @Param("mdmCd") String mdmCd,
            @Param("delpersId") String delpersId
    );

    /**
     * 서비스 코드 생성
     *
     * @param largeClassCd 대분류 코드 (공통)
     * @param aveClassCd 중분류 코드 (공통)
     * @param smallClassCd 소분류 코드 (공통)
     * @param mdmCd 매체 코드 (공통)
     * @param command 생성 요청
     * @param inspersId 등록자 ID
     * @return 생성된 행 수
     */
    int createServiceCode(
            @Param("largeClassCd") String largeClassCd,
            @Param("aveClassCd") String aveClassCd,
            @Param("smallClassCd") String smallClassCd,
            @Param("mdmCd") String mdmCd,
            @Param("command") CreateServiceCode command,
            @Param("inspersId") String inspersId
    );

    /**
     * 서비스 코드 수정
     *
     * @param svcCd 서비스 코드
     * @param command 수정 요청
     * @param modpersId 수정자 ID
     * @return 수정된 행 수
     */
    int updateServiceCode(
            @Param("svcCd") String svcCd,
            @Param("command") UpdateServiceCode command,
            @Param("modpersId") String modpersId
    );

    /**
     * 서비스 코드 삭제 (논리적 삭제)
     *
     * @param svcCd 서비스 코드
     * @param delpersId 삭제자 ID
     * @return 삭제된 행 수
     */
    int deleteServiceCode(
            @Param("svcCd") String svcCd,
            @Param("delpersId") String delpersId
    );
    
    /**
     * 서비스코드 일괄 업데이트
     * 특정 MDM 코드(매체코드)에 속한 서비스코드들의 속성을 기준 서비스코드의 속성으로 일괄 업데이트
     * 
     * @param mdmCd 매체코드
     * @param baseSvcCd 기준 서비스코드
     * @param modpersId 수정자 ID
     * @return 업데이트된 레코드 수
     */
    int batchUpdateServiceCodeByMdmCd(
            @Param("mdmCd") String mdmCd,
            @Param("baseSvcCd") String baseSvcCd,
            @Param("modpersId") String modpersId
    );


}
