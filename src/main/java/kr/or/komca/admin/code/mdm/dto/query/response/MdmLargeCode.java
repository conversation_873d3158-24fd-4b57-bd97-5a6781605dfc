package kr.or.komca.admin.code.mdm.dto.query.response;

import io.swagger.v3.oas.annotations.media.Schema; 
import jakarta.validation.constraints.*; 
import lombok.Builder; 
import lombok.Getter; 
import lombok.AllArgsConstructor; 
import lombok.NoArgsConstructor;

/**
* 매체코드(대분류) 조회에 대한 응답 DTO
*/
@Getter 
@Builder 
@AllArgsConstructor 
@NoArgsConstructor 
@Schema(description = "매체코드(대분류) 조회에 대한 응답 DTO")
public class MdmLargeCode {

    /** 대분류코드 */
    @Schema(description = "대분류코드", example = "A")
    private String largeClassCd;

    /** 대분류코드명 */
    @Schema(description = "대분류코드명", example = "AA")
    private String largeClassNm;

    /** 사용여부 */
    @Schema(description = "사용여부", example = "Y")
    private String useYn;

    /** 사용여부 */
    @Schema(description = "정렬순서", example = "1")
    private int sortOrd;
}
