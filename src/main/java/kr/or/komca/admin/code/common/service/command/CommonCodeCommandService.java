package kr.or.komca.admin.code.common.service.command;

import kr.or.komca.admin.code.common.dto.command.request.ManageDetailCodeRequest;
import kr.or.komca.admin.code.common.dto.command.request.ManageParCodeRequest;
import kr.or.komca.admin.code.common.dto.command.response.ManageDetailCode;
import kr.or.komca.admin.code.common.dto.command.response.ManageParCode;

public interface CommonCodeCommandService {

    /**
     * 상위 코드 통합 관리
     *
     * @param request 통합 관리 요청
     * @param editperId 수정자 ID
     * @return 관리 결과
     */
    ManageParCode manageParCode(ManageParCodeRequest request, String editperId);

    /**
     * 세부 코드 통합 관리
     *
     * @param parCd 상위 코드
     * @param request 통합 관리 요청
     * @param editpersId 수정자 ID
     * @return 관리 결과
     */
    ManageDetailCode manageDetailCode(String parCd, ManageDetailCodeRequest request, String editpersId);
}
