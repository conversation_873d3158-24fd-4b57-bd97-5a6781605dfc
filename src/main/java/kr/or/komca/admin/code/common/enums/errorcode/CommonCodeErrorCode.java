package kr.or.komca.admin.code.common.enums.errorcode;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpStatus;
import kr.or.komca.common.interfaces.response.code.ErrorCode;

/**
 * 공통 코드에 대한 ErrorEnum 처리
 * 1. 상수명: 대문자 스네이크 케이스로 작성 (예: INVALID_CODE_STATUS)
 * 2. code: 상수명과 동일하게 문자열로 작성
 * 3. status: 적절한 HTTP 상태 코드 설정 (HttpStatus enum 사용)
 */
@Getter
@RequiredArgsConstructor
public enum CommonCodeErrorCode implements ErrorCode {

    /**
     * 상위 코드 관련 에러
     * 발생 상황:
     * - 존재하지 않는 상위 코드에 접근할 때
     * - 이미 존재하는 상위 코드를 생성하려 할 때
     * - 세부 코드가 있는 상위 코드를 삭제하려 할 때
     * - 이미 존재하는 공통코드-부서코드를 매핑하려 할 때
     */
    @Schema(description = "상위 코드를 찾을 수 없음")
    PAR_CODE_NOT_FOUND("PAR_CODE_NOT_FOUND", HttpStatus.NOT_FOUND),

    @Schema(description = "이미 존재하는 상위 코드")
    PAR_CODE_ALREADY_EXISTS("PAR_CODE_ALREADY_EXISTS", HttpStatus.CONFLICT),

    @Schema(description = "세부 코드가 있는 상위 코드는 삭제할 수 없음")
    CANNOT_DELETE_PAR_CODE_WITH_DETAIL("CANNOT_DELETE_PAR_CODE_WITH_DETAIL", HttpStatus.BAD_REQUEST),

    @Schema(description = "이미 존재하는 공통코드-부서코드 매핑")
    PAR_CODE_DEPT_ALREADY_EXISTS("PAR_CODE_DEPT_ALREADY_EXISTS", HttpStatus.CONFLICT),

    /**
     * 세부 코드 관련 에러
     * 발생 상황:
     * - 존재하지 않는 세부 코드에 접근할 때
     * - 이미 존재하는 세부 코드를 생성하려 할 때
     */
    @Schema(description = "세부 코드를 찾을 수 없음")
    DETAIL_CODE_NOT_FOUND("DETAIL_CODE_NOT_FOUND", HttpStatus.NOT_FOUND),

    @Schema(description = "이미 존재하는 세부 코드")
    DETAIL_CODE_ALREADY_EXISTS("DETAIL_CODE_ALREADY_EXISTS", HttpStatus.CONFLICT);


    private final String code;
    private final HttpStatus status;
}
