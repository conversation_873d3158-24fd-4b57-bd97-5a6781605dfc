package kr.or.komca.admin.code.common.dto.command.request.sub;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Pattern;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.ToString;

/**
 * 세부 코드 수정 요청
 */
@Getter
@Builder
@AllArgsConstructor
@ToString
@Schema(description = "세부 코드 수정 요청")
public class UpdateDetailCode {

    /** 코드 값 */
    @Schema(description = "코드 값", example = "01")
    @NotBlank
    @Size(max = 10)
    private String cd;

    /** 코드 이름 */
    @Schema(description = "코드 이름", example = "정회원")
    @NotBlank
    @Size(max = 50)
    private String cdNm;

    /** 추가 정보 */
    @Schema(description = "추가 정보", example = "01")
    @Size(max = 100)
    private String cdEtc;

    /** 비고 */
    @Schema(description = "비고", example = "정회원 코드")
    @Size(max = 4000)
    private String remak;

    /** 정렬 순서 */
    @Schema(description = "정렬 순서", example = "1")
    private String sortOrd;

    /** 사용 여부 */
    @Schema(description = "사용 여부", example = "Y")
    @NotBlank
    @Pattern(regexp = "^[YN]$")
    private String useYn;
}
