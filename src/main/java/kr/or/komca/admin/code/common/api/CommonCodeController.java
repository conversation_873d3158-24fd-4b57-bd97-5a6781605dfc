package kr.or.komca.admin.code.common.api;

import jakarta.validation.Valid;
import kr.or.komca.admin.code.common.dto.command.request.ManageDetailCodeRequest;
import kr.or.komca.admin.code.common.dto.command.request.ManageParCodeRequest;
import kr.or.komca.admin.code.common.dto.command.response.ManageDetailCode;
import kr.or.komca.admin.code.common.dto.command.response.ManageParCode;
import kr.or.komca.admin.code.common.dto.query.condition.DetailCodeExcelCondition;
import kr.or.komca.admin.code.common.dto.query.condition.DetailCodeSearchCondition;
import kr.or.komca.admin.code.common.dto.query.condition.ParCodeExcelCondition;
import kr.or.komca.admin.code.common.dto.query.condition.ParCodeSearchCondition;
import kr.or.komca.admin.code.common.dto.query.response.DetailCode;
import kr.or.komca.admin.code.common.dto.query.response.DetailCodeExcel;
import kr.or.komca.admin.code.common.dto.query.response.ParCode;
import kr.or.komca.admin.code.common.dto.query.response.ParCodeExcel;
import kr.or.komca.admin.code.common.service.command.CommonCodeCommandService;
import kr.or.komca.admin.code.common.service.query.CommonCodeExcelService;
import kr.or.komca.admin.code.common.service.query.CommonCodeQueryService;
import kr.or.komca.common.auth.domain.common.KomcaStaff;
import kr.or.komca.common.auth.domain.common.UserContext;
import kr.or.komca.common.auth.support.utils.context.UserContextHolder;
import kr.or.komca.common.exception.response.success.CommonSuccessResponse;
import kr.or.komca.common.utils.core.dto.response.PageListResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

@Slf4j
@RestController
@RequestMapping("/api/v1/code/common")
@RequiredArgsConstructor
public class CommonCodeController implements CommonCodeApi {

    private final CommonCodeExcelService commonCodeExcelService;
    private final CommonCodeCommandService commonCodeCommandService;
    private final CommonCodeQueryService commonCodeQueryService;

    /** ======== Query API ======== */

    @GetMapping("/par-code")
    public ResponseEntity<CommonSuccessResponse<PageListResponse<ParCode>>> getParCodeList(
            @ModelAttribute @Valid ParCodeSearchCondition condition
    ) {
        log.info("상위 코드 목록 조회 요청: {}", condition);

        PageListResponse<ParCode> response = commonCodeQueryService.getParCodeList(condition);
        return CommonSuccessResponse.ok(response);
    }

    @GetMapping("/par-code/{parCd}/detail-code")
    public ResponseEntity<CommonSuccessResponse<PageListResponse<DetailCode>>> getDetailCodeList(
            @PathVariable String parCd,
            @ModelAttribute @Valid DetailCodeSearchCondition condition
    ) {
        log.info("세부 코드 목록 조회 요청 - parCd: {}, condition: {}", parCd, condition);
        
        PageListResponse<DetailCode> response = commonCodeQueryService.getDetailCodeList(parCd, condition);
        return CommonSuccessResponse.ok(response);
    }

    @Override
    @GetMapping("/par-code/excel")
    public ResponseEntity<CommonSuccessResponse<PageListResponse<ParCodeExcel>>> getParCodeExcelList(
            @ModelAttribute @Valid ParCodeExcelCondition condition
    ) {
        log.info("상위 코드 엑셀 데이터 조회 요청: {}", condition);
        
        PageListResponse<ParCodeExcel> response = commonCodeExcelService.getParCodeExcelList(condition);
        return CommonSuccessResponse.ok(response);
    }

    @Override
    @GetMapping("/par-code/{parCd}/detail-code/excel")
    public ResponseEntity<CommonSuccessResponse<PageListResponse<DetailCodeExcel>>> getDetailCodeExcelList(
            @PathVariable String parCd,
            @ModelAttribute @Valid DetailCodeExcelCondition condition
    ) {
        log.info("세부 코드 엑셀 데이터 조회 요청 - parCd: {}, condition: {}", parCd, condition);
        
        PageListResponse<DetailCodeExcel> response = commonCodeExcelService.getDetailCodeExcelList(parCd, condition);
        return CommonSuccessResponse.ok(response);
    }

    /** ======== Command API ======== */

    @Override
    @PostMapping("/par-code")
    public ResponseEntity<CommonSuccessResponse<ManageParCode>> manageParCode(
            @Valid @RequestBody ManageParCodeRequest request
    ) {
        String inspersId = UserContextHolder.getContext().getUserId();
        log.info("상위 코드 통합 관리 요청 - inspersId: {}", inspersId);

        ManageParCode result = commonCodeCommandService.manageParCode(request, inspersId);
        return CommonSuccessResponse.ok(result);
    }

    @Override
    @PostMapping("/par-code/{parCd}/detail-code")
    public ResponseEntity<CommonSuccessResponse<ManageDetailCode>> manageDetailCode(
            @PathVariable String parCd,
            @Valid @RequestBody ManageDetailCodeRequest request
    ) {
        String inspersId = UserContextHolder.getContext().getUserId();
        log.info("세부 코드 통합 관리 요청 - parCd: {}, inspersId: {}", parCd, inspersId);

        ManageDetailCode result = commonCodeCommandService.manageDetailCode(parCd, request, inspersId);
        return CommonSuccessResponse.ok(result);
    }
}
