package kr.or.komca.admin.code.mdm.dto.query.response;

import io.swagger.v3.oas.annotations.media.Schema; 
import lombok.Builder; 
import lombok.Getter; 
import lombok.AllArgsConstructor; 
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
* 매체코드(매체) 조회에 대한 응답 DTO
*/
@Getter 
@Builder 
@AllArgsConstructor 
@NoArgsConstructor 
@Schema(description = "매체코드(매체) 조회에 대한 응답 DTO")
public class MdmCode {

    /** 대분류코드 */
    @Schema(description = "대분류코드", example = "A")
    private String largeClassCd;

    /** 대분류명 */
    @Schema(description = "대분류명", example = "음반")
    private String largeClassNm;

    /** 중분류코드 */
    @Schema(description = "중분류코드", example = "AA")
    private String aveClassCd;

    /** 중분류명 */
    @Schema(description = "중분류명", example = "국내음반")
    private String aveClassNm;

    /** 소분류코드 */
    @Schema(description = "소분류코드", example = "AA01")
    private String smallClassCd;

    /** 소분류명 */
    @Schema(description = "소분류명", example = "일반음반")
    private String smallClassNm;

    /** 매체코드 */
    @Schema(description = "매체코드", example = "AA0103")
    private String mdmCd;

    /** 매체명 */
    @Schema(description = "매체명", example = "KBS")
    private String mdmNm;

    /** 비고 */
    @Schema(description = "비고", example = "비고 예시")
    private String remak;

    /** 정렬순서 */
    @Schema(description = "정렬순서", example = "1")
    private Integer sortOrd;

    /** 사용여부 */
    @Schema(description = "사용여부", example = "Y")
    private String useYn;

    /** 관리수수료율 */
    @Schema(description = "관리수수료율", example = "3.5")
    private BigDecimal mngComisRate;

    /** 국내계정코드 */
    @Schema(description = "국내계정코드", example = "0845")
    private String domAcctCd;

    /** 국외계정코드 */
    @Schema(description = "국외계정코드", example = "0846")
    private String abrAcctCd;

    /** 국내지부계정코드 */
    @Schema(description = "국내지부계정코드", example = "0845")
    private String domBranAcctCd;

    /** 국외지부계정코드 */
    @Schema(description = "국외지부계정코드", example = "0846")
    private String abrBranAcctCd;

    /** 부가세여부 */
    @Schema(description = "부가세여부", example = "1")
    private String ataxYn;

    /** 등록일시 */
    @Schema(description = "등록일시")
    private LocalDateTime insDt;

    /** 등록자ID */
    @Schema(description = "등록자ID", example = "admin")
    private String inspersId;

    /** 수정일시 */
    @Schema(description = "수정일시")
    private LocalDateTime modDt;

    /** 수정자ID */
    @Schema(description = "수정자ID", example = "admin")
    private String modpersId;
}