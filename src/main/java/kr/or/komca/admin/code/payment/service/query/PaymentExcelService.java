package kr.or.komca.admin.code.payment.service.query;

import kr.or.komca.admin.code.payment.dto.query.condition.PaymentExcelCondition;
import kr.or.komca.admin.code.payment.dto.query.response.PaymentExcel;
import kr.or.komca.common.utils.core.dto.response.PageListResponse;

public interface PaymentExcelService {

    /**
     * 지급공제 목록 엑셀 조회
     *
     * @param condition 검색 조건
     * @return 지급공제 리스트
     */
    PageListResponse<PaymentExcel> getPaymentExcelList(PaymentExcelCondition condition);
}
