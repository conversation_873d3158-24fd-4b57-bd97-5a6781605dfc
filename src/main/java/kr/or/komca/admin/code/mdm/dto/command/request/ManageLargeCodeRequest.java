package kr.or.komca.admin.code.mdm.dto.command.request;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.Valid;
import kr.or.komca.admin.code.mdm.dto.command.request.sub.CreateLargeCode;
import kr.or.komca.admin.code.mdm.dto.command.request.sub.UpdateLargeCode;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.ToString;

import java.util.List;


/**
 * 대분류 코드 통합 업데이트 요청
 */
@Getter
@Builder
@AllArgsConstructor
@ToString
@Schema(description = "대분류 코드 통합 업데이트 요청 (C/U/D)")
public class ManageLargeCodeRequest {

	@Schema(description = "생성 목록")
	private List<@Valid CreateLargeCode> create;

	@Schema(description = "수정 목록")
	private List<@Valid UpdateLargeCode> update;

	@Schema(description = "삭제 목록")
	private List<String> delete;
}
