package kr.or.komca.admin.code.payment.api;

import jakarta.validation.Valid;
import kr.or.komca.admin.code.payment.dto.command.request.ManagePaymentRequest;
import kr.or.komca.admin.code.payment.dto.command.response.ManagePayment;
import kr.or.komca.admin.code.payment.dto.query.condition.PaymentExcelCondition;
import kr.or.komca.admin.code.payment.dto.query.condition.PaymentSearchCondition;
import kr.or.komca.admin.code.payment.dto.query.response.Payment;
import kr.or.komca.admin.code.payment.dto.query.response.PaymentExcel;
import kr.or.komca.admin.code.payment.service.command.PaymentCommandService;
import kr.or.komca.admin.code.payment.service.query.PaymentExcelService;
import kr.or.komca.admin.code.payment.service.query.PaymentQueryService;
import kr.or.komca.common.auth.support.utils.context.UserContextHolder;
import kr.or.komca.common.exception.response.success.CommonSuccessResponse;
import kr.or.komca.common.utils.core.dto.response.PageListResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

@Slf4j
@RestController
@RequestMapping("/api/v1/code/payment")
@RequiredArgsConstructor
public class PaymentController implements PaymentApi {

    private final PaymentExcelService paymentExcelService;
    private final PaymentCommandService paymentCommandService;
    private final PaymentQueryService paymentQueryService;

    /**
     * ======== Query API ========
     */

    @GetMapping
    public ResponseEntity<CommonSuccessResponse<PageListResponse<Payment>>> getPaymentList(
        @ModelAttribute @Valid PaymentSearchCondition condition
    ) {
        log.info("지급공제 목록 조회 요청: {}", condition);
        PageListResponse<Payment> response = paymentQueryService.getPaymentList(condition);
        return CommonSuccessResponse.ok(response);
    }

    @Override
    @GetMapping("/excel")
    public ResponseEntity<CommonSuccessResponse<PageListResponse<PaymentExcel>>> getPaymentExcelList(
        @ModelAttribute @Valid PaymentExcelCondition condition
    ) {
        PageListResponse<PaymentExcel> response = paymentExcelService.getPaymentExcelList(condition);
        return CommonSuccessResponse.ok(response);
    }

    /**
     * ======== Command API ========
     */

    @PostMapping
    public ResponseEntity<CommonSuccessResponse<ManagePayment>> managePayment(
        @Valid @RequestBody ManagePaymentRequest request
    ) {
        log.info("지급공제 통합 관리 요청: {}", request);

        // 임시 사용자 ID (인증 구현 후 실제 로그인 사용자로 변경 필요)
        String inspersId = UserContextHolder.getContext().getUserId();

        ManagePayment result = paymentCommandService.managePayment(request, inspersId);
        return CommonSuccessResponse.ok(result);
    }
}
