package kr.or.komca.admin.code.mdm.dto.query.condition;

import io.swagger.v3.oas.annotations.media.Schema;
import kr.or.komca.admin.code.mdm.util.MdmLargeCodeSortColumnValidator;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.ToString;

/**
 * 매체코드 대분류 조회 조건 DTO
 */
@ToString
@Getter
@AllArgsConstructor
@Builder(toBuilder = true)
@Schema(description = "매체코드 대분류 조회 조건")
public class MdmLargeCodeSearchCondition {

    /** 페이지 번호 */
    @Schema(description = "페이지 번호", example = "1")
    private int page;

    /** 페이지 크기 */
    @Schema(description = "페이지 크기", example = "10")
    private int pageSize;

    /** 정렬할 컬럼 **/
    @Schema(description = "정렬할 컬럼", example = "largeClassCd")
    private String sortColumn;

    /** 정렬 순서 1: asc. 2: desc  */
    @Schema(description = "정렬 순서 (1: 오름차순, 2: 내림차순)", example = "1", allowableValues = {"1", "2"})
    private String sortOrder;

    public String getSortColumn() {
        return MdmLargeCodeSortColumnValidator.validate(this.sortColumn);
    }

    public String getSortOrder() {
        if ("1".equals(this.sortOrder)) return "ASC";
        if ("2".equals(this.sortOrder)) return "DESC";
        return "";
    }
}
