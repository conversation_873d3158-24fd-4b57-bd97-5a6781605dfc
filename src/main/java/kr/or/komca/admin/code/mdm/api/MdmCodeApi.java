package kr.or.komca.admin.code.mdm.api;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import kr.or.komca.admin.code.mdm.dto.command.request.*;
import kr.or.komca.admin.code.mdm.dto.command.response.*;
import kr.or.komca.admin.code.mdm.dto.query.condition.*;
import kr.or.komca.admin.code.mdm.dto.query.response.*;
import kr.or.komca.common.exception.response.error.CommonErrorResponse;
import kr.or.komca.common.exception.response.success.CommonSuccessResponse;
import kr.or.komca.common.utils.core.dto.response.PageListResponse;
import org.springframework.http.ResponseEntity;

/**
 * 매체 코드 API 인터페이스
 */
@Tag(name = "MdmCode", description = "매체 코드 관리 API")
public interface MdmCodeApi {

	/** ======== Query API ======== */

	@Operation(
			summary = "대분류 코드 조회",
			description = "매체코드 대분류 목록을 조회합니다."
	)
	@ApiResponses({
			@ApiResponse(responseCode = "200", description = "조회 성공"),
			@ApiResponse(responseCode = "400", description = "잘못된 요청",
					content = @Content(schema = @Schema(implementation = CommonErrorResponse.class)))
	})
	ResponseEntity<CommonSuccessResponse<PageListResponse<MdmLargeCode>>> getLargeCodes(
			@Parameter(description = "검색조건") MdmLargeCodeSearchCondition condition
	);

	@Operation(
			summary = "중분류 코드 조회",
			description = "특정 대분류에 속한 중분류 코드 목록을 조회합니다."
	)
	@ApiResponses({
			@ApiResponse(responseCode = "200", description = "조회 성공"),
			@ApiResponse(responseCode = "400", description = "잘못된 요청",
					content = @Content(schema = @Schema(implementation = CommonErrorResponse.class)))
	})
	ResponseEntity<CommonSuccessResponse<PageListResponse<MdmAveCode>>> getAveCodes(
			@Parameter(description = "대분류 코드") String largeClassCd,
			@Parameter(description = "검색조건") MdmAveCodeSearchCondition condition
	);

	@Operation(
			summary = "소분류 코드 조회",
			description = "특정 중분류에 속한 소분류 코드 목록을 조회합니다."
	)
	@ApiResponses({
			@ApiResponse(responseCode = "200", description = "조회 성공"),
			@ApiResponse(responseCode = "400", description = "잘못된 요청",
					content = @Content(schema = @Schema(implementation = CommonErrorResponse.class)))
	})
	ResponseEntity<CommonSuccessResponse<PageListResponse<MdmSmallCode>>> getSmallCodes(
			@Parameter(description = "중분류 코드") String aveClassCd,
			@Parameter(description = "검색조건") MdmSmallCodeSearchCondition condition
	);

	@Operation(
			summary = "매체 코드 조회",
			description = "특정 소분류에 속한 매체 코드 목록을 조회합니다."
	)
	@ApiResponses({
			@ApiResponse(responseCode = "200", description = "조회 성공"),
			@ApiResponse(responseCode = "400", description = "잘못된 요청",
					content = @Content(schema = @Schema(implementation = CommonErrorResponse.class)))
	})
	ResponseEntity<CommonSuccessResponse<PageListResponse<MdmCode>>> getMdmCodes(
			@Parameter(description = "소분류 코드") String smallClassCd,
			@Parameter(description = "검색조건") MdmCodeSearchCondition condition
	);

	@Operation(
			summary = "서비스 코드 조회",
			description = "특정 매체에 속한 서비스 코드 목록을 조회합니다."
	)
	@ApiResponses({
			@ApiResponse(responseCode = "200", description = "조회 성공"),
			@ApiResponse(responseCode = "400", description = "잘못된 요청",
					content = @Content(schema = @Schema(implementation = CommonErrorResponse.class)))
	})
	ResponseEntity<CommonSuccessResponse<PageListResponse<ServiceCode>>> getServiceCodes(
			@Parameter(description = "매체 코드") String mdmCd,
			@Parameter(description = "검색조건") ServiceCodeSearchCondition condition
	);

	@Operation(
			summary = "서비스 코드 엑셀 조회",
			description = "특정 매체에 속한 서비스 코드 엑셀 목록을 조회합니다."
	)
	@ApiResponses({
			@ApiResponse(responseCode = "200", description = "조회 성공"),
			@ApiResponse(responseCode = "400", description = "잘못된 요청",
					content = @Content(schema = @Schema(implementation = CommonErrorResponse.class)))
	})
	ResponseEntity<CommonSuccessResponse<PageListResponse<ServiceCodeExcel>>> getServiceCodeExcelList(
			@Parameter(description = "매체 코드") String mdmCd,
			@Parameter(description = "검색조건") ServiceCodeExcelCondition condition
	);

	/** ======== 이력관리 API ======== */

	@Operation(
			summary = "매체코드 이력 조회",
			description = "모든 매체코드의 변경 이력을 페이징으로 조회합니다."
	)
	@ApiResponses({
			@ApiResponse(responseCode = "200", description = "조회 성공"),
			@ApiResponse(responseCode = "400", description = "잘못된 요청",
					content = @Content(schema = @Schema(implementation = CommonErrorResponse.class)))
	})
	ResponseEntity<CommonSuccessResponse<PageListResponse<MdmCodeHistory>>> getMdmCodeHistoryList(
			@Parameter(description = "검색조건") MdmCodeHistorySearchCondition condition
	);

	/** ======== Command API ======== */

	@Operation(
			summary = "대분류 코드 통합 관리",
			description = "대분류 코드를 일괄로 생성/수정/삭제합니다."
	)
	@ApiResponses({
			@ApiResponse(responseCode = "200", description = "처리 성공"),
			@ApiResponse(responseCode = "400", description = "잘못된 요청",
					content = @Content(schema = @Schema(implementation = CommonErrorResponse.class)))
	})
	ResponseEntity<CommonSuccessResponse<ManageLargeCode>> manageLargeCodes(
			@Parameter(description = "대분류 코드 통합 관리 요청") ManageLargeCodeRequest request
	);

	@Operation(
			summary = "중분류 코드 통합 관리",
			description = "중분류 코드를 일괄로 생성/수정/삭제합니다."
	)
	@ApiResponses({
			@ApiResponse(responseCode = "200", description = "처리 성공"),
			@ApiResponse(responseCode = "400", description = "잘못된 요청",
					content = @Content(schema = @Schema(implementation = CommonErrorResponse.class)))
	})
	ResponseEntity<CommonSuccessResponse<ManageAveCode>> manageAveCodes(
			@Parameter(description = "중분류 코드 통합 관리 요청") ManageAveCodeRequest request
	);

	@Operation(
			summary = "소분류 코드 통합 관리",
			description = "소분류 코드를 일괄로 생성/수정/삭제합니다."
	)
	@ApiResponses({
			@ApiResponse(responseCode = "200", description = "처리 성공"),
			@ApiResponse(responseCode = "400", description = "잘못된 요청",
					content = @Content(schema = @Schema(implementation = CommonErrorResponse.class)))
	})
	ResponseEntity<CommonSuccessResponse<ManageSmallCode>> manageSmallCodes(
			@Parameter(description = "소분류 코드 통합 관리 요청") ManageSmallCodeRequest request
	);

	@Operation(
			summary = "매체 코드 통합 관리",
			description = "매체 코드를 일괄로 생성/수정/삭제합니다."
	)
	@ApiResponses({
			@ApiResponse(responseCode = "200", description = "처리 성공"),
			@ApiResponse(responseCode = "400", description = "잘못된 요청",
					content = @Content(schema = @Schema(implementation = CommonErrorResponse.class)))
	})
	ResponseEntity<CommonSuccessResponse<ManageMdmCode>> manageMdmCodes(
			@Parameter(description = "매체 코드 통합 관리 요청") ManageMdmCodeRequest request
	);

	@Operation(
			summary = "서비스 코드 통합 관리",
			description = "서비스 코드를 일괄로 생성/수정/삭제합니다."
	)
	@ApiResponses({
			@ApiResponse(responseCode = "200", description = "처리 성공"),
			@ApiResponse(responseCode = "400", description = "잘못된 요청",
					content = @Content(schema = @Schema(implementation = CommonErrorResponse.class)))
	})
	ResponseEntity<CommonSuccessResponse<ManageServiceCode>> manageServiceCodes(
			@Parameter(description = "서비스 코드 통합 관리 요청") ManageServiceCodeRequest request
	);

	@Operation(
			summary = "서비스 코드 일괄 동기화",
			description = "특정 서비스코드를 기준으로 같은 매체의 모든 서비스코드 속성을 일괄 동기화합니다."
	)
	@ApiResponses({
			@ApiResponse(responseCode = "200", description = "동기화 성공"),
			@ApiResponse(responseCode = "400", description = "잘못된 요청",
					content = @Content(schema = @Schema(implementation = CommonErrorResponse.class)))
	})
	ResponseEntity<CommonSuccessResponse<BatchUpdateServiceCode>> syncServiceCodes(
			@Parameter(description = "기준 서비스 코드") String baseSvcCd
	);

}
