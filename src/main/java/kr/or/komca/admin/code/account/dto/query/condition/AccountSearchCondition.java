package kr.or.komca.admin.code.account.dto.query.condition;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.ToString;

/**
 * 계정 코드 검색 조건 DTO
 */
@ToString
@Getter
@AllArgsConstructor
@Builder(toBuilder = true)
public class AccountSearchCondition {
    /**
     * 회계구분
     */
    @Schema(description = "회계구분", example = "001")
    private String acctnGbn;
}