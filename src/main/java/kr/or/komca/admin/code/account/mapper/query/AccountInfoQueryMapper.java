package kr.or.komca.admin.code.account.mapper.query;

import kr.or.komca.admin.code.account.dto.query.condition.AccountInfoSearchCondition;
import kr.or.komca.admin.code.account.dto.query.response.AccountInfo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface AccountInfoQueryMapper {
    /**
     * 계정 정보 목록 조회
     *
     * @param condition 검색 조건
     * @return 계정 목록
     */
    List<AccountInfo> getAccountInfoList(@Param("condition") AccountInfoSearchCondition condition);

    /**
     * cd로 계정 조회
     *
     * @param acctCd 계정 코드
     * @return 계정
     */
    AccountInfo getAccountByCd(@Param("acctCd") String acctCd);
}
