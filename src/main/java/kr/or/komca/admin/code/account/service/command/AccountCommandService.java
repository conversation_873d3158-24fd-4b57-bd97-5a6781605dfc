package kr.or.komca.admin.code.account.service.command;

import kr.or.komca.admin.code.account.dto.command.request.ManageAccountRequest;
import kr.or.komca.admin.code.account.dto.command.response.ManageAccount;

public interface AccountCommandService {

    /**
     * 계정 통합 관리
     *
     * @param request   통합 관리 요청
     * @param inspersId 사용자 ID
     * @return 관리 결과
     */
    ManageAccount manageAccount(ManageAccountRequest request, String inspersId);
}
