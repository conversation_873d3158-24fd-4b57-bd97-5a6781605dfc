package kr.or.komca.admin.code.payment.api;


import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import kr.or.komca.admin.code.payment.dto.command.request.ManagePaymentRequest;
import kr.or.komca.admin.code.payment.dto.command.response.ManagePayment;
import kr.or.komca.admin.code.payment.dto.query.condition.PaymentExcelCondition;
import kr.or.komca.admin.code.payment.dto.query.condition.PaymentSearchCondition;
import kr.or.komca.admin.code.payment.dto.query.response.Payment;
import kr.or.komca.admin.code.payment.dto.query.response.PaymentExcel;
import kr.or.komca.common.exception.response.error.CommonErrorResponse;
import kr.or.komca.common.exception.response.success.CommonSuccessResponse;
import kr.or.komca.common.utils.core.dto.response.PageListResponse;
import org.springframework.http.ResponseEntity;

@Tag(name = "Payment", description = "지급공제")
public interface PaymentApi {

    /** ======== Query API ======== */

    @Operation(
            summary = "지급공제 목록 조회",
            description = "검색 조건에 맞는 지급공제 목록을 조회합니다."
    )
    @ApiResponses({
            @ApiResponse(
                    responseCode = "200",
                    description = "조회 성공"
            ),
            @ApiResponse(
                    responseCode = "400",
                    description = "잘못된 요청",
                    content = @Content(schema = @Schema(implementation = CommonErrorResponse.class))
            ),
    })
    ResponseEntity<CommonSuccessResponse<PageListResponse<Payment>>> getPaymentList(
            @Parameter(description = "지급공제 검색 조건", required = true)
            PaymentSearchCondition condition
    );

    @Operation(
            summary = "지급공제 목록 조회",
            description = "검색 조건에 맞는 지급공제 목록을 엑셀 조회합니다."
    )
    @ApiResponses({
            @ApiResponse(
                    responseCode = "200",
                    description = "조회 성공"
            ),
            @ApiResponse(
                    responseCode = "400",
                    description = "잘못된 요청",
                    content = @Content(schema = @Schema(implementation = CommonErrorResponse.class))
            ),
    })
    ResponseEntity<CommonSuccessResponse<PageListResponse<PaymentExcel>>> getPaymentExcelList(
            @Parameter(description = "지급공제 검색 조건", required = true)
            PaymentExcelCondition condition
    );

    /** ======== Command API ======== */

    @Operation(
            summary = "지급공제 통합 관리",
            description = "지급공제를 일괄로 생성/수정/삭제합니다."
    )
    @ApiResponses({
            @ApiResponse(
                    responseCode = "200",
                    description = "처리 성공"
            ),
            @ApiResponse(
                    responseCode = "400",
                    description = "잘못된 요청",
                    content = @Content(schema = @Schema(implementation = CommonErrorResponse.class))
            ),
    })
    ResponseEntity<CommonSuccessResponse<ManagePayment>> managePayment(
            @Parameter(description = "지급공제 통합 관리 요청", required = true)
            ManagePaymentRequest request
    );
}
