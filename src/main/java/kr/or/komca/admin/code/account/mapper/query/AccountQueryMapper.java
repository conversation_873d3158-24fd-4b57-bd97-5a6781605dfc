package kr.or.komca.admin.code.account.mapper.query;

import kr.or.komca.admin.code.account.dto.query.condition.AccountSearchCondition;
import kr.or.komca.admin.code.account.dto.query.response.Account;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface AccountQueryMapper {

    /**
     * 계정 목록 조회
     *
     * @param condition 검색 조건
     * @return 계정 목록
     */
    List<Account> getAccountList(@Param("condition") AccountSearchCondition condition);
}
