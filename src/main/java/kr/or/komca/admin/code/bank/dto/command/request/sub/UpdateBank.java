package kr.or.komca.admin.code.bank.dto.command.request.sub;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Pattern;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.ToString;

/**
 * 은행 수정 요청
 */
@Getter
@Builder
@AllArgsConstructor
@ToString
@Schema(description = "은행 수정 요청")
public class UpdateBank {

    /** 은행코드 */
    @Schema(description = "은행코드", example = "001")
    @NotBlank
    @Size(max = 7)
    private String bankCd;

    /** 은행명 */
    @Schema(description = "은행명", example = "한국은행")
    @NotBlank
    @Size(max = 5)
    private String bankNm;

    /** 비고 */
    @Schema(description = "비고", example = "사용")
    @NotBlank
    @Size(max = 60)
    private String remak;

    /** 대표은행 */
    @Schema(description = "대표은행", example = "Y")
    @NotBlank
    @Pattern(regexp = "^[YN]$")
    private String bankCdYn;

    /** 신탁사용여부 */
    @Schema(description = "신탁사용여부", example = "Y")
    @NotBlank
    @Pattern(regexp = "^[YN]$")
    private String trustUseYn;

    /** 홈페이지사용여부 */
    @Schema(description = "홈페이지사용여부", example = "Y")
    @NotBlank
    @Pattern(regexp = "^[YN]$")
    private String hompUseYn;
}
