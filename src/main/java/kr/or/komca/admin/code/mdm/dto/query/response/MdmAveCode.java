package kr.or.komca.admin.code.mdm.dto.query.response;

import io.swagger.v3.oas.annotations.media.Schema; 
import lombok.Builder; 
import lombok.Getter; 
import lombok.AllArgsConstructor; 
import lombok.NoArgsConstructor;

/**
* 매체코드(중분류) 조회에 대한 응답 DTO
*/
@Getter 
@Builder 
@AllArgsConstructor 
@NoArgsConstructor 
@Schema(description = "매체코드(중분류) 조회에 대한 응답 DTO")
public class MdmAveCode {

    /** 대분류코드 */
    @Schema(description = "대분류코드", example = "A")
    private String largeClassCd;

    /** 중분류코드 */
    @Schema(description = "중분류코드", example = "AA")
    private String aveClassCd;

    /** 중분류명 */
    @Schema(description = "중분류명", example = "방송")
    private String aveClassNm;

    /** 국내계정코드 */
    @Schema(description = "국내계정코드", example = "0845")
    private String domAcctCd;

    /** 국외계정코드 */
    @Schema(description = "국외계정코드", example = "0846")
    private String abrAcctCd;

    /** 국내지부계정코드 */
    @Schema(description = "국내지부계정코드", example = "0845")
    private String domBranAcctCd;

    /** 국외지부계정코드 */
    @Schema(description = "국외지부계정코드", example = "0846")
    private String abrBranAcctCd;

    /** 사용여부 */
    @Schema(description = "사용여부", example = "Y")
    private String useYn;

    /** 정렬순서 */
    @Schema(description = "정렬순서", example = "1")
    private int sortOrd;
    
    /** 출력순번 */
    @Schema(description = "출력순번", example = "1")
    private int prntSeq;
}
