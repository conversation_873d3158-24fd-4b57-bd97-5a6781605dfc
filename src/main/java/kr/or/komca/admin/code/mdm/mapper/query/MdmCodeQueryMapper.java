package kr.or.komca.admin.code.mdm.mapper.query;

import kr.or.komca.admin.code.mdm.dto.query.condition.*;
import kr.or.komca.admin.code.mdm.dto.query.response.*;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 공통 코드 데이터에 대한 Query 처리를 위한 Mapper 인터페이스
 */
@Mapper
public interface MdmCodeQueryMapper {

	/**
	 * 매체코드(대분류) 조회
	 *
	 * @return 매체코드(대분류) 조회에 대한 결과
	 */
	List<MdmLargeCode> getLargeMdmCodeList(
			@Param("condition") MdmLargeCodeSearchCondition condition
	);

	/**
	 * 매체코드(중분류) 조회
	 *
	 * @param largeClassCd 대분류 코드
	 * @return 매체코드(중분류) 조회에 대한 결과
	 */
	List<MdmAveCode> getAveMdmCodeList(
			@Param("condition") MdmAveCodeSearchCondition condition,
			@Param("largeClassCd") String largeClassCd
	);

	/**
	 * 매체코드(소분류) 조회
	 *
	 * @param aveClassCd 중분류 코드
	 * @return 매체코드(소분류) 조회에 대한 결과
	 */
	List<MdmSmallCode> getSmallMdmCodeList(
			@Param("condition") MdmSmallCodeSearchCondition condition,
			@Param("aveClassCd") String aveClassCd
	);

	/**
	 * 매체코드(매체) 조회
	 *
	 * @param smallClassCd 소분류 코드
	 * @return 매체코드(매체) 조회에 대한 결과
	 */
	List<MdmCode> getMdmCodeList(
			@Param("condition") MdmCodeSearchCondition condition,
			@Param("smallClassCd") String smallClassCd
	);

	/**
	 * 서비스 코드 조회
	 *
	 * @param mdmCd 매체 코드
	 * @return 서비스 코드 조회에 대한 결과
	 */
	List<ServiceCode> getServiceCodeList(
			@Param("condition") ServiceCodeSearchCondition condition,
			@Param("mdmCd") String mdmCd
	);

	/**
	 * 대분류 코드 존재 여부 확인
	 *
	 * @param largeClassCd 대분류 코드
	 * @return 존재 여부
	 */
	boolean existsLargeCode(
			@Param("largeClassCd") String largeClassCd
	);
	
	/**
	 * 중분류 코드 존재 여부 확인
	 *
	 * @param aveClassCd 중분류 코드
	 * @return 존재 여부
	 */
	boolean existsAveCode(
			@Param("aveClassCd") String aveClassCd
	);
	
	/**
	 * 소분류 코드 존재 여부 확인
	 *
	 * @param smallClassCd 소분류 코드
	 * @return 존재 여부
	 */
	boolean existsSmallCode(
			@Param("smallClassCd") String smallClassCd
	);
	
	/**
	 * 매체 코드 존재 여부 확인
	 *
	 * @param mdmCd 매체 코드
	 * @return 존재 여부
	 */
	boolean existsMdmCode(
			@Param("mdmCd") String mdmCd
	);
	
	/**
	 * 서비스 코드 존재 여부 확인
	 *
	 * @param svcCd 서비스 코드
	 * @return 존재 여부
	 */
	boolean existsServiceCode(
			@Param("svcCd") String svcCd
	);
	
	/**
     * 서비스코드로 매체코드 조회
     *
     * @param svcCd 서비스코드
     * @return 매체코드
     */
    String findMdmCdByServiceCode(
            @Param("svcCd") String svcCd
    );

    /**
     * 매체코드 단건 조회
     *
     * @param mdmCd 매체코드
     * @return 매체코드 정보
     */
    MdmCode findMdmCodeByMdmCd(
            @Param("mdmCd") String mdmCd
    );

    /**
     * 중분류가 특정 대분류에 속하는지 확인
     *
     * @param largeClassCd 대분류 코드
     * @param aveClassCd 중분류 코드
     * @return 계층 관계 존재 여부
     */
    boolean existsAveCodeInLargeClass(
            @Param("largeClassCd") String largeClassCd,
            @Param("aveClassCd") String aveClassCd
    );

    /**
     * 소분류가 특정 중분류에 속하는지 확인
     *
     * @param aveClassCd 중분류 코드
     * @param smallClassCd 소분류 코드
     * @return 계층 관계 존재 여부
     */
    boolean existsSmallCodeInAveClass(
            @Param("aveClassCd") String aveClassCd,
            @Param("smallClassCd") String smallClassCd
    );

    /**
     * 매체코드가 특정 소분류에 속하는지 확인
     *
     * @param smallClassCd 소분류 코드
     * @param mdmCd 매체 코드
     * @return 계층 관계 존재 여부
     */
    boolean existsMdmCodeInSmallClass(
            @Param("smallClassCd") String smallClassCd,
            @Param("mdmCd") String mdmCd
    );

    /**
     * 서비스코드가 특정 매체코드에 속하는지 확인
     *
     * @param mdmCd 매체 코드
     * @param svcCd 서비스 코드
     * @return 계층 관계 존재 여부
     */
    boolean existsServiceCodeInMdmCode(
            @Param("mdmCd") String mdmCd,
            @Param("svcCd") String svcCd
    );
}
