package kr.or.komca.admin.code.account.dto.query.response;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

/**
 * 계정 응답 DTO
 */
@ToString
@Getter
@AllArgsConstructor
@NoArgsConstructor
@Builder(toBuilder = true)
public class Account {
    /**
     * 계정코드
     */
    @Schema(description = "계정코드", example = "0002")
    private String acctCd;

    /**
     * 계정명
     */
    @Schema(description = "계정명", example = "유동자산")
    private String acctNm;

    /**
     * 레벨
     */
    @Schema(description = "레벨", example = "2")
    private String lvl;

    /**
     * 전표여부
     */
    @Schema(description = "전표여부", example = "N")
    private String slipYn;
}