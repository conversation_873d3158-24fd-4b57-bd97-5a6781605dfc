package kr.or.komca.admin.code.account.dto.command.request;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.Valid;
import kr.or.komca.admin.code.account.dto.command.request.sub.CreateAccount;
import kr.or.komca.admin.code.account.dto.command.request.sub.UpdateAccount;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.ToString;

import java.util.List;

/**
 * 계정 통합 관리 요청 DTO
 */
@Getter
@Builder
@AllArgsConstructor
@ToString
@Schema(description = "계정 통합 관리 요청 (C/U/D)")
public class ManageAccountRequest {

    @Schema(description = "생성 목록")
    private List<@Valid CreateAccount> create;

    @Schema(description = "수정 목록")
    private List<@Valid UpdateAccount> update;

    @Schema(description = "삭제 목록 (삭제 계정 리스트)")
    private List<String> delete;
}
