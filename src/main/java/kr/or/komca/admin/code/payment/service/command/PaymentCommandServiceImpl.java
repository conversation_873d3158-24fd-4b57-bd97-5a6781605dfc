package kr.or.komca.admin.code.payment.service.command;

import kr.or.komca.admin.code.payment.dto.command.request.ManagePaymentRequest;
import kr.or.komca.admin.code.payment.dto.command.request.sub.CreatePayment;
import kr.or.komca.admin.code.payment.dto.command.request.sub.UpdatePayment;
import kr.or.komca.admin.code.payment.dto.command.response.ManagePayment;
import kr.or.komca.admin.code.payment.mapper.command.PaymentCommandMapper;
import kr.or.komca.admin.code.payment.service.query.PaymentQueryService;
import kr.or.komca.common.exception.core.CommonException;
import kr.or.komca.common.exception.response.error.code.CommonErrorCode;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Slf4j
@RequiredArgsConstructor
@Service
public class PaymentCommandServiceImpl implements PaymentCommandService {

    private final PaymentCommandMapper paymentCommandMapper;
    private final PaymentQueryService paymentQueryService;

    @Override
    @Transactional
    public ManagePayment managePayment(ManagePaymentRequest request, String inspersId) {
        log.info("지급공제 통합 관리 요청: {}", request);

        // 각 작업 수행 결과
        int createAffectedRows = 0;
        int updateAffectedRows = 0;
        int deleteAffectedRows = 0;

        List<CreatePayment> create = request.getCreate();

        // create 요청이 존재한다면 지급공제 추가
        if (create != null && !create.isEmpty()) {
            for (CreatePayment createPayment : create) {
                createAffectedRows += createPayment(createPayment, inspersId);
            }
        }

        List<UpdatePayment> update = request.getUpdate();

        // update 요청이 존재한다면 지급공제 수정
        if (update != null && !update.isEmpty()) {
            for (UpdatePayment updatePayment : update) {
                updateAffectedRows += updatePayment(updatePayment.getSuppDedctCd(), inspersId, updatePayment);
            }
        }

        List<String> delete = request.getDelete();

        // delete 요청이 존재한다면 지급공제 삭제
        if (delete != null && !delete.isEmpty()) {
            for (String suppDedctCd : delete) {
                deleteAffectedRows += deletePayment(suppDedctCd);
            }
        }

        return ManagePayment.builder()
                .createAffectedRows(createAffectedRows)
                .updateAffectedRows(updateAffectedRows)
                .deleteAffectedRows(deleteAffectedRows)
                .build();
    }

    // ================== Private Create Methods ==================

    private int createPayment(CreatePayment request, String inspersId) {
        log.info("지급공제 생성: {}", request);

        String suppDedctCd = request.getSuppDedctCd();

        // 지급공제 중복 확인
        paymentQueryService.validatePaymentDuplicate(suppDedctCd);

        int affectedRows = paymentCommandMapper.createPayment(suppDedctCd, inspersId, request);

        if (affectedRows <= 0) {
            throw new CommonException(CommonErrorCode.INTERNAL_SERVER_ERROR,
                    String.format("지급공제 생성에 실패했습니다. suppDedctCd: %s", suppDedctCd));
        }

        return affectedRows;
    }

    private int updatePayment(String suppDedctCd, String modpersId, UpdatePayment request) {
        log.info("지급공제 수정: suppDedctCd={}, request={}", suppDedctCd, request);

        // 존재하는 코드인지 확인
        paymentQueryService.validateExistPayment(suppDedctCd);

        int affectedRows = paymentCommandMapper.updatePayment(suppDedctCd, modpersId, request);

        if (affectedRows <= 0) {
            throw new CommonException(
                    CommonErrorCode.INTERNAL_SERVER_ERROR,
                    String.format("지급공제 수정에 실패했습니다. suppDedctCd: %s", suppDedctCd)
            );
        }

        return affectedRows;
    }

    // ================== Private Delete Methods ==================

    private int deletePayment(String suppDedctCd) {
        log.info("지급공제 삭제: suppDedctCd={}", suppDedctCd);

        // 존재하는 코드인지 확인
        paymentQueryService.validateExistPayment(suppDedctCd);

        int affectedRows = paymentCommandMapper.deletePayment(suppDedctCd);

        if (affectedRows <= 0) {
            throw new CommonException(
                    CommonErrorCode.INTERNAL_SERVER_ERROR,
                    String.format("지급공제 삭제에 실패했습니다. suppDedctCd: %s", suppDedctCd)
            );
        }

        return affectedRows;
    }
}
