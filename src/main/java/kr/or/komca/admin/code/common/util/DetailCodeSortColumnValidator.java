package kr.or.komca.admin.code.common.util;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

/**
 * Sort Validator
 */
@Slf4j
@Component
public class DetailCodeSortColumnValidator {

    /**
     * 허용된 정렬 컬럼 매핑 정보
     * Key: 프론트엔드 요청 파라미터명
     * Value: 실제 DB 테이블의 컬럼명
     */
    private static final Map<String, String> ALLOWED_SORT_COLUMNS_MAP = new HashMap<>() {
        {
            put("parCd", "par_cd");                     // 상위 코드
            put("cd", "cd");                            // 코드 값
            put("cdNm", "cd_nm");                       // 코드 이름
            put("cdEtc", "cd_etc");                     // 추가 정보
            put("remak", "remak");                      // 비고
            put("sortOrd", "TO_NUMBER(sort_ord)");      // 정렬 순서
            put("useYn", "use_yn");                     // 사용 여부
            put("inspersId", "inspers_id");             // 등록자 ID
            put("insDt", "ins_dt");                     // 등록일시
            put("modpersId", "modpers_id");             // 수정자 ID
            put("modDt", "mod_dt");                     // 수정일시
        }
    };

    /**
     * 정렬 컬럼 유효성 검사 및 매핑
     */
    public static String validate(String sortColumn) {
        return ALLOWED_SORT_COLUMNS_MAP.get(sortColumn);
    }
}