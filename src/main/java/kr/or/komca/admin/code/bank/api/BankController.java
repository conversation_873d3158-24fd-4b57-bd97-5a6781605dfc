package kr.or.komca.admin.code.bank.api;

import jakarta.validation.Valid;
import kr.or.komca.admin.code.bank.dto.command.request.ManageBankRequest;
import kr.or.komca.admin.code.bank.dto.command.response.ManageBank;
import kr.or.komca.admin.code.bank.dto.query.condition.BankExcelCondition;
import kr.or.komca.admin.code.bank.dto.query.condition.BankSearchCondition;
import kr.or.komca.admin.code.bank.dto.query.response.Bank;
import kr.or.komca.admin.code.bank.dto.query.response.BankExcel;
import kr.or.komca.admin.code.bank.service.command.BankCommandService;
import kr.or.komca.admin.code.bank.service.query.BankExcelService;
import kr.or.komca.admin.code.bank.service.query.BankQueryService;
import kr.or.komca.common.auth.support.utils.context.UserContextHolder;
import kr.or.komca.common.exception.response.success.CommonSuccessResponse;
import kr.or.komca.common.utils.core.dto.response.PageListResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

@Slf4j
@RestController
@RequestMapping("/api/v1/code/bank")
@RequiredArgsConstructor
public class BankController implements BankApi {

    private final BankExcelService bankExcelService;
    private final BankCommandService bankCommandService;
    private final BankQueryService bankQueryService;

    /**
     * ======== Query API ========
     */

    @GetMapping
    public ResponseEntity<CommonSuccessResponse<PageListResponse<Bank>>> getBankList(
        @ModelAttribute @Valid BankSearchCondition condition
    ) {
        log.info("은행 목록 조회 요청: {}", condition);
        PageListResponse<Bank> response = bankQueryService.getBankList(condition);
        return CommonSuccessResponse.ok(response);
    }

    @Override
    @GetMapping("/excel")
    public ResponseEntity<CommonSuccessResponse<PageListResponse<BankExcel>>> getBankExcelList(
        @ModelAttribute @Valid BankExcelCondition condition
    ) {
        PageListResponse<BankExcel> response = bankExcelService.getBankExcelList(condition);
        return CommonSuccessResponse.ok(response);
    }

    /**
     * ======== Command API ========
     */

    @PostMapping
    public ResponseEntity<CommonSuccessResponse<ManageBank>> manageBank(
        @Valid @RequestBody ManageBankRequest request
    ) {
        log.info("은행 통합 관리 요청: {}", request);

        String inspersId = UserContextHolder.getContext().getUserId();

        ManageBank result = bankCommandService.manageBank(request, inspersId);
        return CommonSuccessResponse.ok(result);
    }
}
