package kr.or.komca.admin.common.exception.handler;


import lombok.extern.slf4j.Slf4j;
import org.springframework.core.Ordered;
import org.springframework.core.annotation.Order;
import org.springframework.web.bind.annotation.RestControllerAdvice;

/**
 * 애플리케이션의 전역 예외 처리를 담당하는 핸들러 클래스
 * RestController 에서 발생하는 예외들을 일관된 형식으로 처리
 */
@Slf4j
@RestControllerAdvice
@Order(Ordered.HIGHEST_PRECEDENCE)
public class GlobalExceptionHandler {
    /**
     * CommonException 타입의 예외를 처리
     * 예외 발생 시 해당 예외의 에러 코드를 사용하여 표준화된 에러 응답을 생성
     * 여기서 처리하지 않아도 공통 에러 모듈의 핸들러에서 처리됨
     */
//    @ExceptionHandler(CommonException.class)
//    public ResponseEntity<BaseResponse> handleBaseException(CommonException e) {
//        // 각 서비스에 특화된 에러 처리
//        return CommonErrorResponse.of(e.getErrorCode());
//    }
}
