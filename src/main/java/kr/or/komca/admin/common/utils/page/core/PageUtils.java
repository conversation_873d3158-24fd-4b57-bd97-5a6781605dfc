package kr.or.komca.admin.common.utils.page.core;

/**
 * 페이징 처리에 필요한 유틸리티 메서드들을 제공하는 클래스
 * 페이지 계산과 관련된 정적 메서드들을 포함
 */
public class PageUtils {
    /**
     * 유틸리티 클래스의 인스턴스화를 방지하기 위한 private 생성자
     */
    private PageUtils() {
        // 유틸리티 클래스 인스턴스화 방지
    }

    /**
     * 전체 항목 수와 페이지 크기를 기반으로 총 페이지 수를 계산
     */
    public static int calculateTotalPages(int totalElements, Integer size) {
        if (size == null || size <= 0) {
            // 기본 페이지 수 10
            size = 10;
        }
        return (totalElements + size - 1) / size;
    }
}
