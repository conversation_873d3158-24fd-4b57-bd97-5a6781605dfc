package kr.or.komca.admin.common.enums;

import lombok.Getter;

import java.util.Arrays;

@Getter
public enum SortOrderEnums {
	ASC(1),
	DESC(2);

	private final int orderValue;

	SortOrderEnums(int orderValue) {
		this.orderValue = orderValue;
	}

	public static String getByOrderValue(int orderValue) {
		return Arrays.stream(values())
				.filter(direction -> direction.orderValue == orderValue)
				.findFirst()
				.map(Enum::name)
				.orElse("");
	}
}

