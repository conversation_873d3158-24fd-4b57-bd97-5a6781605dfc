package kr.or.komca.admin.common.config.security;

import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.configurers.AbstractHttpConfigurer;
import org.springframework.security.web.SecurityFilterChain;

/**
 * 보안 설정이 비활성화된 환경에서 사용되는 Spring Security 설정 클래스
 * 모든 요청에 대해 인증을 무시하고 접근을 허용
 * 'komca.security.enabled' 속성이 false 로 설정된 경우에만 활성화
 */
@Slf4j
@Configuration
@ConditionalOnProperty(prefix = "komca.security", name = "enabled", havingValue = "false")
public class PermitSecurityConfig {

	/**
	 * 모든 요청에 대해 인증 없이 접근을 허용하는 Security Filter Chain 을 구성
	 * CSRF 보호를 비활성화하고 모든 요청에 대해 접근을 허용
	 */
	@Bean
	public SecurityFilterChain filterChain(HttpSecurity http) throws Exception {
		http
				.csrf(AbstractHttpConfigurer::disable)
				.authorizeHttpRequests(auth -> auth.anyRequest().permitAll());
		return http.build();
	}
}
