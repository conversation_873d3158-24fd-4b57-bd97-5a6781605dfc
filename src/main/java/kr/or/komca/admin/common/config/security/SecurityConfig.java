package kr.or.komca.admin.common.config.security;

import kr.or.komca.common.auth.exception.handler.SecurityExceptionHandler;
import kr.or.komca.common.auth.security.config.BaseSecurityConfig;
import kr.or.komca.common.auth.security.filter.JwtAuthenticationFilter;
import kr.or.komca.common.auth.security.filter.JwtExceptionFilter;
import kr.or.komca.common.auth.security.filter.UserContextMdcFilter;
import kr.or.komca.admin.common.config.security.consts.SecurityUrlPattern;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.web.SecurityFilterChain;

/**
 * 애플리케이션의 보안 설정을 담당하는 핵심 설정 클래스
 * JWT 인증, MDC 로깅, 예외 처리 등 보안 관련 전반적인 설정을 관리
 * 'komca.security.enabled' 속성이 true 로 설정된 경우에만 활성화
 */
@Configuration
@ConditionalOnProperty(prefix = "komca.security", name = "enabled", havingValue = "true")
public class SecurityConfig extends BaseSecurityConfig {

	/** Security 설정을 위한 SecurityFilterChain을 생성하고 구성 */
	@Bean
	public SecurityFilterChain securityFilterChain(HttpSecurity http) throws Exception {
		return super.createFilterChain(http);
	}

	/** Security 관련 필터들과 예외 처리기를 주입받는 생성자 */
	public SecurityConfig(
			JwtAuthenticationFilter jwtAuthenticationFilter,
			SecurityExceptionHandler securityExceptionHandler,
			JwtExceptionFilter jwtExceptionFilter,
			UserContextMdcFilter userContextMdcFilter
	) {
		super(jwtAuthenticationFilter, jwtExceptionFilter, securityExceptionHandler, userContextMdcFilter);
	}

	/**
	 * 인증 관련 필터들을 설정
	 * 필요한 경우 추가 필터를 등록할 수 있음
	 */
	@Override
	protected void configureAuthentication(HttpSecurity http) {
		// --------- 추가 필터 등록 ------------
		// Filter 등록 예시
		// http.addFilterBefore(traceIdFilter, SecurityContextHolderFilter.class);


		// ----------------------------------
		
	}

	/**
	 * JWT 인증 필터에 대한 URL 패턴을 설정
	 * 특정 URL 패턴에 대해서는 JWT 인증을 건너뛰도록 설정할 수 있음
	 */
	@Override
	protected void configureJwtFilterPatterns(JwtAuthenticationFilter jwtAuthenticationFilter) {
		// ** 인증 계층 URL 설정 **
		// Jwt 인증 필터 스킵 URL 등록
		jwtAuthenticationFilter.addSkipPatterns(SecurityUrlPattern.PUBLIC_URLS); // 예시
		jwtAuthenticationFilter.addSkipPatterns("/api/public/**");      // 예시
		jwtAuthenticationFilter.addSkipPatterns("/actuator/**");      // EKS Actuator

		// ------- 추가 스킵 URL 등록 ----------

        //filter.addSkipPatterns(
        //			"/example/**",
        //		    "/example2/*"
        //		 );

		// ----------------------------------
	}

	/**
	 * URL 별 접근 권한을 설정
	 * 공개 API 와 인증이 필요한 API 에 대한 권한을 설정
	 */
	@Override
	protected void configureAuthorization(HttpSecurity http) throws Exception {
		// ** 인가 계층 URL 설정 **
		http
				.authorizeHttpRequests(auth ->
						auth
								.requestMatchers(SecurityUrlPattern.PUBLIC_URLS).permitAll()
								.requestMatchers("/api/public/**").permitAll() // 예시
								.requestMatchers("/actuator/**").permitAll() // EKS Actuator
								.anyRequest().authenticated()
				);
		/* *
		 * 예시
		 * 					// 1. 정적 리소스 접근 설정
		 *                 .requestMatchers("/css/**", "/js/**", "/images/**").permitAll()
		 *                 // 2. API 권한 설정
		 *                 .requestMatchers("/api/admin/**").hasRole("ADMIN")
		 *                 .requestMatchers("/api/manager/**").hasAnyRole("ADMIN", "MANAGER")
		 *                 .requestMatchers("/api/user/**").hasRole("USER")
		 *                 // 3. 특정 HTTP 메서드에 대한 권한 설정
		 *                 .requestMatchers(HttpMethod.POST, "/api/articles/**").hasRole("WRITER")
		 *                 .requestMatchers(HttpMethod.PUT, "/api/articles/**").hasRole("EDITOR")
		 *                 .requestMatchers(HttpMethod.DELETE, "/api/articles/**").hasRole("ADMIN")
		 *                 // 4. IP 기반 접근 제어
		 *                 .requestMatchers("/internal/**").hasIpAddress("***********/24")
		 *                 // 5. 그외 인증 필요
		 *                 .anyRequest().authenticated()
		 */
	}
}