package kr.or.komca.admin.common.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.ToString;


@ToString
@Getter
@AllArgsConstructor
@Builder(toBuilder = true)
@Schema(description = "공통 페이징만 있는 조건")
public class CommonPageSearchCondition {
	@Schema(description = "페이지 번호", example = "1")
	@NotNull
	private int page;

	@Schema(description = "페이지 크기", example = "10")
	@NotNull
	private int pageSize;
}
