package kr.or.komca.admin.bscon.service.command;

import kr.or.komca.admin.bscon.dto.command.request.CreateBsconAttachmentRequest;
import kr.or.komca.admin.bscon.dto.command.request.sub.CreateAttachmentInfo;
import kr.or.komca.admin.bscon.dto.command.response.BsconAttachmentUploadUrlResponse;
import kr.or.komca.admin.bscon.dto.command.response.BsconAttachmentDownloadUrlResponse;

import java.util.List;

/**
 * 거래처 첨부파일 Command 서비스
 */
public interface BsconAttachmentCommandService {

    /**
     * 거래처 첨부파일 업로드용 PreSigned URL 생성 (/bscon 경로로 고정)
     *
     * @param request 첨부파일 업로드 URL 발급 요청
     * @param inspersId 등록자ID
     * @return PreSigned URL 및 관련 정보
     */
    BsconAttachmentUploadUrlResponse createAttachmentUploadUrl(CreateBsconAttachmentRequest request, String inspersId);

    /**
     * 거래처 첨부파일 다운로드 URL 생성
     *
     * @param bsconCd 거래처코드
     * @param mngNo 관리번호
     * @return S3 다운로드 URL 정보
     */
    BsconAttachmentDownloadUrlResponse generateAttachmentDownloadUrl(String bsconCd, Long mngNo);

    /**
     * 거래처 첨부파일 메타데이터 저장 (DB)
     *
     * @param bsconCd 거래처코드
     * @param createAttachments 생성할 첨부파일 정보 목록
     * @param inspersId 등록자ID
     * @return 생성된 첨부파일 수
     */
    int confirmAttachmentUploads(String bsconCd, List<CreateAttachmentInfo> createAttachments, String inspersId);

    /**
     * 거래처 첨부파일 삭제 (DB)
     *
     * @param bsconCd 거래처코드
     * @param deleteAttachmentMngNos 삭제할 첨부파일 관리번호 목록
     * @return 삭제된 첨부파일 수
     */
    int deleteAttachments(String bsconCd, List<Long> deleteAttachmentMngNos);

}
