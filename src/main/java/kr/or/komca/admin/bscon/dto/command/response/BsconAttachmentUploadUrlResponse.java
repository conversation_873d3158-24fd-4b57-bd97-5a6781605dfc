package kr.or.komca.admin.bscon.dto.command.response;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Builder;
import lombok.Getter;
import lombok.ToString;

/**
 * 거래처 첨부파일 업로드 URL 생성 응답 DTO
 */
@Getter
@ToString
@Builder
@Schema(description = "거래처 첨부파일 업로드 URL 생성 응답 (S3 PreSigned URL 방식)")
public class BsconAttachmentUploadUrlResponse {

    @Schema(description = "첨부파일명", example = "계약서.pdf")
    private String attchFileNm;

    @Schema(description = "S3 업로드용 PreSigned URL")
    private String preSignedUrl;

    @Schema(description = "S3 파일 키", example = "bscon/20240610_123456_random.pdf")
    private String fileKey;

    @Schema(description = "PreSigned URL 만료 시간 (분 단위)", example = "15")
    private Integer expirationMinutes;

}