package kr.or.komca.admin.bscon.dto.command.request;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * 거래처 첨부파일 업로드 URL 생성 요청 DTO
 */
@Getter
@Setter
@ToString
@Schema(description = "거래처 첨부파일 업로드 URL 생성 요청")
public class CreateBsconAttachmentRequest {

    @Schema(description = "업로드할 파일명 (확장자 포함)", example = "contract.pdf", maxLength = 255)
    @NotBlank
    @Size(max = 255)
    private String fileName;

    @Schema(description = "파일 MIME 타입 (예: application/pdf, image/jpeg)", example = "application/pdf", maxLength = 100)
    @Size(max = 100)
    private String contentType;

}
