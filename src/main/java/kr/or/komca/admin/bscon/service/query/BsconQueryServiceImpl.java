package kr.or.komca.admin.bscon.service.query;

import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import kr.or.komca.admin.bscon.dto.query.condition.BsconSearchCondition;
import kr.or.komca.admin.bscon.dto.query.condition.RepresentativeBsconSearchCondition;
import kr.or.komca.admin.bscon.dto.query.response.Bscon;
import kr.or.komca.admin.bscon.dto.query.response.BsconDetail;
import kr.or.komca.admin.bscon.dto.query.response.BsconGroupDetail;
import kr.or.komca.admin.bscon.dto.query.response.BsconRep;
import kr.or.komca.admin.bscon.enums.errorcode.BsconErrorCode;
import kr.or.komca.admin.bscon.mapper.query.BsconQueryMapper;
import kr.or.komca.common.exception.core.CommonException;
import kr.or.komca.common.exception.response.error.code.CommonErrorCode;
import kr.or.komca.common.utils.core.dto.response.PageListResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * 거래처 조회 서비스 구현
 */
@Slf4j
@Service
@RequiredArgsConstructor
@Transactional(readOnly = true)
public class BsconQueryServiceImpl implements BsconQueryService {

    private final BsconQueryMapper bsconQueryMapper;

    @Override
    public PageListResponse<Bscon> getBsconList(BsconSearchCondition condition) {
        try (Page<Bscon> page = PageHelper.startPage(condition.getPage(), condition.getPageSize())) {
            List<Bscon> bsconList = bsconQueryMapper.getBsconList(condition);

            PageInfo<Bscon> pageInfo = new PageInfo<>(bsconList);

            return PageListResponse.<Bscon>builder()
                    .contents(pageInfo.getList())
                    .totalElements(pageInfo.getTotal())
                    .page(pageInfo.getPageNum())
                    .totalPages(pageInfo.getPages())
                    .pageSize(pageInfo.getPageSize())
                    .build();
        } catch (Exception e) {
            log.error("거래처 목록 조회 중 오류 발생", e);
            throw new CommonException(
                    CommonErrorCode.INTERNAL_SERVER_ERROR, 
                    "거래처 목록 조회 중 오류가 발생했습니다."
            );
        }
    }

    @Override
    public PageListResponse<BsconRep> getRepresentativeBsconList(RepresentativeBsconSearchCondition condition) {
        try (Page<BsconRep> page = PageHelper.startPage(condition.getPage(), condition.getPageSize())) {
            List<BsconRep> bsconRepList = bsconQueryMapper.getRepresentativeBsconList(condition);

            PageInfo<BsconRep> pageInfo = new PageInfo<>(bsconRepList);

            return PageListResponse.<BsconRep>builder()
                    .contents(pageInfo.getList())
                    .totalElements(pageInfo.getTotal())
                    .page(pageInfo.getPageNum())
                    .totalPages(pageInfo.getPages())
                    .pageSize(pageInfo.getPageSize())
                    .build();
        } catch (Exception e) {
            log.error("대표 거래처 목록 조회 중 오류 발생", e);
            throw new CommonException(
                    CommonErrorCode.INTERNAL_SERVER_ERROR, 
                    "대표 거래처 목록 조회 중 오류가 발생했습니다."
            );
        }
    }

    @Override
    public PageListResponse<BsconGroupDetail> getBsconGroupDetail(String bsconCd) {
        try (Page<BsconGroupDetail> page = PageHelper.startPage(1, Integer.MAX_VALUE)) {
            List<BsconGroupDetail> bsconGroupDetailList = bsconQueryMapper.getBsconGroupDetail(bsconCd);

            PageInfo<BsconGroupDetail> pageInfo = new PageInfo<>(bsconGroupDetailList);

            return PageListResponse.<BsconGroupDetail>builder()
                    .contents(pageInfo.getList())
                    .totalElements(pageInfo.getTotal())
                    .page(pageInfo.getPageNum())
                    .totalPages(pageInfo.getPages())
                    .pageSize(pageInfo.getPageSize())
                    .build();
        } catch (Exception e) {
            log.error("그룹 내 거래처 상세 조회 중 오류 발생", e);
            throw new CommonException(
                    CommonErrorCode.INTERNAL_SERVER_ERROR, 
                    "그룹 내 거래처 상세 조회 중 오류가 발생했습니다."
            );
        }
    }

    @Override
    public String getNextBsconCode() {
        return bsconQueryMapper.getNextBsconCode();
    }

    @Override
    public Long getNextGroupSequence() {
        return bsconQueryMapper.getNextGroupSequence();
    }

    @Override
    public BsconDetail getBsconDetail(String bsconCd) {
        // 거래처 존재 여부 확인
        validateBsconExists(bsconCd);

        return bsconQueryMapper.getBsconDetailById(bsconCd);
    }

    @Override
    public Long getGroupSequenceByBsconCd(String bsconCd) {
        // 거래처 존재 여부 확인
        validateBsconExists(bsconCd);

        return bsconQueryMapper.getGroupSequenceByBsconCd(bsconCd);
    }

    @Override
    public boolean existsBscon(String bsconCd) {
        return bsconQueryMapper.existsBscon(bsconCd);
    }

    @Override
    public boolean existsGroup(Long grpSeq) {
        return bsconQueryMapper.existsGroup(grpSeq);
    }

    @Override
    public String getCurrentRepYn(String bsconCd) {
        // 거래처 존재 여부 확인
        validateBsconExists(bsconCd);

        return bsconQueryMapper.getCurrentRepYn(bsconCd);
    }

    @Override
    @Transactional(readOnly = true)
    public void validateGroupExists(Long grpSeq) {
        if (!existsGroup(grpSeq)) {
            throw new CommonException(
                BsconErrorCode.GROUP_NOT_FOUND,
                String.format("존재하지 않는 그룹입니다: %s", grpSeq)
            );
        }
    }

    @Override
    @Transactional(readOnly = true)
    public void validateBsconExists(String bsconCd) {
        if (!existsBscon(bsconCd)) {
            throw new CommonException(
                    BsconErrorCode.BSCON_NOT_FOUND,
                    String.format("존재하지 않는 거래처입니다: %s", bsconCd)
            );
        }
    }

    @Override
    @Transactional(readOnly = true)
    public void validateBsconCodeDuplicate(String bsconCd) {
        if (existsBscon(bsconCd)) {
            throw new CommonException(
                    BsconErrorCode.BSCON_ALREADY_EXISTS,
                    String.format("이미 존재하는 거래처 코드입니다: %s", bsconCd)
            );
        }
    }
}
