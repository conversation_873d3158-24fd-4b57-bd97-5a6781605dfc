package kr.or.komca.admin.bscon.dto.query.response;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.ToString;

/**
 * 거래처 첨부파일 상세 응답 DTO (파일 다운로드용)
 */
@Getter
@Builder(toBuilder = true)
@AllArgsConstructor
@ToString(exclude = "attchFileCtent")  // BLOB 데이터는 toString에서 제외
@Schema(description = "거래처 첨부파일 상세")
public class BsconAttachmentDetail {

    @Schema(description = "거래처코드", example = "00001")
    private String bsconCd;

    @Schema(description = "관리번호", example = "1")
    private Long mngNo;

    @Schema(description = "첨부파일종류", example = "계약서")
    private String uploadDocGbn;

    @Schema(description = "첨부파일명", example = "계약서.pdf")
    private String attchFileNm;

    @Schema(description = "서버파일명", example = "20240610_123456_계약서.pdf")
    private String svrFileNm;

    @Schema(description = "서버파일경로", example = "/home/<USER>/webdocs/fileupload")
    private String svrFileRout;

    @Schema(description = "첨부파일내용", hidden = true)
    private byte[] attchFileCtent;

    @Schema(description = "업로드일시", example = "20240610")
    private String uploadDay;

    @Schema(description = "등록일시", example = "2024-06-10 12:34:56")
    private String insDt;

    @Schema(description = "등록자ID", example = "admin")
    private String inspersId;

    @Schema(description = "수정일시", example = "2024-06-10 12:34:56")
    private String modDt;

    @Schema(description = "수정자ID", example = "admin")
    private String modpersId;

}
