package kr.or.komca.admin.bscon.service.command;

import kr.or.komca.admin.bscon.dto.command.request.CreateBsconRequest;
import kr.or.komca.admin.bscon.dto.command.request.CreateGroupBsconRequest;
import kr.or.komca.admin.bscon.dto.command.request.UpdateBsconRequest;
import kr.or.komca.admin.bscon.dto.command.request.sub.UpdateBsconAttachment;
import kr.or.komca.admin.bscon.dto.command.request.sub.UpdateBsconCommonInfo;
import kr.or.komca.admin.bscon.dto.command.response.CreateBscon;
import kr.or.komca.admin.bscon.dto.command.response.UpdateBscon;
import kr.or.komca.admin.bscon.dto.command.response.DeleteBscon;
import kr.or.komca.admin.bscon.mapper.command.BsconCommandMapper;
import kr.or.komca.admin.bscon.service.query.BsconQueryService;
import kr.or.komca.admin.code.mdm.service.query.MdmCodeQueryService;
import kr.or.komca.common.exception.core.CommonException;
import kr.or.komca.common.exception.response.error.code.CommonErrorCode;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * 거래처 Command 서비스 구현체
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class BsconCommandServiceImpl implements BsconCommandService {

	private final BsconCommandMapper bsconCommandMapper;
	private final BsconAttachmentCommandService bsconAttachmentCommandService;
	private final BsconQueryService bsconQueryService;
	private final MdmCodeQueryService mdmCodeQueryService;

	@Override
	@Transactional
	public CreateBscon createBscon(CreateBsconRequest request, String inspersId) {
		log.info("거래처 생성 시작 - inspersId: {}", inspersId);

		// 거래처 코드 생성
		String bsconCd = bsconQueryService.getNextBsconCode();

		// 새로운 그룹 시퀀스 생성
		Long grpSeq = bsconQueryService.getNextGroupSequence();

		// 거래처 기본 정보에서 매체코드 검증
		if (request.getInfo() != null) {
			validateMdmCodeIfPresent(
					request.getInfo().getLargeClassCd(),
					request.getInfo().getAveClassCd(),
					request.getInfo().getSmallClassCd(),
					request.getInfo().getMdmCd()
			);
		}

		// 새로운 그룹 생성
		int affectedRows = bsconCommandMapper.createBscon(bsconCd, grpSeq, inspersId, request);

		if (affectedRows <= 0) {
			log.error("거래처 생성 실패 - bsconCd: {}, affectedRows: {}", bsconCd, affectedRows);
			throw new CommonException(CommonErrorCode.INTERNAL_SERVER_ERROR,
					String.format("거래처 생성에 실패했습니다. bsconCd: %s", bsconCd));
		}

		// 첨부파일 처리 (첨부파일 서비스 위임)
		int createdAttachmentsCount = 0;
		if (request.getAttachment() != null) {
			createdAttachmentsCount = bsconAttachmentCommandService.confirmAttachmentUploads(
					bsconCd, request.getAttachment().getCreate(), inspersId
			);
		}

		CreateBscon result = CreateBscon.builder()
				.bsconCd(bsconCd)
				.grpSeq(grpSeq)
				.createdAttachmentsCount(createdAttachmentsCount)
				.build();
				
		log.info("거래처 생성 완료 - bsconCd: {}, grpSeq: {}, 첨부파일: {}건", 
				bsconCd, grpSeq, createdAttachmentsCount);
		return result;
	}

	@Override
	@Transactional
	public CreateBscon createGroupBscon(CreateGroupBsconRequest request, String inspersId) {
		String refBsconCd = request.getRefBsconCd();
		log.info("그룹 거래처 추가 시작 - refBsconCd: {}, inspersId: {}", refBsconCd, inspersId);

		// 거래처 코드 생성
		String bsconCd = bsconQueryService.getNextBsconCode();

		// 참조 거래처 유효성 검증
		bsconQueryService.validateBsconExists(refBsconCd);

		// 거래처 기본 정보에서 매체코드 검증
		if (request.getInfo() != null) {
			validateMdmCodeIfPresent(
					request.getInfo().getLargeClassCd(),
					request.getInfo().getAveClassCd(),
					request.getInfo().getSmallClassCd(),
					request.getInfo().getMdmCd()
			);
		}

		// 대표 거래처로 설정 요청된 경우 기존 대표 거래처 해제
		if (request.getInfo() != null && "Y".equals(request.getInfo().getRepYn())) {
			Long grpSeq = getGroupSequenceByBsconCd(refBsconCd);

			// 새로 추가되는 거래처이므로, 현재 대표 여부 확인 없이 업데이트
			updateRepresentativeStatus(grpSeq, inspersId);
		}

		// 그룹 거래처 추가 생성
		int affectedRows = bsconCommandMapper.createGroupBscon(bsconCd, refBsconCd, inspersId, request);

		if (affectedRows <= 0) {
			log.error(
					"그룹 거래처 추가 실패 - bsconCd: {}, refBsconCd: {}, affectedRows: {}",
					bsconCd, refBsconCd, affectedRows
			);
			throw new CommonException(CommonErrorCode.INTERNAL_SERVER_ERROR,
					String.format("그룹 거래처 추가에 실패했습니다. bsconCd: %s", bsconCd));
		}

		// 첨부파일 처리 (첨부파일 서비스 위임)
		int createdAttachmentsCount = 0;
		if (request.getAttachment() != null) {
			createdAttachmentsCount = bsconAttachmentCommandService.confirmAttachmentUploads(
					bsconCd, request.getAttachment().getCreate(), inspersId
			);
		}

		Long finalGrpSeq = getGroupSequenceByBsconCd(refBsconCd);
		CreateBscon result = CreateBscon.builder()
				.bsconCd(bsconCd)
				.grpSeq(finalGrpSeq)
				.createdAttachmentsCount(createdAttachmentsCount)
				.build();
				
		log.info(
				"그룹 거래처 추가 완료 - bsconCd: {}, grpSeq: {}, 첨부파일: {}건",
				bsconCd, finalGrpSeq, createdAttachmentsCount
		);
		return result;
	}

	// Todo: 대표거래처인 경우, 비활성화할 수 없도록 (다른 거래처를 대표로 해야함)
	@Override
	@Transactional
	public UpdateBscon updateBscon(String bsconCd, UpdateBsconRequest request, String modpersId) {
		log.info("거래처 수정 시작 - bsconCd: {}, modpersId: {}", bsconCd, modpersId);

		// 존재하는 거래처인지 확인
		bsconQueryService.validateBsconExists(bsconCd);

		// 그룹 시퀀스 조회
		Long grpSeq = getGroupSequenceByBsconCd(bsconCd);

		// 거래처 기본 정보에서 매체코드 검증
		if (request.getInfo() != null) {
			validateMdmCodeIfPresent(
					request.getInfo().getLargeClassCd(),
					request.getInfo().getAveClassCd(),
					request.getInfo().getSmallClassCd(),
					request.getInfo().getMdmCd()
			);
		}

		// 대표거래처 변경 여부 반환
		boolean representativeChanged = false;

		// 대표 거래처로 변경 요청된 경우 대표 설정 프로세스 실행
		if (request.getInfo() != null && "Y".equals(request.getInfo().getRepYn())) {
			// 현재 거래처가 이미 대표인지 확인
			String currentRepYn = bsconQueryService.getCurrentRepYn(bsconCd);

			// 현재 대표가 아닌 경우에만 프로세스 실행 (대표가 현재 수정중인 거래처가 되어야 함)
			if (!"Y".equals(currentRepYn)) {
				updateRepresentativeStatus(grpSeq, modpersId);
				representativeChanged = true;
			}
		}

		// 공통정보가 있는 경우 그룹 전체 업데이트
		UpdateBsconCommonInfo commonInfo = request.getCommonInfo();
		if (commonInfo != null) {
			// 그룹 내 공통정보 일괄 업데이트
			int affectedRows = bsconCommandMapper.updateGroupCommonInfo(grpSeq, modpersId, commonInfo);

			if (affectedRows <= 0) {
				log.error("그룹 공통정보 수정 실패 - grpSeq: {}, affectedRows: {}", grpSeq, affectedRows);
				throw new CommonException(
						CommonErrorCode.INTERNAL_SERVER_ERROR,
						String.format("그룹 공통정보 수정에 실패했습니다. grpSeq: %s", grpSeq)
				);
			}
		}

		// 개별 정보 업데이트 (info가 있는 경우에만)
		if (request.getInfo() != null) {
			int affectedRows = bsconCommandMapper.updateBscon(bsconCd, modpersId, request.getInfo());

			if (affectedRows <= 0) {
				log.error("거래처 수정 실패 - bsconCd: {}, affectedRows: {}", bsconCd, affectedRows);
				throw new CommonException(
						CommonErrorCode.INTERNAL_SERVER_ERROR,
						String.format("거래처 수정에 실패했습니다. bsconCd: %s", bsconCd)
				);
			}
		}

		// 첨부파일 처리 (첨부파일 서비스 위임)
		int createdAttachmentsCount = 0;
		int deletedAttachmentsCount = 0;

		// 첨부파일 요청 객체
		UpdateBsconAttachment attachment = request.getAttachment();

		if (attachment != null) {
			// 첨부파일 생성 후 메타데이터 등록
			createdAttachmentsCount = bsconAttachmentCommandService.confirmAttachmentUploads(
					bsconCd, attachment.getCreate(), modpersId
			);

			// 첨부파일 삭제 후 메타데이터 삭제
			deletedAttachmentsCount = bsconAttachmentCommandService.deleteAttachments(
					bsconCd, attachment.getDelete()
			);
		}

		UpdateBscon result = UpdateBscon.builder()
				.bsconCd(bsconCd)
				.representativeChanged(representativeChanged)
				.createdAttachmentsCount(createdAttachmentsCount)
				.deletedAttachmentsCount(deletedAttachmentsCount)
				.build();
				
		log.info(
				"거래처 수정 완료 - bsconCd: {}, 대표변경: {}, 첨부생성: {}건, 첨부삭제: {}건",
				bsconCd, representativeChanged, createdAttachmentsCount, deletedAttachmentsCount
		);
		return result;
	}

	// ========== 유틸리티 메서드 ==========

	/**
	 * 매체코드 검증 (값이 있는 경우에만)
	 * 매체코드 계층 구조 검증 로직 구현
	 */
	private void validateMdmCodeIfPresent(
			String largeClassCd,
			String aveClassCd,
			String smallClassCd,
			String mdmCd
	) {
		// 매체코드 관련 필드가 모두 null인 경우 검증 생략
		if (isAllMdmCodeFieldsNull(largeClassCd, aveClassCd, smallClassCd, mdmCd)) {
			return;
		}

		// 매체코드 계층 무결성 검증 (null 값 이후 모든 필드가 null인지)
		// 서비스 코드는 BSCON에서 사용하지 않으므로 null로 전달
		mdmCodeQueryService.validateMdmCodeSequentialHierarchy(largeClassCd, aveClassCd, smallClassCd, mdmCd, null);

		// 매체 코드 계층 구조 검증 (존재 여부 + 계층 관계)
		// 서비스 코드는 BSCON에서 사용하지 않으므로 null로 전달
		mdmCodeQueryService.validateMdmCodeHierarchy(largeClassCd, aveClassCd, smallClassCd, mdmCd, null);
	}

	/**
	 * 매체코드 관련 필드가 모두 null인지 확인
	 */
	private boolean isAllMdmCodeFieldsNull(String largeClassCd, String aveClassCd, String smallClassCd, String mdmCd) {
		return (largeClassCd == null || largeClassCd.trim().isEmpty()) &&
				(aveClassCd == null || aveClassCd.trim().isEmpty()) &&
				(smallClassCd == null || smallClassCd.trim().isEmpty()) &&
				(mdmCd == null || mdmCd.trim().isEmpty());
	}

	/**
	 * 현재 대표 거래처 해제
	 */
	private void updateRepresentativeStatus(
			Long grpSeq,
			String inspersId
	) {
		// 기존 대표 거래처 비활성화
		int affectedRows = bsconCommandMapper.clearGroupRepresentative(
				grpSeq,
				inspersId
		);

		// 대표 거래처가 없는 그룹은 존재하지 않는다고 가정
		if (affectedRows <= 0) {
			throw new CommonException(
					CommonErrorCode.INTERNAL_SERVER_ERROR,
					String.format("그룹 대표 거래처 비활성화에 실패했습니다. grpSeq: %s", grpSeq)
			);
		}

		log.debug(
			"그룹 대표 거래처 비활성화: grpSeq={}, affectedRows={}",
			grpSeq, affectedRows
		);
	}

	/**
	 * 그룹 시퀀스 조회
	 */
	private Long getGroupSequenceByBsconCd(String bsconCd) {
		return bsconQueryService.getGroupSequenceByBsconCd(bsconCd);
	}

	@Override
	@Transactional
	public DeleteBscon deleteBscon(String bsconCd, String delpersId) {
		log.info("거래처 삭제 요청 - bsconCd: {}, delpersId: {}", bsconCd, delpersId);

		// 존재하는 거래처인지 확인
		bsconQueryService.validateBsconExists(bsconCd);

		// 대표거래처 삭제 방지 검증
		String currentRepYn = bsconQueryService.getCurrentRepYn(bsconCd);
		if ("Y".equals(currentRepYn)) {
			throw new CommonException(
					CommonErrorCode.BAD_REQUEST,
					String.format("대표거래처는 삭제할 수 없습니다. bsconCd: %s", bsconCd)
			);
		}

		int affectedRows = bsconCommandMapper.deleteBscon(bsconCd, delpersId);

		if (affectedRows <= 0) {
			throw new CommonException(
					CommonErrorCode.INTERNAL_SERVER_ERROR,
					String.format("거래처 삭제에 실패했습니다. bsconCd: %s", bsconCd)
			);
		}

		log.info("거래처 삭제 완료 - bsconCd: {}, affectedRows: {}", bsconCd, affectedRows);

		return DeleteBscon.builder()
				.bsconCd(bsconCd)
				.affectedRows(affectedRows)
				.build();
	}
}
