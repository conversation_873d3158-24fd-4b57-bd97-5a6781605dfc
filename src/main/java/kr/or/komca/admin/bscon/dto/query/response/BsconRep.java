package kr.or.komca.admin.bscon.dto.query.response;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.ToString;

/**
 * 대표 거래처 목록 항목 응답 DTO
 */
@Builder(toBuilder = true)
@AllArgsConstructor
@ToString
@Getter
@Schema(description = "대표 거래처 정보 응답")
public class BsconRep {
    
    /** 거래처 코드 */
    @Schema(description = "거래처 코드", example = "09882")
    private String bsconCd;
    
    /** 거래처 한글명 */
    @Schema(description = "거래처 한글명", example = "한국음악저작권협회")
    private String bsconKoNm;

    /** 거래처 구분 (SB: 사용 구분) [ common - 00269 ] */
    @Schema(description = "거래처 구분", example = "001")
    private String bsconGbn;
    
    /** 대표자명 */
    @Schema(description = "대표자명", example = "홍길동")
    private String reppers;
    
    /** 전화번호 */
    @Schema(description = "전화번호", example = "02-123-4567")
    private String tel;

    /** 팩스번호 */
    @Schema(description = "팩스번호", example = "02-123-4567")
    private String fax;

    /** 등록번호(주민/사업자번호) */
    @Schema(description = "등록번호(주민/사업자번호)", example = "123-45-67890")
    private String insNo;

    /** 이메일 */
    @Schema(description = "이메일", example = "<EMAIL>")
    private String email;

    /** 등록일시 */
    @Schema(description = "등록일시", example = "2023-01-01 12:00:00")
    private String insDt;

    /** 그룹 시퀀스 */
    @Schema(description = "그룹 시퀀스", example = "1")
    private Long grpSeq;

    /** 대표 여부 */
    @Schema(description = "대표 여부", example = "Y")
    private String repYn;

    /** 매체코드 대분류 */
    @Schema(description = "매체코드 대분류", example = "A")
    private String largeClassCd;

    /** 매체코드 중분류 */
    @Schema(description = "매체코드 중분류", example = "AA")
    private String aveClassCd;

    /** 매체코드 소분류 */
    @Schema(description = "매체코드 소분류", example = "AA01")
    private String smallClassCd;

    /** 매체코드 */
    @Schema(description = "매체코드", example = "AA0101")
    private String mdmCd;
}
