package kr.or.komca.admin.bscon.dto.query.response;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.ToString;

/**
 * 거래처 상세 정보 응답 DTO
 */
@Builder(toBuilder = true)
@AllArgsConstructor
@ToString
@Getter
@Schema(description = "거래처 상세 정보 응답")
public class BsconDetail {
    
    // ========== 공통 정보 ==========
    
    /** 거래처 코드 */
    @Schema(description = "거래처 코드", example = "09882")
    private String bsconCd;
    
    /** 거래처 한글명 */
    @Schema(description = "거래처 한글명", example = "한국음악저작권협회")
    private String bsconKoNm;

    /** 거래처 영문명 */
    @Schema(description = "거래처 영문명", example = "Korea Music Copyright Association")
    private String bsconEnNm;

    /** 사업자 구분 [ common - 00049 ] */
    @Schema(description = "사업자 구분", example = "001")
    private String bizGbn;

    /** 거래처 구분 (SB: 사용 구분) [ common - 00269 ] */
    @Schema(description = "거래처 구분", example = "001")
    private String bsconGbn;

    /** 업태내용 */
    @Schema(description = "업태내용", example = "서비스업")
    private String bscdtnCtent;

    /** 업종내용 */
    @Schema(description = "업종내용", example = "음악저작권관리")
    private String bstypCtent;

    // ========== 개별 정보 ==========
    
    /** 대표자명 */
    @Schema(description = "대표자명", example = "홍길동")
    private String reppers;

    /** 등록번호(주민/사업자번호) */
    @Schema(description = "등록번호(주민/사업자번호)", example = "123-45-67890")
    private String insNo;

    /** 매체코드 대분류 */
    @Schema(description = "매체코드 대분류", example = "A")
    private String largeClassCd;

    /** 매체코드 중분류 */
    @Schema(description = "매체코드 중분류", example = "AA")
    private String aveClassCd;

    /** 매체코드 소분류 */
    @Schema(description = "매체코드 소분류", example = "AA01")
    private String smallClassCd;

    /** 매체코드 */
    @Schema(description = "매체코드", example = "AA0101")
    private String mdmCd;

    /** 주매체코드 */
    @Schema(description = "주매체코드", example = "A")
    private String mstMdmCd;

    /** 사용여부 */
    @Schema(description = "사용여부", example = "Y")
    private String useYn;

    /** 주소 */
    @Schema(description = "주소", example = "서울시 강남구")
    private String addr;

    /** 주소상세 */
    @Schema(description = "주소상세", example = "테헤란로 123")
    private String addrDtl;

    /** 주소2 */
    @Schema(description = "주소2", example = "서울시 서초구")
    private String addr2;

    /** 상세주소2 */
    @Schema(description = "상세주소2", example = "강남대로 456")
    private String addrDtl2;

    /** 우편번호 */
    @Schema(description = "우편번호", example = "12345")
    private String zip;

    /** 우편번호2 */
    @Schema(description = "우편번호2", example = "67890")
    private String zip2;

    /** 전화번호 */
    @Schema(description = "전화번호", example = "02-123-4567")
    private String tel;

    /** 전화번호2 */
    @Schema(description = "전화번호2", example = "02-987-6543")
    private String tel2;

    /** 팩스번호 */
    @Schema(description = "팩스번호", example = "02-123-4567")
    private String fax;

    /** 이메일 */
    @Schema(description = "이메일", example = "<EMAIL>")
    private String email;

    /** 이메일2 */
    @Schema(description = "이메일2", example = "<EMAIL>")
    private String email2;

    /** 담당자 */
    @Schema(description = "담당자", example = "김담당")
    private String udtkpers;

    /** 담당자전화번호 */
    @Schema(description = "담당자전화번호", example = "010-1234-5678")
    private String udtkpersTel;

    /** 비고 */
    @Schema(description = "비고", example = "특이사항")
    private String remak;

    /** 회원ID */
    @Schema(description = "회원ID", example = "member123")
    private String mbId;

    /** 계좌번호 */
    @Schema(description = "계좌번호", example = "123-456-789012")
    private String accnNo;

    /** 예금주 */
    @Schema(description = "예금주", example = "홍길동")
    private String dpstr;

    /** 은행코드 */
    @Schema(description = "은행코드", example = "001")
    private String bankCd;

    /** 자본금 */
    @Schema(description = "자본금", example = "1000000")
    private Long fundAmt;

    /** 침해관리대상여부 */
    @Schema(description = "침해관리대상여부", example = "Y")
    private String ifmntMngObjYn;

    /** 우편반송여부 */
    @Schema(description = "우편반송여부", example = "N")
    private String postReturnYn;

    /** 관리비율 */
    @Schema(description = "관리비율", example = "10.5")
    private Double mngRate;

    /** 계약구분 [ common - 00050 ] */
    @Schema(description = "계약구분", example = "001")
    private String contrGbn;

    /** 계약체결여부 */
    @Schema(description = "계약체결여부", example = "Y")
    private String contrCclsYn;

    /** 계약기간시작일자 */
    @Schema(description = "계약기간시작일자", example = "2023-01-01")
    private String contrTermStartDay;

    /** 계약기간종료일자 */
    @Schema(description = "계약기간종료일자", example = "2023-12-31")
    private String contrTermEndDay;

    /** 국외입금코드 */
    @Schema(description = "국외입금코드", example = "USD")
    private String abrDpstCd;

    /** 추가상세 */
    @Schema(description = "추가상세", example = "추가정보1")
    private String addDtl;

    /** 추가상세2 */
    @Schema(description = "추가상세2", example = "추가정보2")
    private String addDtl2;

    /** 대표 여부 */
    @Schema(description = "대표 여부", example = "Y")
    private String repYn;

    /** 그룹 시퀀스 */
    @Schema(description = "그룹 시퀀스", example = "1")
    private Long grpSeq;

    /** 등록자 ID */
    @Schema(description = "등록자 ID", example = "admin")
    private String inspersId;
    
    /** 등록일시 */
    @Schema(description = "등록일시", example = "2023-01-01 12:00:00")
    private String insDt;

    /** 수정자 ID */
    @Schema(description = "수정자 ID", example = "admin")
    private String modpersId;
    
    /** 수정일시 */
    @Schema(description = "수정일시", example = "2023-01-02 15:30:00")
    private String modDt;
}
