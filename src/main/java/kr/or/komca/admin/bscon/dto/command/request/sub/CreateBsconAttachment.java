package kr.or.komca.admin.bscon.dto.command.request.sub;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.Valid;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.ToString;

import java.util.List;

/**
 * 거래처 첨부파일 생성 DTO
 */
@ToString
@Getter
@AllArgsConstructor
@Builder(toBuilder = true)
@Schema(description = "거래처 첨부파일 생성")
public class CreateBsconAttachment {

    /** 생성할 첨부파일 정보 목록 */
    @Schema(description = "생성할 첨부파일 정보 목록")
    private List<@Valid CreateAttachmentInfo> create;
}