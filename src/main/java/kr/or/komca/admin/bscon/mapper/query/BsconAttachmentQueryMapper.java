package kr.or.komca.admin.bscon.mapper.query;

import kr.or.komca.admin.bscon.dto.query.condition.BsconAttachmentSearchCondition;
import kr.or.komca.admin.bscon.dto.query.response.BsconAttachment;
import kr.or.komca.admin.bscon.dto.query.response.BsconAttachmentDetail;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 거래처 첨부파일 조회 매퍼
 */
@Mapper
public interface BsconAttachmentQueryMapper {

    List<BsconAttachment> getBsconAttachmentList(
            @Param("condition") BsconAttachmentSearchCondition condition
    );

    BsconAttachmentDetail getBsconAttachmentDetail(
            @Param("bsconCd") String bsconCd,
            @Param("mngNo") Long mngNo
    );

    boolean existsBsconAttachment(
            @Param("bsconCd") String bsconCd,
            @Param("mngNo") Long mngNo
    );

    BsconAttachmentDetail getBsconAttachmentByFileKey(
            @Param("bsconCd") String bsconCd,
            @Param("fileKey") String fileKey
    );

}
