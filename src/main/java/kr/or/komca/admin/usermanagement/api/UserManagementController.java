package kr.or.komca.admin.usermanagement.api;

import jakarta.validation.Valid;
import kr.or.komca.admin.common.dto.CommonPageSearchCondition;
import kr.or.komca.admin.role.dto.query.response.RoleMenu;
import kr.or.komca.admin.usermanagement.dto.query.response.UserRole;
import kr.or.komca.admin.usermanagement.dto.command.request.ManageUserRequest;
import kr.or.komca.admin.usermanagement.dto.command.response.ManageUser;
import kr.or.komca.admin.usermanagement.dto.query.condition.UserExcelCondition;
import kr.or.komca.admin.usermanagement.dto.query.condition.UserManageSearchCondition;
import kr.or.komca.admin.usermanagement.dto.query.response.UserExcel;
import kr.or.komca.admin.usermanagement.dto.query.response.UserManagement;
import kr.or.komca.admin.usermanagement.dto.query.response.UserTempMenuAuth;
import kr.or.komca.admin.usermanagement.dto.query.response.UserMdmAuth;
import kr.or.komca.admin.usermanagement.dto.query.condition.UserMdmAuthSearchCondition;
import kr.or.komca.admin.usermanagement.service.command.UserManagementCommandService;
import kr.or.komca.admin.usermanagement.service.query.UserManagementQueryService;
import kr.or.komca.common.auth.support.utils.context.UserContextHolder;
import kr.or.komca.common.exception.response.success.CommonSuccessResponse;
import kr.or.komca.common.utils.core.dto.response.ListResponse;
import kr.or.komca.common.utils.core.dto.response.PageListResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;


@Slf4j
@RestController
@RequestMapping("/api/v1/user-management")
@RequiredArgsConstructor
public class UserManagementController implements UserManagementApi {

	private final UserManagementQueryService userManagementQueryService;
	private final UserManagementCommandService userManagementCommandService;

	// ------------------ Query ----------------------
	@Override
	@GetMapping
	public ResponseEntity<CommonSuccessResponse<PageListResponse<UserManagement>>> getUserList (
			@Valid @ModelAttribute UserManageSearchCondition condition
	) {
		log.info("사용자 목록 조회 요청: {}", condition);
		
		PageListResponse<UserManagement> result = userManagementQueryService.getUserList(condition);
		return CommonSuccessResponse.ok(result);
	}

	@Override
	@GetMapping("/excel")
	public ResponseEntity<CommonSuccessResponse<PageListResponse<UserExcel>>> getUserExcelList (
			@Valid @ModelAttribute UserExcelCondition condition
	) {
		log.info("사용자 엑셀 데이터 조회 요청: {}", condition);
		
		PageListResponse<UserExcel> result = userManagementQueryService.getUserExcelList(condition);
		return CommonSuccessResponse.ok(result);
	}

	@Override
	@GetMapping("/{userId}")
	public ResponseEntity<CommonSuccessResponse<UserManagement>> getUserById (
			@PathVariable String userId
	) {
		log.info("사용자 상세 조회 요청 - userId: {}", userId);
		
		UserManagement result = userManagementQueryService.getUserById(userId);
		return CommonSuccessResponse.ok(result);
	}

	@Override
	@GetMapping("/{userId}/exist")
	public ResponseEntity<CommonSuccessResponse<Boolean>> validateExistUser(
			@PathVariable String userId
	) {
		log.info("사용자 존재 여부 검증 요청 - userId: {}", userId);
		
		userManagementQueryService.validateUserExists(userId);
		return CommonSuccessResponse.ok(true);
	}

	@Override
	@GetMapping("/{userId}/role")
	public ResponseEntity<CommonSuccessResponse<ListResponse<UserRole>>> getUserRoleList(
			@PathVariable String userId
	) {
		log.info("사용자 역할 목록 조회 요청 - userId: {}", userId);
		
		ListResponse<UserRole> userRoleList = userManagementQueryService.getUserRoleList(userId);
		return CommonSuccessResponse.ok(userRoleList);
	}

	@Override
	@GetMapping("/{userId}/auth/menu")
	public ResponseEntity<CommonSuccessResponse<PageListResponse<RoleMenu>>> getUserRoleMenuList(
			@PathVariable String userId,
			@Valid @ModelAttribute CommonPageSearchCondition condition
	) {
		PageListResponse<RoleMenu> userRoleMenuList = userManagementQueryService.getUserRoleMenuList(userId, condition);
		return CommonSuccessResponse.ok(userRoleMenuList);
	}

	@GetMapping("/{userId}/auth/menu/temp")
	public ResponseEntity<CommonSuccessResponse<ListResponse<UserTempMenuAuth>>> getUserTempMenuAuthList(
			@PathVariable String userId
	) {
		ListResponse<UserTempMenuAuth> tempRoleByUserId = userManagementQueryService.getTempAuthByUserId(userId);

		return CommonSuccessResponse.ok(tempRoleByUserId);
	}

	@Override
	@GetMapping("/{userId}/auth/mdm")
	public ResponseEntity<CommonSuccessResponse<PageListResponse<UserMdmAuth>>> getUserMdmAuthList(
			@PathVariable String userId,
			@Valid @ModelAttribute UserMdmAuthSearchCondition condition
	) {
		log.info("사용자 매체 코드 권한 조회 요청: userId={}, condition={}", userId, condition);

		PageListResponse<UserMdmAuth> mdmAuthByUserId = userManagementQueryService.getMdmAuthByUserIdWithPaging(userId, condition);
		return CommonSuccessResponse.ok(mdmAuthByUserId);
	}

	// ------------------ Command ----------------------

	@Override
	@PostMapping("/{userId}")
	public ResponseEntity<CommonSuccessResponse<ManageUser>> updateUser (
			@PathVariable String userId,
			@Valid @RequestBody ManageUserRequest command
		) {
		log.info("사용자 수정 요청: userId={}, command={}", userId, command);

		// 추가/수정/삭제 모든 경우를 한번에 처리
		String editpersId = UserContextHolder.getContext().getUserId();

		ManageUser result = userManagementCommandService.updateUser(userId, editpersId, command);
		return CommonSuccessResponse.ok(result);
	}
}
