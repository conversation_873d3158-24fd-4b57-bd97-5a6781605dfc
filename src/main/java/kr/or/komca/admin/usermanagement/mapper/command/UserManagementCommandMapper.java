package kr.or.komca.admin.usermanagement.mapper.command;

import kr.or.komca.admin.usermanagement.dto.command.request.sub.CreateTempAuth;
import kr.or.komca.admin.usermanagement.dto.command.request.sub.UpdateTempAuth;
import kr.or.komca.admin.usermanagement.dto.command.request.sub.UpdateUserInfo;
import kr.or.komca.admin.usermanagement.dto.command.request.sub.UpdateUserRole;
import kr.or.komca.admin.usermanagement.dto.command.request.sub.CreateMdmAuth;
import kr.or.komca.admin.usermanagement.dto.command.request.sub.UpdateMdmAuth;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 사용자 관리 명령 매퍼 인터페이스
 */
@Mapper
public interface UserManagementCommandMapper {

	/**
	 * 사용자 정보 수정
	 *
	 * @param userId 사용자 ID
	 * @param modpersId 수정자 ID
	 * @param command 수정할 사용자 정보
	 * @return 수정된 행 수
	 */
	int updateUser(
			@Param("userId") String userId,
			@Param("modpersId") String modpersId,
			@Param("command") UpdateUserInfo command
	);

	/**
	 * 사용자 역할 추가
	 * 특정 사용자에게 역할을 추가합니다.
	 *
	 * @param userId 사용자 ID
	 * @param inspersId 등록자 ID
	 * @param command 추가할 역할 정보
	 * @return 추가된 행 수
	 */
	int createUserRole(
			@Param("userId") String userId,
			@Param("inspersId") String inspersId,
			@Param("command") UpdateUserRole command
	);

	/**
	 * 사용자 역할 삭제
	 * 특정 사용자에게서 역할을 제거합니다.
	 *
	 * @param userId 사용자 ID
	 * @param inspersId 수정자 ID
	 * @param command 삭제할 역할 정보
	 * @return 삭제된 행 수
	 */
	int deleteUserRole(
			@Param("userId") String userId,
			@Param("inspersId") String inspersId,
			@Param("command") UpdateUserRole command
	);

	int createUserTempAuth(
			@Param("userId") String userId,
			@Param("inspersId") String editUserId,
			@Param("command") List<CreateTempAuth> command
	);

	int updateUserTempAuth(
			@Param("userId") String userId,
			@Param("modpersId") String editUserId,
			@Param("command") List<UpdateTempAuth> command
	);

	int deleteUserTempAuth(
			@Param("userId") String userId,
			@Param("command") List<String> command
	);

	/**
	 * 사용자 매체 코드 권한 추가
	 *
	 * @param userId 사용자 ID
	 * @param inspersId 등록자 ID
	 * @param command 추가할 매체 코드 권한 정보
	 * @return 추가된 행 수
	 */
	int createUserMdmAuth(
			@Param("userId") String userId,
			@Param("inspersId") String inspersId,
			@Param("command") CreateMdmAuth command
	);

	/**
	 * 사용자 매체 코드 권한 수정
	 *
	 * @param userId 사용자 ID
	 * @param modpersId 수정자 ID
	 * @param command 수정할 매체 코드 권한 정보
	 * @return 수정된 행 수
	 */
	int updateUserMdmAuth(
			@Param("userId") String userId,
			@Param("modpersId") String modpersId,
			@Param("command") List<UpdateMdmAuth> command
	);
}