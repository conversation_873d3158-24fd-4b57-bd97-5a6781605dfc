package kr.or.komca.admin.usermanagement.dto.command.response;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;

/**
 * 사용자 업데이트 응답 DTO
 */
@NoArgsConstructor
@AllArgsConstructor
@Getter
@Builder(toBuilder = true)
public class ManageUser {
	/** 사용자 ID */
	@Schema(description = "사용자 ID", example = "K123456")
	private String userId;

	/** 유저 정보 영향 받은 행 수 */
	@Schema(description = "유저 정보 영향 받은 행 수", example = "3")
	private int infoAffectedRows;

	/** 유저 역할 생성 영향 받은 행 수 */
	@Schema(description = "유저 역할 생성 영향 받은 행 수", example = "3")
	private int createRoleAffectedRows;

	/** 유저 역할 삭제 영향 받은 행 수 */
	@Schema(description = "유저 역할 삭제 영향 받은 행 수", example = "3")
	private int deleteRoleAffectedRows;

	/** 유저 임시 권한 생성 영향 받은 행 수 */
	@Schema(description = "유저 임시 권한 생성 받은 행 수", example = "3")
	private int createTempAuthAffectedRows;

	/** 유저 임시 권한 수정 영향 받은 행 수 */
	@Schema(description = "유저 임시 권한 수정 영향 받은 행 수", example = "3")
	private int updateTempAuthAffectedRows;

	/** 유저 임시 권한 삭제 영향 받은 행 수 */
	@Schema(description = "유저 임시 권한 삭제 영향 받은 행 수", example = "3")
	private int deleteTempAuthAffectedRows;

	/** 유저 매체 코드 권한 생성 영향 받은 행 수 */
	@Schema(description = "유저 매체 코드 권한 생성 영향 받은 행 수", example = "3")
	private int createMdmAuthAffectedRows;

	/** 유저 매체 코드 권한 수정 영향 받은 행 수 */
	@Schema(description = "유저 매체 코드 권한 수정 영향 받은 행 수", example = "3")
	private int updateMdmAuthAffectedRows;

}
