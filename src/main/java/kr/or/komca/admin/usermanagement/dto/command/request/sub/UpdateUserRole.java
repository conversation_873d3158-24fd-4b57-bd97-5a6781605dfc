package kr.or.komca.admin.usermanagement.dto.command.request.sub;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.ToString;

import java.util.List;

/**
 * 사용자 역할 업데이트 요청 DTO
 */
@Getter
@Builder
@ToString
@AllArgsConstructor
public class UpdateUserRole {
	/** 생성할 역할 코드 목록 */
	@Schema(description = "생성할 역할 코드 목록", example = "[\"00001\", \"00002\"]")
	private List<String> create;

	/** 삭제할 역할 코드 목록 */
	@Schema(description = "삭제할 역할 코드 목록", example = "[\"00003\", \"00004\"]")
	private List<String> delete;
}
