package kr.or.komca.admin.usermanagement.service.query;

import kr.or.komca.admin.common.dto.CommonPageSearchCondition;
import kr.or.komca.admin.role.dto.query.response.RoleMenu;
import kr.or.komca.admin.usermanagement.dto.query.response.UserRole;
import kr.or.komca.admin.usermanagement.dto.query.condition.UserExcelCondition;
import kr.or.komca.admin.usermanagement.dto.query.condition.UserManageSearchCondition;
import kr.or.komca.admin.usermanagement.dto.query.response.UserExcel;
import kr.or.komca.admin.usermanagement.dto.query.response.UserManagement;
import kr.or.komca.admin.usermanagement.dto.query.response.UserTempMenuAuth;
import kr.or.komca.admin.usermanagement.dto.query.response.UserMdmAuth;
import kr.or.komca.admin.usermanagement.dto.query.condition.UserMdmAuthSearchCondition;
import kr.or.komca.common.utils.core.dto.response.ListResponse;
import kr.or.komca.common.utils.core.dto.response.PageListResponse;

import java.util.List;

public interface UserManagementQueryService {

	/**
	 * 사용자 목록 페이징 조회
	 *
	 * @param condition 사용자 관리 검색 조건 DTO
	 * @return 페이징된 사용자 목록 응답
	 */
	PageListResponse<UserManagement> getUserList(UserManageSearchCondition condition);

	/**
	 * 사용자 엑셀 목록 페이징 조회
	 *
	 * @param condition 사용자 엑셀 검색 조건 DTO
	 * @return 페이징된 사용자 엑셀 목록 응답
	 */
	PageListResponse<UserExcel> getUserExcelList(UserExcelCondition condition);

	/**
	 * 사용자 존재 여부 검증
	 *
	 * @param userId 사용자 ID
	 */
	void validateUserExists(String userId);

	/**
	 * 사용자 ID로 사용자 정보 조회
	 *
	 * @param userId 사용자 ID
	 * @return 사용자 관리 정보
	 */
	UserManagement getUserById(String userId);

	/**
	 * 사용자 ID 존재 여부 확인
	 *
	 * @param userId 사용자 ID
	 * @return 사용자 존재 여부
	 */
	boolean isExistUser(String userId);

	/**
     * 사용자의 역할 목록 조회
     * 사용자 ID를 기반으로 해당 사용자에게 할당된 모든 역할 정보를 조회합니다.
     *
     * @param userId 사용자 ID
     * @return 사용자 역할 목록 응답
     */
    ListResponse<UserRole> getUserRoleList(String userId);

    /**
     * 사용자가 가진 역할에 따른 메뉴 권한 리스트 조회
     *
     * @param userId 사용자 ID
     * @return 메뉴 권한 목록 응답
     */
    PageListResponse<RoleMenu> getUserRoleMenuList(String userId, CommonPageSearchCondition condition);

	/**
	 * 사용자가 가진 임시 메뉴 권한 조회
	 *
	 * @param userId 사용자 ID
	 * @return 임시 메뉴 권한 목록 응답
	 */
	ListResponse<UserTempMenuAuth> getTempAuthByUserId(String userId);

	/**
	 * 사용자가 가진 매체 코드 권한 조회
	 *
	 * @param userId 사용자 ID
	 * @param condition 검색 조건 (페이징 포함)
	 * @return 매체 코드 권한 목록 응답 (페이징)
	 */
	PageListResponse<UserMdmAuth> getMdmAuthByUserIdWithPaging(String userId, UserMdmAuthSearchCondition condition);

	/**
	 * 사용자의 특정 메뉴 임시권한 존재 여부 검증
	 *
	 * @param userId 사용자 ID
	 * @param menuCdList 확인할 메뉴 코드 목록
	 */
	void validateUserTempAuthExists(String userId, List<String> menuCdList);

	/**
	 * 사용자의 특정 메뉴 임시권한 중복 여부 검증
	 *
	 * @param userId 사용자 ID
	 * @param menuCdList 확인할 메뉴 코드 목록
	 */
	void validateUserTempAuthDuplicate(String userId, List<String> menuCdList);

	/**
	 * 사용자의 특정 역할 존재 여부 검증
	 *
	 * @param userId 사용자 ID
	 * @param roleCdList 확인할 역할 코드 목록
	 */
	void validateUserRoleExists(String userId, List<String> roleCdList);

	/**
	 * 사용자의 특정 역할 중복 여부 검증
	 *
	 * @param userId 사용자 ID
	 * @param roleCdList 확인할 역할 코드 목록
	 */
	void validateUserRoleDuplicate(String userId, List<String> roleCdList);

	/**
	 * 사용자의 매체 코드 권한 중복 여부 검증
	 *
	 * @param userId 사용자 ID
	 * @param largeClassCd 대분류 코드
	 * @param aveClassCd 중분류 코드 (nullable)
	 * @param smallClassCd 소분류 코드 (nullable)
	 * @param mdmCd 매체 코드 (nullable)
	 * @param svcCd 서비스 코드 (nullable)
	 */
	void validateUserMdmAuthDuplicate(
			String userId,
			String largeClassCd,
			String aveClassCd,
			String smallClassCd,
			String mdmCd,
			String svcCd
	);
}