package kr.or.komca.admin.usermanagement.dto.command.request;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.Valid;
import kr.or.komca.admin.usermanagement.dto.command.request.sub.UpdateUserInfo;
import kr.or.komca.admin.usermanagement.dto.command.request.sub.UpdateUserRole;
import kr.or.komca.admin.usermanagement.dto.command.request.sub.UpdateUserTempAuth;
import kr.or.komca.admin.usermanagement.dto.command.request.sub.UpdateUserMdmAuth;
import lombok.*;

/**
 * 사용자 정보 업데이트 요청 DTO
 */
@AllArgsConstructor
@Getter
@Builder
@ToString
public class ManageUserRequest {

    /** 사용자 기본 정보 */
    @Schema(description = "사용자 정보")
    private @Valid UpdateUserInfo info;
    
    /** 사용자 역할 정보 (추가/삭제) */
    @Schema(description = "사용자 역할 정보 (추가/삭제 역할 목록)")
    private @Valid UpdateUserRole role;

    /** 사용자 임시 권한 정보 (추가/수정/삭제) */
    @Schema(description = "사용자 임시 권한")
    private @Valid UpdateUserTempAuth tempAuth;

    /** 사용자 매체 코드 권한 정보 (추가/수정) */
    @Schema(description = "사용자 매체 코드 권한")
    private @Valid UpdateUserMdmAuth mdmAuth;
}

