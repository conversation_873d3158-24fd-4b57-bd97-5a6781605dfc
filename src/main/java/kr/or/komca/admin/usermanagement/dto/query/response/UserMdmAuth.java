package kr.or.komca.admin.usermanagement.dto.query.response;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;

/**
 * 사용자 매체 코드 권한 정보 DTO
 */
@NoArgsConstructor
@AllArgsConstructor
@Getter
@Builder(toBuilder = true)
public class UserMdmAuth {

    /** 시퀀스 번호 */
    @Schema(description = "시퀀스 번호", example = "1")
    private Long seq;

    /** 대분류 코드 */
    @Schema(description = "대분류 코드", example = "A")
    private String largeClassCd;

    /** 중분류 코드 */
    @Schema(description = "중분류 코드", example = "AA")
    private String aveClassCd;

    /** 소분류 코드 */
    @Schema(description = "소분류 코드", example = "AA01")
    private String smallClassCd;

    /** 매체 코드 */
    @Schema(description = "매체 코드", example = "AA0101")
    private String mdmCd;

    /** 서비스 코드 */
    @Schema(description = "서비스 코드", example = "AA010101")
    private String svcCd;

    /** 사용 여부 */
    @Schema(description = "사용 여부", example = "Y")
    private String useYn;

    /** 등록자 ID */
    @Schema(description = "등록자 ID", example = "admin")
    private String inspersId;

    /** 등록일시 */
    @Schema(description = "등록일시", example = "2023-01-01 10:00:00")
    private String insDt;

    /** 수정자 ID */
    @Schema(description = "수정자 ID", example = "admin")
    private String modpersId;

    /** 수정일시 */
    @Schema(description = "수정일시", example = "2023-01-01 10:00:00")
    private String modDt;
}
