package kr.or.komca.admin.usermanagement.api;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import kr.or.komca.admin.common.dto.CommonPageSearchCondition;
import kr.or.komca.admin.role.dto.query.response.RoleMenu;
import kr.or.komca.admin.usermanagement.dto.command.request.ManageUserRequest;
import kr.or.komca.admin.usermanagement.dto.command.response.ManageUser;
import kr.or.komca.admin.usermanagement.dto.query.condition.UserExcelCondition;
import kr.or.komca.admin.usermanagement.dto.query.condition.UserManageSearchCondition;
import kr.or.komca.admin.usermanagement.dto.query.condition.UserMdmAuthSearchCondition;
import kr.or.komca.admin.usermanagement.dto.query.response.*;
import kr.or.komca.common.exception.response.error.CommonErrorResponse;
import kr.or.komca.common.exception.response.success.CommonSuccessResponse;
import kr.or.komca.common.utils.core.dto.response.ListResponse;
import kr.or.komca.common.utils.core.dto.response.PageListResponse;
import org.springframework.http.ResponseEntity;

@Tag(name = "UserManagement", description = "사용자 관리 API")
public interface UserManagementApi {

	@Operation(
			summary = "사용자 목록 조회",
			description = "검색 조건에 맞는 사용자 목록을 조회합니다."
	)
	@ApiResponses({
			@ApiResponse(
					responseCode = "200",
					description = "조회 성공"
			),
			@ApiResponse(
					responseCode = "400",
					description = "잘못된 요청",
					content = @Content(schema = @Schema(implementation = CommonErrorResponse.class))
			),
	})
	ResponseEntity<CommonSuccessResponse<PageListResponse<UserManagement>>> getUserList(
			@Parameter(description = "사용자 검색 조건", required = true)
			UserManageSearchCondition condition
	);

	@Operation(
			summary = "사용자 엑셀 조회",
			description = "엑셀 출력을 위해 검색 조건에 맞는 사용자 목록을 조회합니다."
	)
	@ApiResponses({
			@ApiResponse(
					responseCode = "200",
					description = "조회 성공"
			),
			@ApiResponse(
					responseCode = "400",
					description = "잘못된 요청",
					content = @Content(schema = @Schema(implementation = CommonErrorResponse.class))
			),
	})
	ResponseEntity<CommonSuccessResponse<PageListResponse<UserExcel>>> getUserExcelList(
			@Parameter(description = "사용자 검색 조건", required = true)
			UserExcelCondition condition
	);

	@Operation(
			summary = "사용자 정보 조회",
			description = "사용자 ID로 사용자 정보를 조회합니다."
	)
	@ApiResponses({
			@ApiResponse(
					responseCode = "200",
					description = "조회 성공"
			),
			@ApiResponse(
					responseCode = "400",
					description = "잘못된 요청",
					content = @Content(schema = @Schema(implementation = CommonErrorResponse.class))
			),
	})
	ResponseEntity<CommonSuccessResponse<UserManagement>> getUserById(
			@Parameter(description = "사용자 ID", required = true)
			String userId
	);

	@Operation(
			summary = "사용자 존재 여부 확인",
			description = "사용자 ID로 사용자의 존재 여부를 확인합니다."
	)
	@ApiResponses({
			@ApiResponse(
					responseCode = "200",
					description = "확인 성공"
			),
			@ApiResponse(
					responseCode = "400",
					description = "잘못된 요청",
					content = @Content(schema = @Schema(implementation = CommonErrorResponse.class))
			),
	})
	ResponseEntity<CommonSuccessResponse<Boolean>> validateExistUser(
			@Parameter(description = "사용자 ID", required = true)
			String userId
	);


	@Operation(
			summary = "사용자의 역할 리스트 조회",
			description = "사용자 ID로 사용자에게 할당된 역할 리스트를 조회합니다."
	)
	@ApiResponses({
			@ApiResponse(
					responseCode = "200",
					description = "확인 성공"
			),
			@ApiResponse(
					responseCode = "400",
					description = "잘못된 요청",
					content = @Content(schema = @Schema(implementation = CommonErrorResponse.class))
			),
	})
	ResponseEntity<CommonSuccessResponse<ListResponse<UserRole>>> getUserRoleList(
			@Parameter(description = "사용자 ID", required = true)
			String userId
	);

	@Operation(
			summary = "사용자의 메뉴 권한 리스트 조회",
			description = "사용자 ID로 사용자에게 할당된 메뉴 권한 리스트를 조회합니다. (중복 권한 통합 및 존재하지 않는 메뉴는 표시하지 않음)"
	)
	@ApiResponses({
			@ApiResponse(
					responseCode = "200",
					description = "확인 성공"
			),
			@ApiResponse(
					responseCode = "400",
					description = "잘못된 요청",
					content = @Content(schema = @Schema(implementation = CommonErrorResponse.class))
			),
	})
	ResponseEntity<CommonSuccessResponse<PageListResponse<RoleMenu>>> getUserRoleMenuList(
			@Parameter(description = "사용자 ID", required = true)
			String userId,
			@Parameter(description = "페이지 검색 조건", required = true)
			CommonPageSearchCondition condition
	);

	@Operation(
			summary = "사용자의 임시 메뉴 권한 리스트 조회",
			description = "사용자 ID로 사용자에게 할당된 임시 메뉴 권한 리스트를 조회합니다."
	)
	@ApiResponses({
			@ApiResponse(
					responseCode = "200",
					description = "확인 성공"
			),
			@ApiResponse(
					responseCode = "400",
					description = "잘못된 요청",
					content = @Content(schema = @Schema(implementation = CommonErrorResponse.class))
			),
	})
	ResponseEntity<CommonSuccessResponse<ListResponse<UserTempMenuAuth>>> getUserTempMenuAuthList(
			@Parameter(description = "사용자 ID", required = true)
			String userId
	);

	@Operation(
			summary = "사용자 매체 코드 권한 조회",
			description = "사용자의 매체 코드 권한 목록을 페이징과 검색 조건으로 조회합니다."
	)
	@ApiResponses({
			@ApiResponse(
					responseCode = "200",
					description = "조회 성공"
			),
			@ApiResponse(
					responseCode = "400",
					description = "잘못된 요청",
					content = @Content(schema = @Schema(implementation = CommonErrorResponse.class))
			),
	})
	ResponseEntity<CommonSuccessResponse<PageListResponse<UserMdmAuth>>> getUserMdmAuthList(
			@Parameter(description = "사용자 ID", required = true)
			String userId,
			@Parameter(description = "검색 조건 (페이징 포함)", required = true)
			UserMdmAuthSearchCondition condition
	);

	@Operation(
			summary = "사용자 정보 수정",
			description = "사용자 정보를 수정합니다."
	)
	@ApiResponses({
			@ApiResponse(
					responseCode = "200",
					description = "수정 성공"
			),
			@ApiResponse(
					responseCode = "400",
					description = "잘못된 요청",
					content = @Content(schema = @Schema(implementation = CommonErrorResponse.class))
			),
	})
	ResponseEntity<CommonSuccessResponse<ManageUser>> updateUser(
			@Parameter(description = "사용자 ID", required = true)
			String userId,
			@Parameter(description = "사용자 정보 수정 요청 데이터", required = true)
			ManageUserRequest command
	);

}