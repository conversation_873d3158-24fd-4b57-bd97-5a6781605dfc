package kr.or.komca.admin.usermanagement.service.command;

import kr.or.komca.admin.code.mdm.service.query.MdmCodeQueryService;
import kr.or.komca.admin.menu.service.query.MenuQueryService;
import kr.or.komca.admin.role.service.query.RoleQueryService;
import kr.or.komca.admin.usermanagement.dto.command.request.ManageUserRequest;
import kr.or.komca.admin.usermanagement.dto.command.request.sub.*;
import kr.or.komca.admin.usermanagement.dto.command.response.ManageUser;
import kr.or.komca.admin.usermanagement.mapper.command.UserManagementCommandMapper;
import kr.or.komca.admin.usermanagement.service.query.UserManagementQueryService;
import kr.or.komca.common.exception.core.CommonException;
import kr.or.komca.common.exception.response.error.code.CommonErrorCode;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Service
@Slf4j
@RequiredArgsConstructor
public class UserManagementCommandServiceImpl implements UserManagementCommandService {

	private final UserManagementQueryService userManagementQueryService;
	private final UserManagementCommandMapper userManagementCommandMapper;
	private final RoleQueryService roleQueryService;
	private final MenuQueryService menuQueryService;
	private final MdmCodeQueryService mdmCodeQueryService;

	@Transactional
	@Override
	public ManageUser updateUser(String userId, String editpersId, ManageUserRequest command) {
		log.info("사용자 정보 수정 시작 - userId: {}, editpersId: {}", userId, editpersId);

		// 각 작업 수행 결과
		int infoAffectedRows = 0;
		int addRoleAffectedRows = 0;
		int removeRoleAffectedRows = 0;
		int addTempAuthAffectedRows = 0;
		int updateTempAuthAffectedRows = 0;
		int removeTempAuthAffectedRows = 0;
		int addMdmAuthAffectedRows = 0;
		int updateMdmAuthAffectedRows = 0;

		// 유저 검증
		userManagementQueryService.validateUserExists(userId);

		// 사용자 정보 수정 객체
		UpdateUserInfo updateUserInfo = command.getInfo();

		// 유저 업데이트 수행
		if (updateUserInfo != null) {
			infoAffectedRows = updateUserInfo(userId, editpersId, updateUserInfo);
		}

		// 사용자 역할 수정 객체
		UpdateUserRole updateRoleCommand = command.getRole();

		// 사용자 역할 생성 및 삭제
		if (updateRoleCommand != null) {
			// 역할 생성
			addRoleAffectedRows = createUserRole(userId, editpersId, updateRoleCommand);
			// 역할 삭제
			removeRoleAffectedRows = deleteUserRole(userId, editpersId, updateRoleCommand);
		}

		// 사용자 임시 권한 수정 객체
		UpdateUserTempAuth tempAuth = command.getTempAuth();

		if (tempAuth != null) {
			addTempAuthAffectedRows = createTempAuth(userId, editpersId, tempAuth.getCreate());
			updateTempAuthAffectedRows = updateTempAuth(userId, editpersId, tempAuth.getUpdate());
			removeTempAuthAffectedRows = deleteTempAuth(userId, tempAuth.getDelete());
		}

		// 사용자 매체 코드 권한 수정 객체
		UpdateUserMdmAuth mdmAuth = command.getMdmAuth();

		if (mdmAuth != null) {
			addMdmAuthAffectedRows = createMdmAuth(userId, editpersId, mdmAuth.getCreate());
			updateMdmAuthAffectedRows = updateMdmAuth(userId, editpersId, mdmAuth.getUpdate());
		}

		log.info("사용자 정보 수정 완료 - userId: {}, 역할추가: {}건, 역할삭제: {}건, 임시권한추가: {}건, 임시권한수정: {}건, 임시권한삭제: {}건", 
				userId, addRoleAffectedRows, removeRoleAffectedRows, addTempAuthAffectedRows, updateTempAuthAffectedRows, removeTempAuthAffectedRows);

		// 응답 DTO 구성
		return ManageUser.builder()
				.userId(userId)
				.infoAffectedRows(infoAffectedRows)
				.createRoleAffectedRows(addRoleAffectedRows)
				.deleteRoleAffectedRows(removeRoleAffectedRows)
				.createTempAuthAffectedRows(addTempAuthAffectedRows)
				.updateTempAuthAffectedRows(updateTempAuthAffectedRows)
				.deleteTempAuthAffectedRows(removeTempAuthAffectedRows)
				.createMdmAuthAffectedRows(addMdmAuthAffectedRows)
				.updateMdmAuthAffectedRows(updateMdmAuthAffectedRows)
				.build();
	}

	private int updateUserInfo(String userId, String modpersId, UpdateUserInfo userInfo) {
		int result = userManagementCommandMapper.updateUser(userId, modpersId, userInfo);
		if (result <= 0) {
			throw new CommonException(
					CommonErrorCode.INTERNAL_SERVER_ERROR,
					"유저 업데이트 실패 - 변경된 유저 없음"
			);
		}
		return result;
	}

	private int createUserRole(String userId, String inspersId, UpdateUserRole updateRoleCommand) {
		if (updateRoleCommand.getCreate() != null && !updateRoleCommand.getCreate().isEmpty()) {
			log.info("사용자 역할 생성사항 존재 | roleIdList = {}", updateRoleCommand.getCreate());

			// 역할 존재 검증
			roleQueryService.validateRoleExists(updateRoleCommand.getCreate());

			// 역할 중복 검증
			userManagementQueryService.validateUserRoleDuplicate(userId, updateRoleCommand.getCreate());

			int result = userManagementCommandMapper.createUserRole(userId, inspersId, updateRoleCommand);

			if (result <= 0) {
				throw new CommonException(
						CommonErrorCode.INTERNAL_SERVER_ERROR,
						"유저 역할 생성 실패 - 생성된 유저 역할 없음"
				);
			}
			return result;
		}
		return 0;
	}

	private int deleteUserRole(String userId, String modpersId, UpdateUserRole updateRoleCommand) {
		if (updateRoleCommand.getDelete() != null && !updateRoleCommand.getDelete().isEmpty()) {
			log.info("사용자 역할 삭제사항 존재 | roleIdList = {}", updateRoleCommand.getDelete());

			// 사용자에게 할당된 역할인지 검증
			userManagementQueryService.validateUserRoleExists(userId, updateRoleCommand.getDelete());

			int result = userManagementCommandMapper.deleteUserRole(userId, modpersId, updateRoleCommand);

			if (result <= 0) {
				throw new CommonException(
						CommonErrorCode.INTERNAL_SERVER_ERROR,
						"유저 역할 삭제 실패 - 삭제된 유저 역할 없음"
				);
			}
			return result;
		}
		return 0;
	}

	private int createTempAuth(String userId, String inspersId, List<CreateTempAuth> createTempAuthList) {
		if (createTempAuthList != null && !createTempAuthList.isEmpty()) {
			log.info("임시 권한 생성사항 존재 | tempAuthList = {}", createTempAuthList);

			List<String> menuCdList = createTempAuthList.stream()
					.map(CreateTempAuth::getMenuCd)
					.toList();

			// 메뉴 존재 검증
			menuQueryService.validateMenuExists(menuCdList);

			// 이미 사용자에게 할당된 권한인지 검증
			userManagementQueryService.validateUserTempAuthDuplicate(userId, menuCdList);

			int result = userManagementCommandMapper.createUserTempAuth(userId, inspersId, createTempAuthList);

			if (result <= 0) {
				throw new CommonException(
						CommonErrorCode.INTERNAL_SERVER_ERROR,
						"유저 임시 권한 생성 실패 - 생성된 임시 권한 없음"
				);
			}
			return result;
		}
		return 0;
	}

	private int updateTempAuth(String userId, String modpersId, List<UpdateTempAuth> updateTempAuthList) {
		if (updateTempAuthList != null && !updateTempAuthList.isEmpty()) {
			log.info("임시 권한 수정사항 존재 | tempAuthList = {}", updateTempAuthList);

			// 수정 대상 메뉴 코드 리스트 추출
			List<String> menuCdList = updateTempAuthList.stream()
					.map(UpdateTempAuth::getMenuCd)
					.toList();

			// 존재하는 권한인지 검증
			userManagementQueryService.validateUserTempAuthExists(userId, menuCdList);

			int result = userManagementCommandMapper.updateUserTempAuth(userId, modpersId, updateTempAuthList);

			if (result <= 0) {
				throw new CommonException(
						CommonErrorCode.INTERNAL_SERVER_ERROR,
						"유저 임시 권한 수정 실패 - 수정된 임시 권한 없음"
				);
			}
			return result;
		}
		return 0;
	}

	private int deleteTempAuth(String userId, List<String> deleteTempAuthList) {
		if (deleteTempAuthList != null && !deleteTempAuthList.isEmpty()) {
			log.info("임시 권한 삭제사항 존재 | tempAuthList = {}", deleteTempAuthList);

			// 삭제하려는 임시권한이 존재하는지 검증
			userManagementQueryService.validateUserTempAuthExists(userId, deleteTempAuthList);

			int result = userManagementCommandMapper.deleteUserTempAuth(userId, deleteTempAuthList);

			if (result <= 0) {
				throw new CommonException(
						CommonErrorCode.INTERNAL_SERVER_ERROR,
						"유저 임시 권한 삭제 실패 - 삭제된 임시 권한 없음"
				);
			}
			return result;
		}
		return 0;
	}

	private int createMdmAuth(String userId, String inspersId, List<CreateMdmAuth> createMdmAuthList) {
		if (createMdmAuthList != null && !createMdmAuthList.isEmpty()) {
			log.info("매체 코드 권한 생성사항 존재 | mdmAuthList = {}", createMdmAuthList);

			// 매체 코드 권한 검증
			for (CreateMdmAuth mdmAuth : createMdmAuthList) {
				// 매체코드 계층 무결성 검증 (null 값 이후 모든 필드가 null인지)
				mdmCodeQueryService.validateMdmCodeSequentialHierarchy(
						mdmAuth.getLargeClassCd(),
						mdmAuth.getAveClassCd(),
						mdmAuth.getSmallClassCd(),
						mdmAuth.getMdmCd(),
						mdmAuth.getSvcCd()
				);

				// 매체코드 계층 유효성 여부 검증
				mdmCodeQueryService.validateMdmCodeHierarchy(
						mdmAuth.getLargeClassCd(),
						mdmAuth.getAveClassCd(),
						mdmAuth.getSmallClassCd(),
						mdmAuth.getMdmCd(),
						mdmAuth.getSvcCd()
				);

				// 매체 코드 권한 중복 검증
				userManagementQueryService.validateUserMdmAuthDuplicate(
						userId,
						mdmAuth.getLargeClassCd(),
						mdmAuth.getAveClassCd(),
						mdmAuth.getSmallClassCd(),
						mdmAuth.getMdmCd(),
						mdmAuth.getSvcCd()
				);
			}

			int result = 0;

			for (CreateMdmAuth createMdmAuth : createMdmAuthList) {
				result += userManagementCommandMapper.createUserMdmAuth(userId, inspersId, createMdmAuth);
			}

			if (result <= 0) {
				throw new CommonException(
						CommonErrorCode.INTERNAL_SERVER_ERROR,
						"유저 매체 코드 권한 생성 실패 - 생성된 매체 코드 권한 없음"
				);
			}
			return result;
		}
		return 0;
	}

	private int updateMdmAuth(String userId, String modpersId, List<UpdateMdmAuth> updateMdmAuthList) {
		if (updateMdmAuthList != null && !updateMdmAuthList.isEmpty()) {
			log.info("매체 코드 권한 수정사항 존재 | mdmAuthList = {}", updateMdmAuthList);

			// 매체 코드 권한 검증
			for (UpdateMdmAuth mdmAuth : updateMdmAuthList) {
				// 매체코드 계층 무결성 검증 (null 값 이후 모든 필드가 null인지)
				mdmCodeQueryService.validateMdmCodeSequentialHierarchy(
						mdmAuth.getLargeClassCd(),
						mdmAuth.getAveClassCd(),
						mdmAuth.getSmallClassCd(),
						mdmAuth.getMdmCd(),
						mdmAuth.getSvcCd()
				);

				// 매체코드 존재 여부 및 계층 구조 검증
				mdmCodeQueryService.validateMdmCodeHierarchy(
						mdmAuth.getLargeClassCd(),
						mdmAuth.getAveClassCd(),
						mdmAuth.getSmallClassCd(),
						mdmAuth.getMdmCd(),
						mdmAuth.getSvcCd()
				);
			}

			int result = userManagementCommandMapper.updateUserMdmAuth(userId, modpersId, updateMdmAuthList);

			if (result <= 0) {
				throw new CommonException(
						CommonErrorCode.INTERNAL_SERVER_ERROR,
						"유저 매체 코드 권한 수정 실패 - 수정된 매체 코드 권한 없음"
				);
			}
			return result;
		}
		return 0;
	}

}
