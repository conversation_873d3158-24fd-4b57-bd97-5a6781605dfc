package kr.or.komca.admin.usermanagement.dto.command.request.sub;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.ToString;

@Getter
@Builder
@ToString
@AllArgsConstructor
public class UpdateTempAuth {

	/** 메뉴 코드 */
	@Schema(description = "메뉴 코드", example = "00001")
	private String menuCd;

	/** 조회 권한 */
	@Schema(description = "조회 권한", example = "Y")
	private String authSelect;

	/** 편집 권한 */
	@Schema(description = "편집 권한", example = "Y")
	private String authEdit;

	/** 다운로드 권한 */
	@Schema(description = "다운로드 권한", example = "Y")
	private String authDown;

	/** 마스킹 권한 */
	@Schema(description = "마스킹 권한", example = "N")
	private String authMask;

	/** 권한 시작일 */
	@Schema(description = "권한 시작일", example = "2023-01-01")
	private String authStartDay;

	/** 권한 종료일 */
	@Schema(description = "권한 종료일", example = "2023-12-31")
	private String authEndDay;
}
