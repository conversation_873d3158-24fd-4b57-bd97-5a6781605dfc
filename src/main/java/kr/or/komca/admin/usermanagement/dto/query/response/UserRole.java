package kr.or.komca.admin.usermanagement.dto.query.response;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;

/**
 * 유저가 가진 역할 응답 DTO
 */
@NoArgsConstructor
@AllArgsConstructor
@Getter
@Builder(toBuilder = true)
public class UserRole {

	/** 권한 코드 */
	@Schema(description = "권한 코드", example = "00001")
	private String roleCd;

	/** 역할명 */
	@Schema(description = "역할명", example = "기초권한")
	private String roleNm;

	/** 비고 */
	@Schema(description = "비고", example = "신규 입사자 세팅")
	private String remak;

	/** 사용 여부 */
	@Schema(description = "사용 여부", example = "Y")
	private String useYn;
}