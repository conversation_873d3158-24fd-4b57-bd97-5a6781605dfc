package kr.or.komca.admin.usermanagement.dto.query.response;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * User와 Equipment를 한번에 응답하는 DTO 객체
 */
@NoArgsConstructor
@AllArgsConstructor
@Getter
@Builder(toBuilder = true)
public class UserManagement {
	/** 사용자 ID */
	@Schema(description = "사용자 ID", example = "K123456")
	private String userId;

	/** 사용자 이름 */
	@Schema(description = "사용자 이름", example = "사용자")
	private String userNm;
	
	/** 사용자 구분 */
	@Schema(description = "사용자 구분", example = "01")
	private String userGbn;
	
	/** 사용 여부 */
	@Schema(description = "사용 여부", example = "Y")
	private String useYn;
	
	/** 직원 번호 */
	@Schema(description = "직원 번호", example = "K123456")
	private String staffNo;
	
	/** 단위 */
	@Schema(description = "회계 단위", example = "01")
	private String acctnUnit;
	
	/** IP 주소 */
	@Schema(description = "IP 주소", example = "***********")
	private String ip;
	
	/** IPPBX 사용자 ID */
	@Schema(description = "IPPBX 사용자 ID", example = "07077843804")
	private String ippbxUserId;
	
	/** IP PHONE 내선번호 */
	@Schema(description = "IP PHONE 내선번호", example = "804")
	private String ippbxInnerTel;
	
	/** 수정일 */
	@Schema(description = "수정일", example = "2009-11-19T08:55:01")
	private LocalDateTime modDt;
	
	/** 수정자 */
	@Schema(description = "수정자", example = "admin")
	private String modpersId;
}