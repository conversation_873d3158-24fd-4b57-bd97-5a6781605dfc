package kr.or.komca.admin.usermanagement.dto.query.condition;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.Pattern;
import kr.or.komca.admin.usermanagement.util.UserManagementSortColumnValidator;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.ToString;

/**
 * 사용자 관리 검색 조건 DTO
 */
@ToString
@Getter
@AllArgsConstructor
@Builder(toBuilder = true)
public class UserManageSearchCondition {
	/** 사용자 구분 [common - 00389] [00001(사무기기 제외)] */
	@Schema(description = "사용자 구분", example = "01")
	private String userGbn;

	/** 사용자 이름 */
	@Schema(description = "사용자 이름", example = "사용자")
	private String userNm;

	/** 사용자 ID */
	@Schema(description = "사용자 ID", example = "K123456")
	private String userId;

	/** 사원 번호 */
	@Schema(description = "사원 번호", example = "K123456")
	private String staffNo;

	/** 사용 여부 */
	@Pattern(regexp = "^(Y|N|)$")
	@Schema(description = "사용 여부", example = "Y")
	private String useYn;

	/** 페이지 번호 */
	@Schema(description = "페이지 번호", example = "1")
	private int page;

	/** 페이지 크기 */
	@Schema(description = "페이지 크기", example = "10")
	private int pageSize;

	/** 정렬할 컬럼 **/
	@Schema(description = "정렬할 컬럼", example = "userNm")
	private String sortColumn;

	/** 정렬 순서 1: asc. 2: desc  */
	@Schema(description = "정렬 순서 (1: 오름차순, 2: 내림차순)", example = "1")
	private String sortOrder;

	public String getSortColumn() {
		return UserManagementSortColumnValidator.validate(this.sortColumn);
	}

	public String getSortOrder() {
		if ("1".equals(this.sortOrder)) return "ASC";
		if ("2".equals(this.sortOrder)) return "DESC";
		return "";
	}
}