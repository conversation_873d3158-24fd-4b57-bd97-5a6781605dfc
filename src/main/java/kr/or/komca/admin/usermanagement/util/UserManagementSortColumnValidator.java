package kr.or.komca.admin.usermanagement.util;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

/**
 * Sort Validator
 */
@Slf4j
@Component
public class UserManagementSortColumnValidator {

    /**
     * 허용된 정렬 컬럼 매핑 정보
     * Key: 프론트엔드 요청 파라미터명
     * Value: 실제 DB 테이블의 컬럼명
     */
    private static final Map<String, String> ALLOWED_SORT_COLUMNS_MAP = new HashMap<>() {
        {
            put("userId", "user_id");           // 사용자/장비 ID
            put("roleCd", "role_cd");           // 권한 코드
            put("userNm", "user_nm");           // 사용자/장비 이름
            put("userGbn", "user_gbn");         // 사용자 구분
            put("useYn", "use_yn");             // 사용 여부
            put("staffNo", "staff_no");         // 직원 번호
            put("acctnUnit", "acctn_unit");     // 계정 단위
            put("ip", "ip");                    // IP 주소
            put("ippbxUserId", "ippbx_user_id"); // IPPBX 사용자 ID
            put("ippbxInnerTel", "ippbx_inner_tel"); // IPPBX 전화번호
            put("modDt", "mod_dt");             // 수정일
            put("modpersId", "modpers_id");     // 수정자
        }
    };

    /**
     * 정렬 컬럼 유효성 검사 및 매핑
     */
    public static String validate(String sortColumn) {
        return ALLOWED_SORT_COLUMNS_MAP.get(sortColumn);
    }
}