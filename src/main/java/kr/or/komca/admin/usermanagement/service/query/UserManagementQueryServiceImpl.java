package kr.or.komca.admin.usermanagement.service.query;

import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import kr.or.komca.admin.common.dto.CommonPageSearchCondition;
import kr.or.komca.admin.menu.dto.query.response.MenuPath;
import kr.or.komca.admin.menu.service.query.MenuQueryService;
import kr.or.komca.admin.role.dto.query.condition.RoleListMenuSearchCondition;
import kr.or.komca.admin.role.dto.query.response.RoleMenu;
import kr.or.komca.admin.role.service.query.RoleQueryService;
import kr.or.komca.admin.usermanagement.dto.query.condition.UserExcelCondition;
import kr.or.komca.admin.usermanagement.dto.query.condition.UserManageSearchCondition;
import kr.or.komca.admin.usermanagement.dto.query.condition.UserMdmAuthSearchCondition;
import kr.or.komca.admin.usermanagement.dto.query.response.*;
import kr.or.komca.admin.usermanagement.enums.errorcode.UserManagementErrorCode;
import kr.or.komca.admin.usermanagement.mapper.query.UserExcelMapper;
import kr.or.komca.admin.usermanagement.mapper.query.UserManagementQueryMapper;
import kr.or.komca.common.exception.core.CommonException;
import kr.or.komca.common.exception.response.error.code.CommonErrorCode;
import kr.or.komca.common.utils.core.dto.response.ListResponse;
import kr.or.komca.common.utils.core.dto.response.PageListResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;

@Slf4j
@Service
@RequiredArgsConstructor
@Transactional(readOnly = true)
public class UserManagementQueryServiceImpl implements UserManagementQueryService {

	private final RoleQueryService roleQueryService;
	private final MenuQueryService menuQueryService;
	private final UserManagementQueryMapper userManagementQueryMapper;
	private final UserExcelMapper userExcelMapper;

	@Override
	public PageListResponse<UserManagement> getUserList(UserManageSearchCondition condition) {
		log.info("사용자 목록 조회 요청 - page: {}, pageSize: {}", condition.getPage(), condition.getPageSize());
		
		try (Page<UserManagement> page = PageHelper.startPage(condition.getPage(), condition.getPageSize())) {
			List<UserManagement> userList = userManagementQueryMapper.getUserList(condition);

			PageInfo<UserManagement> pageInfo = new PageInfo<>(userList);

			PageListResponse<UserManagement> result = PageListResponse.<UserManagement>builder()
					.contents(pageInfo.getList())
					.totalElements(pageInfo.getTotal())
					.page(pageInfo.getPageNum())
					.totalPages(pageInfo.getPages())
					.pageSize(pageInfo.getPageSize())
					.build();
					
			return result;
		} catch (Exception e) {
			log.error("사용자 목록 조회 실패 - 페이징 처리 오류 발생", e);
			throw new CommonException(CommonErrorCode.INTERNAL_SERVER_ERROR, "페이징 처리 오류 발생");
		}
	}

	@Override
	public PageListResponse<UserExcel> getUserExcelList(UserExcelCondition condition) {
		log.info("사용자 엑셀 데이터 조회 요청 - page: {}, pageSize: {}", condition.getPage(), condition.getPageSize());
		try (Page<UserExcel> page = PageHelper.startPage(condition.getPage(), condition.getPageSize())) {
			List<UserExcel> userList = userExcelMapper.getUserExcelList(condition);

			PageInfo<UserExcel> pageInfo = new PageInfo<>(userList);

			return PageListResponse.<UserExcel>builder()
					.contents(pageInfo.getList())
					.totalElements(pageInfo.getTotal())
					.page(pageInfo.getPageNum())
					.totalPages(pageInfo.getPages())
					.pageSize(pageInfo.getPageSize())
					.build();
		} catch (Exception e) {
			throw new CommonException(CommonErrorCode.INTERNAL_SERVER_ERROR, "페이징 처리 오류 발생");
		}
	}

	@Override
	@Transactional(readOnly = true)
	public void validateUserExists(String userId) {
		boolean exist = userManagementQueryMapper.isExistUser(userId);

		// 존재하지 않는다면
		if (!exist) {
			throw new CommonException(UserManagementErrorCode.USER_NOT_FOUND,
					String.format("존재하지 않는 UserId 입니다. : %s", userId)
			);
		}
	}

	@Override
	public boolean isExistUser(String userId) {
		return userManagementQueryMapper.isExistUser(userId);
	}

	@Override
	public UserManagement getUserById(String userId) {
		UserManagement user = userManagementQueryMapper.getUserById(userId);

		// 유효성 검사
		if (user == null) {
			throw new CommonException(UserManagementErrorCode.USER_NOT_FOUND,
					String.format("존재하지 않는 UserId 입니다. : %s", userId)
			);
		}

		return user;
	}

	@Override
    public ListResponse<UserRole> getUserRoleList(String userId) {
        // 유저 아이디 검증
        validateUserExists(userId);

        try (Page<UserRole> page = PageHelper.startPage(1, Integer.MAX_VALUE, true)) {
            // 사용자 역할 목록 조회
            List<UserRole> roleListByUserId = userManagementQueryMapper.getRoleListByUserId(userId);

            if (roleListByUserId == null || roleListByUserId.isEmpty()) {
                log.warn("해당 사용자에게 할당된 역할이 없습니다. userId = {}", userId);
            }

            return ListResponse.<UserRole>builder()
                    .contents(roleListByUserId)
                    .totalElements(page.getTotal())
                    .build();
        }
    }

    @Override
    public PageListResponse<RoleMenu> getUserRoleMenuList(String userId, CommonPageSearchCondition condition) {
	    // 유저 아이디 검증
        validateUserExists(userId);

        // 사용자의 역할 코드 목록 조회
        List<String> userRoleList = userManagementQueryMapper.getRoleCdListByUserId(userId);

        if (userRoleList == null || userRoleList.isEmpty()) {
            log.warn("사용자에게 할당된 역할이 없습니다. userId={}", userId);
            return PageListResponse.empty(condition.getPage(), condition.getPageSize());
        }

		// 입력 조건으로 RoleList 검색 조건 생성
	    RoleListMenuSearchCondition roleListSearchCondition = RoleListMenuSearchCondition.builder()
			    .roleCdList(userRoleList)
			    .page(condition.getPage())
			    .pageSize(condition.getPageSize())
			    .build();

	    // 역할에 대한 메뉴 권한 조회는 Role 도메인으로 위임
        return roleQueryService.getRoleMenuListByRoleCdList(roleListSearchCondition);
    }

	@Override
	public ListResponse<UserTempMenuAuth> getTempAuthByUserId(String userId) {
		validateUserExists(userId);

		try (Page<UserRole> page = PageHelper.startPage(1, Integer.MAX_VALUE, true)) {

			List<UserTempMenuAuth> tempRoleByUserId = userManagementQueryMapper.getTempAuthByUserId(userId);

			// 메뉴 경로 정보 매핑
			List<UserTempMenuAuth> tempAuthWithPath = mapTempAuthPath(tempRoleByUserId);

			return ListResponse.<UserTempMenuAuth>builder()
					.contents(tempAuthWithPath)
					.totalElements(page.getTotal())
					.build();
		}
	}

	@Override
	public PageListResponse<UserMdmAuth> getMdmAuthByUserIdWithPaging(String userId, UserMdmAuthSearchCondition condition) {
		validateUserExists(userId);

		try (Page<UserMdmAuth> page = PageHelper.startPage(condition.getPage(), condition.getPageSize())) {

			List<UserMdmAuth> mdmAuthByUserId = userManagementQueryMapper.getMdmAuthByUserId(userId, condition);

			PageInfo<UserMdmAuth> pageInfo = new PageInfo<>(mdmAuthByUserId);

			return PageListResponse.<UserMdmAuth>builder()
					.contents(pageInfo.getList())
					.totalElements(pageInfo.getTotal())
					.page(pageInfo.getPageNum())
					.totalPages(pageInfo.getPages())
					.pageSize(pageInfo.getPageSize())
					.build();
		} catch (Exception e) {
			throw new CommonException(CommonErrorCode.INTERNAL_SERVER_ERROR, "매체 코드 권한 목록 조회 중 페이징 처리 오류 발생");
		}
	}

	/**
	 * MenuPath 메소드 호출 및 임시 권한 조회 결과에 매핑
	 *
	 * @param targetTempAuthList 매핑 할 리스트
	 * @return 매핑된 임시 권한 리스트
	 */
	private List<UserTempMenuAuth> mapTempAuthPath(List<UserTempMenuAuth> targetTempAuthList) {
		List<UserTempMenuAuth> result = new ArrayList<>();

		// 목록이 비어있다면 바로 반환
		if (targetTempAuthList.isEmpty()) return result;

		// 임시 권한 목록 순회
		for (UserTempMenuAuth tempAuth : targetTempAuthList) {

			boolean existMenu = menuQueryService.isExistMenu(tempAuth.getMenuCd());

			// 메뉴가 존재하지 않는다면 경고 로그 출력
			if (!existMenu) {
				log.warn("존재하지 않는 메뉴의 임시 권한이 부여되어있습니다. {}", tempAuth.getMenuCd());
				result.add(tempAuth);

			} else {

				// 메뉴 경로 조회
				MenuPath menuPathById = menuQueryService.getMenuPathById(tempAuth.getMenuCd());

				// 임시 권한에 메뉴 경로 매핑
				result.add(
						tempAuth.toBuilder()
								.path(menuPathById.getPath())
								.build()
				);
			}
		}

		return result;
	}

	@Override
	@Transactional(readOnly = true)
	public void validateUserTempAuthExists(String userId, List<String> menuCdList) {
		if (menuCdList == null || menuCdList.isEmpty()) {
			return;
		}

		List<String> invalidMenuCdList = userManagementQueryMapper.getInvalidUserTempAuthList(userId, menuCdList);

		if (invalidMenuCdList != null && !invalidMenuCdList.isEmpty()) {
			throw new CommonException(
					UserManagementErrorCode.USER_TEMP_AUTH_NOT_FOUND,
					String.format("사용자에게 존재하지 않는 임시권한입니다. userId=%s, menuCdList=%s", userId, invalidMenuCdList)
			);
		}
	}

	@Override
	@Transactional(readOnly = true)
	public void validateUserTempAuthDuplicate(String userId, List<String> menuCdList) {
		if (menuCdList == null || menuCdList.isEmpty()) {
			return;
		}

		List<String> existingMenuCdList = userManagementQueryMapper.getExistingUserTempAuthList(userId, menuCdList);

		if (existingMenuCdList != null && !existingMenuCdList.isEmpty()) {
			throw new CommonException(
					UserManagementErrorCode.USER_TEMP_AUTH_ALREADY_EXISTS,
					String.format("사용자에게 이미 존재하는 임시권한입니다. userId=%s, menuCdList=%s", userId, existingMenuCdList)
			);
		}
	}

	@Override
	@Transactional(readOnly = true)
	public void validateUserRoleExists(String userId, List<String> roleCdList) {
		if (roleCdList == null || roleCdList.isEmpty()) {
			return;
		}

		List<String> invalidRoleCdList = userManagementQueryMapper.getInvalidUserRoleList(userId, roleCdList);

		if (invalidRoleCdList != null && !invalidRoleCdList.isEmpty()) {
			throw new CommonException(
					UserManagementErrorCode.USER_ROLE_NOT_FOUND,
					String.format("사용자에게 존재하지 않는 역할입니다. userId=%s, roleCdList=%s", userId, invalidRoleCdList)
			);
		}
	}

	@Override
	@Transactional(readOnly = true)
	public void validateUserRoleDuplicate(String userId, List<String> roleCdList) {
		if (roleCdList == null || roleCdList.isEmpty()) {
			return;
		}

		List<String> existingRoleCdList = userManagementQueryMapper.getExistingUserRoleList(userId, roleCdList);

		if (existingRoleCdList != null && !existingRoleCdList.isEmpty()) {
			throw new CommonException(
					UserManagementErrorCode.USER_ROLE_ALREADY_EXISTS,
					String.format("사용자에게 이미 존재하는 역할입니다. userId=%s, roleCdList=%s", userId, existingRoleCdList)
			);
		}
	}

	@Override
	@Transactional(readOnly = true)
	public void validateUserMdmAuthDuplicate(
			String userId,
			String largeClassCd,
			String aveClassCd,
			String smallClassCd,
			String mdmCd,
			String svcCd
	) {
		boolean exists = userManagementQueryMapper.existsUserMdmAuth(userId, largeClassCd, aveClassCd, smallClassCd, mdmCd, svcCd);

		if (exists) {
			throw new CommonException(
					UserManagementErrorCode.USER_MDM_AUTH_ALREADY_EXISTS,
					String.format("사용자에게 이미 존재하는 매체 코드 권한입니다. userId=%s, largeClassCd=%s, aveClassCd=%s, smallClassCd=%s, mdmCd=%s, svcCd=%s",
							userId, largeClassCd, aveClassCd, smallClassCd, mdmCd, svcCd)
			);
		}
	}
}
