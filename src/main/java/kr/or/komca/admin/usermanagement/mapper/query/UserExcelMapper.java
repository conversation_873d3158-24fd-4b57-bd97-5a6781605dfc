package kr.or.komca.admin.usermanagement.mapper.query;

import kr.or.komca.admin.usermanagement.dto.query.condition.UserExcelCondition;
import kr.or.komca.admin.usermanagement.dto.query.response.UserExcel;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface UserExcelMapper {

	/**
	 * 사용자 엑셀 다운로드를 위한 조회
	 *
	 * @param condition 검색 조건 DTO
	 * @return 사용자 목록
	 */
	List<UserExcel> getUserExcelList(@Param("condition") UserExcelCondition condition);
}
