package kr.or.komca.admin.usermanagement.service.command;

import kr.or.komca.admin.usermanagement.dto.command.request.ManageUserRequest;
import kr.or.komca.admin.usermanagement.dto.command.response.ManageUser;

/**
 * 사용자 관리 명령 서비스 인터페이스
 */
public interface UserManagementCommandService {

	/**
	 * 사용자 정보 및 역할 업데이트
	 *
	 * @param userId 수정할 사용자 ID
	 * @param modpersId 수정자 ID
	 * @param command 사용자 수정 요청 데이터 (정보 및 역할)
	 * @return 업데이트 결과 응답 객체
	 */
	ManageUser updateUser(String userId, String modpersId, ManageUserRequest command);
}
