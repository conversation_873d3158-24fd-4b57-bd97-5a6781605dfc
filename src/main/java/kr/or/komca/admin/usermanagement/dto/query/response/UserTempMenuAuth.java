package kr.or.komca.admin.usermanagement.dto.query.response;

import io.swagger.v3.oas.annotations.media.Schema;
import kr.or.komca.admin.menu.dto.query.response.MenuPathItem;
import lombok.*;

import java.util.List;

/**
 * 사용자 임시 권한 정보 DTO
 */
@Builder(toBuilder = true)
@AllArgsConstructor
@NoArgsConstructor
@ToString
@Getter
@Schema(description = "사용자 임시 권한 정보")
public class UserTempMenuAuth {

    /** 메뉴 코드 */
    @Schema(description = "메뉴 코드", example = "00001")
    private String menuCd;

    /** 메뉴명 */
    @Schema(description = "메뉴명", example = "메뉴 관리")
    private String menuNm;

    /** 조회 권한 */
    @Schema(description = "조회 권한", example = "Y", allowableValues = {"Y", "N"})
    private String authSelect;

    /** 다운로드 권한 */
    @Schema(description = "다운로드 권한", example = "Y", allowableValues = {"Y", "N"})
    private String authDown;

    /** 수정 권한 */
    @Schema(description = "수정 권한", example = "Y", allowableValues = {"Y", "N"})
    private String authEdit;

    /** 마스킹 여부 */
    @Schema(description = "마스킹 여부", example = "N", allowableValues = {"Y", "N"})
    private String authMask;

    /** 권한 시작일 */
    @Schema(description = "권한 시작일", example = "2023-01-01 1:00:00")
    private String authStartDay;

    /** 권한 종료일 */
    @Schema(description = "권한 종료일", example = "2023-12-31 13:00:00")
    private String authEndDay;

    /** 메뉴 경로 */
    @Schema(description = "메뉴 경로")
    private List<MenuPathItem> path;
}
