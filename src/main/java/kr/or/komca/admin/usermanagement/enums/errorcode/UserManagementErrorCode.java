package kr.or.komca.admin.usermanagement.enums.errorcode;

import io.swagger.v3.oas.annotations.media.Schema;
import kr.or.komca.common.interfaces.response.code.ErrorCode;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpStatus;

/**
 * 사용자 관리에 대한 ErrorEnum 처리
 * 1. 상수명: 대문자 스네이크 케이스로 작성 (예: USER_ROLE_NOT_FOUND)
 * 2. code: 상수명과 동일하게 문자열로 작성
 * 3. status: 적절한 HTTP 상태 코드 설정 (HttpStatus enum 사용)
 */
@Getter
@RequiredArgsConstructor
public enum UserManagementErrorCode implements ErrorCode {

    /**
     * 사용자 기본 정보 관련 에러
     */
    @Schema(description = "중복된 USER Id")
    DUPLICATED_USER_ID("DUPLICATED_USER_ID", HttpStatus.BAD_REQUEST),

    /**
     * 사용자 역할 관련 에러
     * 발생 상황:
     * - 사용자에게 존재하지 않는 역할을 삭제하려 할 때
     * - 사용자에게 이미 존재하는 역할을 추가하려 할 때
     */
    @Schema(description = "사용자에게 존재하지 않는 역할")
    USER_ROLE_NOT_FOUND("USER_ROLE_NOT_FOUND", HttpStatus.NOT_FOUND),

    @Schema(description = "사용자에게 이미 존재하는 역할")
    USER_ROLE_ALREADY_EXISTS("USER_ROLE_ALREADY_EXISTS", HttpStatus.CONFLICT),

    /**
     * 사용자 임시권한 관련 에러
     * 발생 상황:
     * - 사용자에게 존재하지 않는 임시권한을 수정/삭제하려 할 때
     * - 사용자에게 이미 존재하는 임시권한을 추가하려 할 때
     */
    @Schema(description = "사용자에게 존재하지 않는 임시권한")
    USER_TEMP_AUTH_NOT_FOUND("USER_TEMP_AUTH_NOT_FOUND", HttpStatus.NOT_FOUND),

    @Schema(description = "사용자에게 이미 존재하는 임시권한")
    USER_TEMP_AUTH_ALREADY_EXISTS("USER_TEMP_AUTH_ALREADY_EXISTS", HttpStatus.CONFLICT),

    /**
     * 사용자 매체 코드 권한 관련 에러
     * 발생 상황:
     * - 사용자에게 이미 동일한 매체 코드 권한을 추가하려 할 때
     * - 매체 코드 계층 구조가 올바르지 않을 때 (null 값 이후 non-null 값)
     */
    @Schema(description = "사용자에게 이미 존재하는 매체 코드 권한")
    USER_MDM_AUTH_ALREADY_EXISTS("USER_MDM_AUTH_ALREADY_EXISTS", HttpStatus.CONFLICT),

    @Schema(description = "매체 코드 계층 구조 무결성 위반")
    INVALID_MDM_AUTH_HIERARCHY("INVALID_MDM_AUTH_HIERARCHY", HttpStatus.BAD_REQUEST),

    /**
     * 사용자 관련 특정 에러
     * 발생 상황:
     * - 사용자를 찾을 수 없을 때
     */
    @Schema(description = "사용자를 찾을 수 없음")
    USER_NOT_FOUND("USER_NOT_FOUND", HttpStatus.NOT_FOUND);

    private final String code;
    private final HttpStatus status;
}
