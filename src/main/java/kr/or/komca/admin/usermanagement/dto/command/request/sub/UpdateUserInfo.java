package kr.or.komca.admin.usermanagement.dto.command.request.sub;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.ToString;

/**
 * 사용자 정보 업데이트 세부 요청 DTO
 */
@AllArgsConstructor
@Getter
@Builder
@ToString
public class UpdateUserInfo {
	/** 사용자 이름 */
	@Schema(description = "사용자 이름", example = "사용자")
	private String userNm;

	/** 사용자 구분 [common - 00389] [00001(사무기기 제외)] */
	@Schema(description = "사용자 구분", example = "01")
	private String userGbn;

	/** 사용 여부 */
	@Schema(description = "사용 여부", example = "Y")
	private String useYn;

	/** 회계단위 [common - 00114] */
	@Schema(description = "회계단위", example = "01")
	private String acctnUnit;

	/** IP 주소 */
	@Schema(description = "IP 주소", example = "*************")
	private String ip;

	/** IP PHONE ID */
	@Schema(description = "IP PHONE ID", example = "07077843804")
	private String ippbxUserId;

	/** IP PHONE 내선번호 */
	@Schema(description = "IP PHONE 내선번호", example = "804")
	private String ippbxInnerTel;
}
