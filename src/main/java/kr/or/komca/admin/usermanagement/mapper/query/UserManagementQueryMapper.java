package kr.or.komca.admin.usermanagement.mapper.query;

import kr.or.komca.admin.usermanagement.dto.query.response.UserRole;
import kr.or.komca.admin.usermanagement.dto.query.condition.UserManageSearchCondition;
import kr.or.komca.admin.usermanagement.dto.query.response.UserManagement;
import kr.or.komca.admin.usermanagement.dto.query.response.UserTempMenuAuth;
import kr.or.komca.admin.usermanagement.dto.query.response.UserMdmAuth;
import kr.or.komca.admin.usermanagement.dto.query.condition.UserMdmAuthSearchCondition;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface UserManagementQueryMapper {

	/**
	 * 사용자 통합 조회
	 *
	 * @param condition 검색 조건 DTO
	 * @return 사용자 목록
	 */
	List<UserManagement> getUserList(@Param("condition") UserManageSearchCondition condition);

	/**
	 * 사용자 ID 존재 여부 확인
	 *
	 * @param userId 사용자 ID
	 * @return 사용자 존재 여부 (true: 존재함, false: 존재하지 않음)
	 */
	boolean isExistUser(@Param("userId") String userId);

	/**
	 * 사용자 ID로 사용자 정보 조회
	 *
	 * @param userId 사용자 ID
	 * @return 사용자 관리 정보
	 */
	UserManagement getUserById(@Param("userId") String userId);

	/**
	 * 사용자 ID에 따른 역할 목록 조회
	 *
	 * @param userId 사용자 ID
	 * @return 사용자에게 할당된 역할 목록
	 */
	List<UserRole> getRoleListByUserId(@Param("userId") String userId);

	/**
	 * 사용자 ID에 따른 역할 코드 목록 조회
	 *
	 * @param userId 사용자 ID
	 * @return 사용자에게 할당된 역할 코드 목록
	 */
	List<String> getRoleCdListByUserId(@Param("userId") String userId);

	/**
	 * 사용자가 가진 임시 메뉴 권한 조회
	 *
	 * @param userId 사용자 ID
	 * @return 임시 메뉴 권한 목록 응답
	 */
	List<UserTempMenuAuth> getTempAuthByUserId(@Param("userId") String userId);

	/**
	 * 사용자가 가진 매체 코드 권한 조회 (페이징 포함)
	 *
	 * @param userId 사용자 ID
	 * @param condition 검색 조건 (페이징 포함)
	 * @return 매체 코드 권한 목록 응답
	 */
	List<UserMdmAuth> getMdmAuthByUserId(
			@Param("userId") String userId,
			@Param("condition") UserMdmAuthSearchCondition condition
	);

	/**
	 * 사용자의 특정 메뉴 임시권한 존재 여부 확인
	 *
	 * @param userId 사용자 ID
	 * @param menuCdList 확인할 메뉴 코드 목록
	 * @return 존재하지 않는 메뉴 코드 목록 (빈 리스트면 모두 존재)
	 */
	List<String> getInvalidUserTempAuthList(
			@Param("userId") String userId,
			@Param("menuCdList") List<String> menuCdList
	);

	/**
	 * 사용자의 특정 메뉴 임시권한 중 이미 존재하는 메뉴 코드 목록 조회
	 *
	 * @param userId 사용자 ID
	 * @param menuCdList 확인할 메뉴 코드 목록
	 * @return 이미 존재하는 메뉴 코드 목록 (빈 리스트면 모두 새로운 권한)
	 */
	List<String> getExistingUserTempAuthList(
			@Param("userId") String userId,
			@Param("menuCdList") List<String> menuCdList
	);

	/**
	 * 사용자의 특정 역할 중 존재하지 않는 역할 코드 목록 조회
	 *
	 * @param userId 사용자 ID
	 * @param roleCdList 확인할 역할 코드 목록
	 * @return 존재하지 않는 역할 코드 목록 (빈 리스트면 모두 존재)
	 */
	List<String> getInvalidUserRoleList(
			@Param("userId") String userId,
			@Param("roleCdList") List<String> roleCdList
	);

	/**
	 * 사용자의 특정 역할 중 이미 존재하는 역할 코드 목록 조회
	 *
	 * @param userId 사용자 ID
	 * @param roleCdList 확인할 역할 코드 목록
	 * @return 이미 존재하는 역할 코드 목록 (빈 리스트면 모두 새로운 역할)
	 */
	List<String> getExistingUserRoleList(
			@Param("userId") String userId,
			@Param("roleCdList") List<String> roleCdList
	);

	/**
	 * 사용자의 매체 코드 권한 중복 여부 확인
	 *
	 * @param userId 사용자 ID
	 * @param largeClassCd 대분류 코드
	 * @param aveClassCd 중분류 코드 (nullable)
	 * @param smallClassCd 소분류 코드 (nullable)
	 * @param mdmCd 매체 코드 (nullable)
	 * @param svcCd 서비스 코드 (nullable)
	 * @return 중복 권한 존재 여부
	 */
	boolean existsUserMdmAuth(
			@Param("userId") String userId,
			@Param("largeClassCd") String largeClassCd,
			@Param("aveClassCd") String aveClassCd,
			@Param("smallClassCd") String smallClassCd,
			@Param("mdmCd") String mdmCd,
			@Param("svcCd") String svcCd
	);
}

