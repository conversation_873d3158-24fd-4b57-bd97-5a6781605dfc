package kr.or.komca.admin.usermanagement.dto.query.condition;

import java.util.HashMap;
import java.util.Map;

/**
 * 사용자 매체 코드 권한 정렬 컬럼 검증기
 */
public class UserMdmAuthSortColumnValidator {

    /**
     * 허용된 정렬 컬럼 매핑 정보
     * Key: 프론트엔드 요청 파라미터명
     * Value: 실제 DB 테이블의 컬럼명
     */
    private static final Map<String, String> ALLOWED_SORT_COLUMNS_MAP = new HashMap<>() {
        {
            put("seq", "TO_NUMBER(seq)");          // 순번
            put("largeClassCd", "large_class_cd");  // 대분류 코드
            put("aveClassCd", "ave_class_cd");      // 중분류 코드
            put("smallClassCd", "small_class_cd");  // 소분류 코드
            put("mdmCd", "mdm_cd");                 // MDM 코드
            put("svcCd", "svc_cd");                 // 서비스 코드
            put("useYn", "use_yn");                 // 사용여부
            put("insDt", "ins_dt");                 // 등록일시
            put("modDt", "mod_dt");                 // 수정일시
        }
    };

    /**
     * 정렬 컬럼 유효성 검사 및 매핑
     */
    public static String validate(String sortColumn) {
        return ALLOWED_SORT_COLUMNS_MAP.get(sortColumn);
    }
}
