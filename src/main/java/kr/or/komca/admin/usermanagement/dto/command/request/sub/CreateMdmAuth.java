package kr.or.komca.admin.usermanagement.dto.command.request.sub;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Pattern;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.ToString;

@Getter
@Builder
@ToString
@NoArgsConstructor
@AllArgsConstructor
public class CreateMdmAuth {

    /** 대분류 코드 */
    @Schema(description = "대분류 코드", example = "A")
    @NotBlank
    @Size(min = 1, max = 1)
    private String largeClassCd;

    /** 중분류 코드 */
    @Schema(description = "중분류 코드", example = "AA")
    @Size(min = 2, max = 2)
    private String aveClassCd;

    /** 소분류 코드 */
    @Schema(description = "소분류 코드", example = "AA01")
    @Size(min = 4, max = 4)
    private String smallClassCd;

    /** 매체 코드 */
    @Schema(description = "매체 코드", example = "AA0101")
    @Size(min = 6, max = 6)
    private String mdmCd;

    /** 서비스 코드 */
    @Schema(description = "서비스 코드", example = "AA010101")
    @Size(max = 8)
    private String svcCd;

    /** 사용 여부 */
    @Schema(description = "사용여부", example = "Y", allowableValues = {"Y", "N"})
    @Pattern(regexp = "^[YN]$")
    private String useYn;
}
