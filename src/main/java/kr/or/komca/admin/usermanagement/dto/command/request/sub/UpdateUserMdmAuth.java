package kr.or.komca.admin.usermanagement.dto.command.request.sub;

import jakarta.validation.Valid;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.util.List;

@Getter
@Builder
@ToString
@NoArgsConstructor
@AllArgsConstructor
public class UpdateUserMdmAuth {

    private List<@Valid CreateMdmAuth> create;

    private List<@Valid UpdateMdmAuth> update;
}
