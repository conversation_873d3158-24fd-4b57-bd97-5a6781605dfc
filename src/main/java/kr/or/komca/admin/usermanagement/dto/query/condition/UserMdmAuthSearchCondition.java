package kr.or.komca.admin.usermanagement.dto.query.condition;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * 사용자 매체 코드 권한 조회 조건 DTO
 */
@AllArgsConstructor
@Getter
@Builder
@ToString
public class UserMdmAuthSearchCondition {

    @Schema(description = "페이지 번호", example = "1")
    private Integer page;

    @Schema(description = "페이지 크기 (1-100)", example = "10")
    private Integer pageSize;

    /** 정렬할 컬럼 **/
    @Schema(description = "정렬할 컬럼", example = "INS_DT")
    private String sortColumn;

    /** 정렬 순서 1: asc. 2: desc  */
    @Schema(description = "정렬 순서 (1: 오름차순, 2: 내림차순)", example = "2", allowableValues = {"1", "2"})
    private String sortOrder;

    public String getSortColumn() {
        return UserMdmAuthSortColumnValidator.validate(this.sortColumn);
    }

    public String getSortOrder() {
        if ("1".equals(this.sortOrder)) return "ASC";
        if ("2".equals(this.sortOrder)) return "DESC";
        return "";
    }
}
