package kr.or.komca.admin.menu.dto.command.request;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;

import java.io.Serial;
import java.io.Serializable;

/**
 * 메뉴 순서 항목
 */
@Getter
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class MenuOrder {

	/** 메뉴코드 */
	@NotNull
	@Schema(description = "메뉴코드", example = "00005")
	private String menuCd;

	/** 정렬순서 */
	@NotNull
	@Schema(description = "정렬순서", example = "1")
	private Integer sortOrd;
}