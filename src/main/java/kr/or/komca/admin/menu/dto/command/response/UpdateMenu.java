package kr.or.komca.admin.menu.dto.command.response;

import io.swagger.v3.oas.annotations.media.Schema;
import kr.or.komca.admin.menu.dto.query.response.MenuSummary;
import lombok.*;

/**
 * 메뉴 수정 응답 DTO
 */
@Getter
@NoArgsConstructor
@AllArgsConstructor
@Builder
@ToString
public class UpdateMenu {

    /** 메뉴코드 */
    @Schema(description = "메뉴코드", example = "00005")
    private String menuCd;

    /** 영향 받은 행 수 */
    @Schema(description = "[메뉴 변경] 영향 받은 행 수", example = "3")
    private int affectedMenuRows;

    /** 영향 받은 행 수 */
    @Schema(description = "[순서 변경] 영향 받은 행 수", example = "3")
    private int affectedOrderRows;
}