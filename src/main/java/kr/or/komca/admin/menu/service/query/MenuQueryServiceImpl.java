package kr.or.komca.admin.menu.service.query;

import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import kr.or.komca.admin.menu.dto.query.condition.MenuSearchCondition;
import kr.or.komca.admin.menu.dto.query.response.*;
import kr.or.komca.admin.menu.enums.errorcode.MenuErrorCode;
import kr.or.komca.admin.menu.mapper.query.MenuQueryMapper;
import kr.or.komca.admin.menu.util.MenuUtils;
import kr.or.komca.admin.usermanagement.dto.query.response.UserManagement;
import kr.or.komca.common.exception.core.CommonException;
import kr.or.komca.common.utils.core.dto.response.ListResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;

@Slf4j
@Service
@RequiredArgsConstructor
@Transactional(readOnly = true)
public class MenuQueryServiceImpl implements MenuQueryService {

	private final MenuQueryMapper menuQueryMapper;

	@Override
	public MenuDetail getMenuDetailById(String menuCd) {
		log.info("메뉴 상세 조회 요청 - menuCd: {}", menuCd);
		
		MenuDetail menuDetail = menuQueryMapper.getMenuDetailById(menuCd);

		// 유효성 검사
		if (menuDetail == null) {
			log.error("메뉴 상세 조회 실패 - 메뉴를 찾을 수 없음: {}", menuCd);
			throw new CommonException(MenuErrorCode.NOT_FOUND_MENU,
					String.format("메뉴 코드를 찾을 수 없습니다 : %s", menuCd)
			);
		}

		return menuDetail;
	}

	@Override
	public ListResponse<MenuTree> getSubMenuListById(String menuCd, MenuSearchCondition condition) {
		log.info("하위 메뉴 목록 조회 요청 - menuCd: {}", menuCd);
		
		// 부모 Id 유효성 검사
		validateMenuExists(menuCd);

		try (Page<UserManagement> page = PageHelper.startPage(1, Integer.MAX_VALUE, true)) {

			// 하위 메뉴 조회
			List<MenuTree> subMenuList = menuQueryMapper.getSubMenuList(menuCd, condition);

			// 하위 메뉴 유효성 검사
			if (subMenuList == null || subMenuList.isEmpty()) {
				return ListResponse.<MenuTree>builder()
						.contents(Collections.emptyList())
						.totalElements(0L)
						.build();
			}

			long totalCount = page.getTotal();

			return ListResponse.<MenuTree>builder()
					.contents(subMenuList)
					.totalElements(totalCount)
					.build();
		}
	}

	@Override
	@Transactional(readOnly = true)
	public void validateMenuExists(String menuCd) {
		boolean exist = isExistMenu(menuCd);
		if (!exist) {
			throw new CommonException(MenuErrorCode.NOT_FOUND_MENU,
					String.format("메뉴 코드를 찾을 수 없습니다 : %s", menuCd)
			);
		}
	}

	@Override
	@Transactional(readOnly = true)
	public void validateMenuExists(List<String> menuCdList) {
		List<String> invalidMenuCdList = menuQueryMapper.getInvalidMenuCdList(menuCdList);

		if (invalidMenuCdList != null && !invalidMenuCdList.isEmpty()) {
			throw new CommonException(MenuErrorCode.NOT_FOUND_MENU,
					String.format("메뉴 코드를 찾을 수 없습니다 : %s", menuCdList)
			);
		}
	}

	@Override
	public ListResponse<MenuTree> getMenuTree(MenuSearchCondition condition) {
		log.info("메뉴 트리 조회 요청: {}", condition);

		try (Page<UserManagement> page = PageHelper.startPage(1, Integer.MAX_VALUE, true)) {

			// 선형 구조 메뉴 조회
			List<MenuTree> flatMenuList = menuQueryMapper.getMenuTree(condition);

			// 결과가 없는 경우 빈 리스트 반환
			if (flatMenuList == null || flatMenuList.isEmpty()) {
				return ListResponse.<MenuTree>builder()
						.contents(Collections.emptyList())
						.totalElements(0L)
						.build();
			}

			long totalCount = page.getTotal();

			// 평면 구조를 계층 구조로 변환 후 반환
			List<MenuTree> menuTrees = MenuUtils.parseToMenuTree(flatMenuList);

			return ListResponse.<MenuTree>builder()
					.contents(menuTrees)
					.totalElements(totalCount)
					.build();
		}
	}

	@Override
	public ListResponse<MenuDetail> getMenuDetailList(MenuSearchCondition condition) {
		try (Page<UserManagement> page = PageHelper.startPage(1, Integer.MAX_VALUE, true)) {

			List<MenuDetail> menuDetailList = menuQueryMapper.getMenuDetailList(condition);

			long totalCount = page.getTotal();

			return ListResponse.<MenuDetail>builder()
					.contents(menuDetailList)
					.totalElements(totalCount)
					.build();
		}
	}

	@Override
	public MenuPath getMenuPathById(String id) {
		// 유효성 검사
		validateMenuExists(id);
		return menuQueryMapper.getMenuPath(id);
	}

	@Override
	public String getNextMenuCd() {
		return menuQueryMapper.getNextMenuCd();
	}

	@Override
	public int countChildMenus(String menuCd) {
		return menuQueryMapper.countChildMenus(menuCd);
	}

	@Override
	public boolean isActive(String menuCd) {
		return menuQueryMapper.isActive(menuCd);
	}

	@Override
	public MenuSummary getMenuSummaryById(String menuCd) {
		return menuQueryMapper.getMenuSummaryById(menuCd);
	}

	@Override
	public ListResponse<AvailableMenu> getAvailableMenuListByUser(String userId) {
		log.info("사용 가능한 메뉴 목록 조회 요청 - userId: {}", userId);

		try (Page<UserManagement> page = PageHelper.startPage(1, Integer.MAX_VALUE, true)) {

			List<AvailableMenu> menuList = menuQueryMapper.getAvailableMenuListByUser(userId);

			return ListResponse.<AvailableMenu>builder()
					.contents(menuList)
					.totalElements(page.getTotal())
					.build();
		}
	}

	@Override
	public boolean isExistMenu(String menuCd) {
		return menuQueryMapper.isExistMenu(menuCd);
	}
}
