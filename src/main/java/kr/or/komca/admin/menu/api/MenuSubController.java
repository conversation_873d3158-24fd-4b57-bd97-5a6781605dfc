//package kr.or.komca.admin.menu.api;
//
//import jakarta.validation.Valid;
//import kr.or.komca.admin.menu.dto.command.request.*;
//import kr.or.komca.admin.menu.dto.command.response.*;
//import kr.or.komca.admin.menu.dto.query.condition.MenuSearchCondition;
//import kr.or.komca.admin.menu.dto.query.response.*;
//import kr.or.komca.admin.menu.service.command.MenuCommandService;
//import kr.or.komca.admin.menu.service.query.MenuQueryService;
//import kr.or.komca.admin.menu.util.MenuUtils;
//import kr.or.komca.common.exception.response.success.CommonSuccessResponse;
//import kr.or.komca.common.utils.core.dto.response.ListResponse;
//import lombok.RequiredArgsConstructor;
//import lombok.extern.slf4j.Slf4j;
//import org.springframework.http.ResponseEntity;
//import org.springframework.web.bind.annotation.*;
//
//@Slf4j
//@RestController
//@RequestMapping("/api/v1/menu")
//@RequiredArgsConstructor
//public class MenuSubController implements MenuSubApi {
//
//    private final MenuQueryService menuQueryService;
//    private final MenuCommandService menuCommandService;
//
//    @Override
//    @GetMapping
//    public ResponseEntity<CommonSuccessResponse<ListResponse<MenuDetail>>> getMenuList(
//            @Valid @ModelAttribute MenuSearchCondition condition
//    ) {
//        log.info("getMenuList - condition: {}", condition);
//        ListResponse<MenuDetail> menuList = menuQueryService.getMenuDetailList(condition);
//        return CommonSuccessResponse.ok(menuList);
//    }
//
//    @Override
//    @GetMapping("/{menuCd}")
//    public ResponseEntity<CommonSuccessResponse<MenuDetail>> getMenuDetailById(@PathVariable String menuCd) {
//        log.info("getMenuDetailById - menuCd: {}", menuCd);
//        MenuDetail menuDetail = menuQueryService.getMenuDetailById(menuCd);
//
//        return CommonSuccessResponse.ok(menuDetail);
//    }
//
//    @Override
//    @GetMapping("/{menuCd}/sub")
//    public ResponseEntity<CommonSuccessResponse<ListResponse<MenuTree>>> getSubMenuListById(
//            @PathVariable String menuCd,
//            @Valid @ModelAttribute MenuSearchCondition condition
//    ) {
//        log.info("getSubMenuListById - menuCd: {}, condition: {}", menuCd, condition);
//        ListResponse<MenuTree> menuTreeList = menuQueryService.getSubMenuListById(menuCd, condition);
//
//        return CommonSuccessResponse.ok(menuTreeList);
//    }
//
//    @Override
//    @GetMapping("/{menuCd}/path")
//    public ResponseEntity<CommonSuccessResponse<MenuPath>> getMenuPathById(@PathVariable String menuCd) {
//        log.info("getMenuPathById - menuCd: {}", menuCd);
//        MenuPath menuPath = menuQueryService.getMenuPathById(menuCd);
//
//        return CommonSuccessResponse.ok(menuPath);
//    }
//
//    @Override
//    @GetMapping("/{menuCd}/active")
//    public ResponseEntity<CommonSuccessResponse<Boolean>> isActiveMenu(@PathVariable String menuCd) {
//        log.info("isMenuActive - menuCd: {}", menuCd);
//        boolean result = menuQueryService.isActive(menuCd);
//
//        return CommonSuccessResponse.ok(result);
//    }
//
//    @GetMapping("/{menuCd}/summary")
//    public ResponseEntity<CommonSuccessResponse<MenuSummary>> getMenuSummary(@PathVariable String menuCd) {
//        MenuSummary result = menuQueryService.getMenuSummaryById(menuCd);
//
//        return CommonSuccessResponse.ok(result);
//    }
//
//
//    @PostMapping("/update-order")
//    public ResponseEntity<CommonSuccessResponse<UpdateMenuOrder>> updateMenuOrder(
//            @Valid @RequestBody MenuOrderRequest command
//    ) {
//        log.info("updateMenuOrder - command: {}", command);
//        UpdateMenuOrder result = menuCommandService.updateMenuOrder(command);
//        return CommonSuccessResponse.ok(result);
//    }
//
//    @PostMapping
//    public ResponseEntity<CommonSuccessResponse<CreateMenu>> createMenu(
//            @Valid @RequestBody CreateMenuRequest command
//    ) {
//        log.info("createMenu - command: {}", command);
//
//        String tempInspersId = "test";
//
//        CreateMenu result = menuCommandService.createMenu(tempInspersId, command);
//        return CommonSuccessResponse.created(result);
//    }
//
//}