package kr.or.komca.admin.menu.dto.command.request;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotEmpty;
import lombok.*;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;

/**
 * 메뉴 순서 변경 요청 DTO
 */
@Getter
@NoArgsConstructor
@AllArgsConstructor
@Builder
@ToString
public class MenuOrderRequest {

    /** 메뉴 순서 목록 */
    @NotEmpty
    @Schema(description = "메뉴 순서 목록")
    private List<@Valid MenuOrder> orders;

    public static MenuOrderRequest from(List<@Valid MenuOrder> orders) {
        return MenuOrderRequest.builder()
                .orders(orders)
                .build();
    }
}