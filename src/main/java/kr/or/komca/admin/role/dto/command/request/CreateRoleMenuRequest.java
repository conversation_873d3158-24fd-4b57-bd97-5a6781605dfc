package kr.or.komca.admin.role.dto.command.request;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotEmpty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.ToString;

import java.util.List;

/**
 * 역할별 메뉴 권한 저장 요청 DTO
 */
@Builder(toBuilder = true)
@AllArgsConstructor
@ToString
@Getter
public class CreateRoleMenuRequest {

    /** 역할별 메뉴 권한 정보 목록 */
    @Schema(description = "역할별 메뉴 권한 정보 목록")
    @NotEmpty
    @Valid
    private List<@Valid RoleMenuInfo> menuList;
}
