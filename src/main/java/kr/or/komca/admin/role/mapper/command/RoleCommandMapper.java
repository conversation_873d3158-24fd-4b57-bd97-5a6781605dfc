package kr.or.komca.admin.role.mapper.command;

import kr.or.komca.admin.role.dto.command.request.CreateRoleRequest;
import kr.or.komca.admin.role.dto.command.request.RoleMenuInfo;
import kr.or.komca.admin.role.dto.command.request.UpdateRoleRequest;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface RoleCommandMapper {

    /**
     * 역할 생성
     *
     * @Param command   생성 요청
     * @param inspersId 등록자 ID
     * @return 생성된 행 수
     */
    int createRole(
            @Param("inspersId") String inspersId,
            @Param("command") CreateRoleRequest command
    );

    /**
     * 역할 수정
     *
     * @param roleCd    역할 코드
     * @param command   업데이트 요청
     * @param modpersId 수정자 ID
     * @return 수정된 행 수
     */
    int updateRole(
            @Param("roleCd") String roleCd,
            @Param("modpersId") String modpersId,
            @Param("command") UpdateRoleRequest command
    );


    /**
     * 역할 논리삭제
     *
     * @param roleCd 역할 코드
     * @param delpersId 삭제자 ID
     * @return 삭제된 행 수
     */
    int deleteRoleLogical(@Param("roleCd") String roleCd, @Param("delpersId") String delpersId);


    /**
     * 역할별 메뉴 권한 삭제
     *
     * @param roleCd 역할 코드
     * @param menuList 삭제할 메뉴 코드 리스트
     * @return 삭제된 행 수
     */
    int deleteRoleMenu(@Param("roleCd") String roleCd, @Param("menuList") List<String> menuList);

    /**
     * 역할별 모든 메뉴 권한 논리삭제 (역할 삭제시 사용)
     *
     * @param roleCd 역할 코드
     * @param delpersId 삭제자 ID
     * @return 삭제된 행 수
     */
    int deleteRoleMenuLogical(@Param("roleCd") String roleCd, @Param("delpersId") String delpersId);

    /**
     * 역할별 메뉴 권한 저장
     *
     * @param roleCd    역할 코드
     * @param inspersId 등록자 ID
     * @Param command   저장 메뉴 정보
     * @return 저장된 행 수
     */
    int saveRoleMenu(
            @Param("roleCd") String roleCd,
            @Param("inspersId") String inspersId,
            @Param("command") List<RoleMenuInfo> command
    );

    int updateRoleMenu(
            @Param("roleCd") String roleCd,
            @Param("modpersId") String modpersId,
            @Param("command") RoleMenuInfo command
    );
}
