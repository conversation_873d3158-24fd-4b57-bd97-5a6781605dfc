package kr.or.komca.admin.role.dto.command.response;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.ToString;

/**
 * 역할별 메뉴 권한 삭제 결과 DTO
 */
@Builder(toBuilder = true)
@AllArgsConstructor
@ToString
@Getter
@Schema(description = "역할별 메뉴 권한 삭제 결과")
public class DeleteRoleMenu {

	/** 역할 코드 */
	@Schema(description = "역할 코드", example = "00001")
	private String roleCd;

	/** 삭제된 행 수 */
	@Schema(description = "삭제된 행 수", example = "3")
	private int affectedRows;
}
