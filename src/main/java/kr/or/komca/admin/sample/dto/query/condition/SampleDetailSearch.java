package kr.or.komca.admin.sample.dto.query.condition;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Builder;
import lombok.Getter;

/**
 * 상세 검색 조건 DTO
 */
@Schema(description = "샘플 상세 검색 조건 DTO")
@Getter
@Builder
public class SampleDetailSearch {
    /** 샘플 번호 (PK) - IDENTITY 컬럼으로 자동 증가 */
    @Schema(description = "샘플 번호 (PK)",
            example = "1",
            minimum = "1",
            required = true)
    @NotNull(message = "SAMPLE_NO_REQUIRED")
    private Long sampleNo;
}
