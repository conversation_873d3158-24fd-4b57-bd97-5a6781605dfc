package kr.or.komca.admin.sample.dto.command.request;

import jakarta.validation.constraints.*;
import lombok.Builder;
import lombok.Getter;

/**
 * 수정 요청 DTO
 */
@Getter
@Builder
public class SampleUpdateRequest {
    /** 샘플 번호 (PK) - IDENTITY 컬럼으로 자동 증가 */
    @NotNull(message = "SAMPLE_NO_REQUIRED")
    private Long sampleNo;

    /** 제목 (최대 100자) */
    @Size(min = 3, max = 50, message = "TITLE_SIZE")
    private String title;

    /** 내용 (최대 4000자) */
    @Size(max = 1000, message = "CONTENT_SIZE")
    private String content;

    /** 수량 */
    @Min(value = 0, message = "QUANTITY_MIN")
    private Integer quantity;

    /** 연락처 (형식: 000-0000-0000) */
    @Pattern(regexp = "^\\d{3}-\\d{4}-\\d{4}$", message = "PHONE_FORMAT")
    private String phoneNumber;

    /** 이메일 주소 */
    @Email(message = "EMAIL_FORMAT")
    private String email;
}
