package kr.or.komca.admin.sample.dto.query.response;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Builder;
import lombok.Getter;

import java.time.LocalDateTime;

/**
 * 리스트 응답 DTO
 */
@Getter
@Builder
@Schema(description = "샘플 목록 정보 DTO")
public class SampleList {
    /** 샘플 번호 (PK) - IDENTITY 컬럼으로 자동 증가 */
    @Schema(description = "샘플 번호 (PK)", example = "1")
    private Long sampleNo;

    /** 제목 (최대 100자) */
    @Schema(description = "제목", example = "샘플 제목")
    private String title;

    /** 수량 */
    @Schema(description = "수량", example = "10")
    private Integer quantity;

    /** 연락처 (형식: 000-0000-0000) */
    @Schema(description = "연락처", example = "010-1234-5678")
    private String phoneNumber;

    /** 이메일 주소 */
    @Schema(description = "이메일 주소", example = "<EMAIL>")
    private String email;

    /** 생성일시 - 기본값: SYSTIMESTAMP */
    @Schema(description = "생성일시", example = "2024-02-20T10:00:00")
    private LocalDateTime createdAt;

    /** 수정일시 - 기본값: SYSTIMESTAMP */
    @Schema(description = "수정일시", example = "2024-02-20T10:00:00")
    private LocalDateTime updatedAt;
}
