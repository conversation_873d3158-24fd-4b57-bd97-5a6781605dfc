package kr.or.komca.admin.sample.service.query;

import kr.or.komca.admin.sample.dto.query.condition.SampleDetailSearch;
import kr.or.komca.admin.sample.dto.query.condition.SampleListSearch;
import kr.or.komca.admin.sample.dto.query.response.SampleDetail;
import kr.or.komca.admin.sample.dto.query.response.SampleExcelList;
import kr.or.komca.admin.sample.dto.query.response.SampleList;
import kr.or.komca.common.utils.core.dto.response.PageListResponse;

/**
 * 샘플 조회 로직을 처리하는 서비스 인터페이스
 */
public interface SampleQueryService {
    /**
     * 검색 조건에 맞는 리스트 조회
     *
     * @param request 리스트 검색 조건 DTO
     * @return 페이징 처리된 샘플 목록
     */
    PageListResponse<SampleList> getSampleList(SampleListSearch request);

    /**
     * 상세 조회
     *
     * @param request 상세 조회 조건 DTO
     * @return 상세 응답 DTO
     */
    SampleDetail getSampleDetail(SampleDetailSearch request);

    /**
     * 엑셀 조건에 맞는 리스트 조회
     *
     * @param request 리스트 검색 조건 DTO
     * @return 페이징 처리된 샘플 목록
     */
    PageListResponse<SampleExcelList> getSampleExcelList(SampleListSearch request);
}
