package kr.or.komca.admin.sample.dto.command.request;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.*;
import lombok.Builder;
import lombok.Getter;

/**
 * 생성 요청 DTO
 */
@Schema(description = "샘플 생성 요청 DTO")
@Getter
@Builder
public class SampleCreateRequest {
    /** 샘플 번호 (자동 증가) */
    @Schema(description = "샘플 번호 (PK) - IDENTITY 컬럼으로 자동 증가", accessMode = Schema.AccessMode.READ_ONLY)
    private Long sampleNo;

    /** 샘플 제목 (3-50자) */
    @Schema(description = "제목", example = "샘플 제목", maxLength = 50, minLength = 3)
    @NotBlank(message = "TITLE_REQUIRED")
    @Size(min = 3, max = 50, message = "TITLE_SIZE")
    private String title;

    /** 샘플 내용 (최대 1000자) */
    @Schema(description = "내용", example = "샘플 내용", maxLength = 1000)
    @Size(max = 1000, message = "CONTENT_SIZE")
    @NotNull
    private String content;

    /** 샘플 수량 (0 이상) */
    @Schema(description = "수량", example = "10", minimum = "0")
    @NotNull(message = "QUANTITY_REQUIRED")
    @Min(value = 0, message = "QUANTITY_MIN")
    private Integer quantity;

    /** 연락처 (형식: 000-0000-0000) */
    @Schema(description = "연락처", example = "010-1234-5678", pattern = "^\\d{3}-\\d{4}-\\d{4}$")
    @Pattern(regexp = "^\\d{3}-\\d{4}-\\d{4}$", message = "PHONE_FORMAT")
    private String phoneNumber;

    /** 이메일 주소 */
    @Schema(description = "이메일 주소", example = "<EMAIL>")
    @Email(message = "EMAIL_FORMAT")
    private String email;
}
