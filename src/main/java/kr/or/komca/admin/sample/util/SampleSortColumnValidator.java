package kr.or.komca.admin.sample.util;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

/**
 * Sort Validator
 */
@Slf4j
@Component
public class SampleSortColumnValidator {

    /**
     * 허용된 정렬 컬럼 매핑 정보
     * Key: 프론트엔드 요청 파라미터명
     * Value: 실제 DB 테이블의 컬럼명
     */
    private static final Map<String, String> ALLOWED_SORT_COLUMNS_MAP = new HashMap<>() {{
        put("sampleNo", "sample_no");       // 샘플 번호
        put("title", "title");              // 제목
        put("quantity", "quantity");        // 수량
        put("phoneNumber", "phone_number"); // 연락처
        put("email", "email");              // 이메일 주소
        put("createdAt", "created_at");     // 생성일시
        put("updatedAt", "updated_at");     // 수정일시
        put("userNo", "user_no");           // 사용자 번호
        put("username", "username");        // 사용자 이름
    }};

    /**
     * 정렬 컬럼 유효성 검사 및 매핑
     */
    public static String validate(String sortColumn) {
        return ALLOWED_SORT_COLUMNS_MAP.get(sortColumn);
    }

}

