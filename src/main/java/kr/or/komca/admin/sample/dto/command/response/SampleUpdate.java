package kr.or.komca.admin.sample.dto.command.response;

import io.swagger.v3.oas.annotations.media.Schema;
import kr.or.komca.admin.sample.dto.command.request.SampleUpdateRequest;
import lombok.Builder;
import lombok.Getter;

/**
 * 수정 응답
 */
@Schema(description = "샘플 수정 응답 DTO")
@Getter
@Builder
public class SampleUpdate {
    /** 수정 요청 참고 */
    @Schema(description = "수정 요청 정보",
            implementation = SampleUpdateRequest.class)
    private SampleUpdateRequest request;
    
    /** 수정 카운트 */
    @Schema(description = "수정된 레코드 수",
            example = "1",
            minimum = "0")
    private int updateCount;
}
