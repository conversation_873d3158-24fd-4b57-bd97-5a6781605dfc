package kr.or.komca.admin.sample.mapper.command;

import kr.or.komca.admin.sample.dto.command.request.SampleCreateRequest;
import kr.or.komca.admin.sample.dto.command.request.SampleDeleteRequest;
import kr.or.komca.admin.sample.dto.command.request.SampleUpdateRequest;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 샘플 데이터에 대한 Command 를 처리하는 Mapper 인터페이스
 */
@Mapper
public interface SampleCommandMapper {

    /**
     * 생성
     *
     * @param request 생성 요청 DTO
     * @param userNo 사용자 번호
     */
    void createSample(@Param("request") SampleCreateRequest request, @Param("userNo") Long userNo);

    /**
     * 수정
     *
     * @param request 수정 요청 DTO
     * @return updateCount
     */
    int updateSample(SampleUpdateRequest request);

    /**
     * 삭제
     *
     * @param request 삭제 요청 DTO
     */
    void deleteSample(SampleDeleteRequest request);
}
