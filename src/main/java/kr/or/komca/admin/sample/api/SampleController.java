package kr.or.komca.admin.sample.api;

import kr.or.komca.admin.sample.dto.command.request.SampleCreateRequest;
import kr.or.komca.admin.sample.dto.command.request.SampleDeleteRequest;
import kr.or.komca.admin.sample.dto.command.request.SampleUpdateRequest;
import kr.or.komca.admin.sample.dto.command.response.SampleCreate;
import kr.or.komca.admin.sample.dto.command.response.SampleUpdate;
import kr.or.komca.admin.sample.dto.query.condition.SampleDetailSearch;
import kr.or.komca.admin.sample.dto.query.condition.SampleListSearch;
import kr.or.komca.admin.sample.dto.query.response.SampleDetail;
import kr.or.komca.admin.sample.dto.query.response.SampleExcelList;
import kr.or.komca.admin.sample.dto.query.response.SampleList;
import kr.or.komca.admin.sample.service.command.SampleCommandService;
import kr.or.komca.admin.sample.service.query.SampleQueryService;
import kr.or.komca.common.auth.annotation.CheckMenuPermission;
import kr.or.komca.common.auth.support.utils.context.UserContextHolder;
import kr.or.komca.common.exception.response.success.CommonSuccessResponse;
import kr.or.komca.common.utils.core.dto.response.PageListResponse;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * 샘플 데이터 관리를 위한 REST API Controller
 */

@RequestMapping("/api/v1/sample")
@RestController
@RequiredArgsConstructor
public class SampleController implements SampleApi {
	private final SampleCommandService sampleCommandService;
	private final SampleQueryService sampleQueryService;

	@PreAuthorize("hasAnyRole('ADMIN', 'USER', 'MANAGER')")
	@PostMapping
	@Override
	public ResponseEntity<CommonSuccessResponse<SampleCreate>> createSample(
			@Validated @RequestBody SampleCreateRequest request) {
		Long userNo = UserContextHolder.getContext().getUserNo();
		return CommonSuccessResponse.created(sampleCommandService.createSample(request, userNo));
	}

	@PreAuthorize("hasAnyRole('ADMIN', 'USER')")
	@CheckMenuPermission(menuCd = "00002", authType = {"SELECT", "EDIT"})
	@GetMapping("/list2")
	public ResponseEntity<CommonSuccessResponse<PageListResponse<SampleList>>> getSampleList1() {
		System.out.println("== UserContextHolder.getContext().getUserId() " + UserContextHolder.getContext().getUserId() );
		System.out.println("== UserContextHolder.getContext().getStaff() " + UserContextHolder.getContext().getStaff() );
		System.out.println("== UserContextHolder.getContext().getUserGbn() " + UserContextHolder.getContext().getUserGbn() );
		System.out.println("== UserContextHolder.getContext().getRoleName() " + UserContextHolder.getContext().getRoleName() );
		System.out.println("== UserContextHolder.getContext().getPermissions() " + UserContextHolder.getContext().getPermissions() );
		PageListResponse<SampleList> aaa = PageListResponse.<SampleList>builder()
				.build();
		return CommonSuccessResponse.ok(aaa);
	}



	@PreAuthorize("hasAnyRole('ADMIN', 'USER')")
	@GetMapping
	@Override
	public ResponseEntity<CommonSuccessResponse<PageListResponse<SampleList>>> getSampleList(
			@ModelAttribute @Validated SampleListSearch request) {
		return CommonSuccessResponse.ok(sampleQueryService.getSampleList(request));
	}

	@PreAuthorize("hasRole('USER')")
	@GetMapping("/detail")
	@Override
	public ResponseEntity<CommonSuccessResponse<SampleDetail>> getSampleDetail(
			@ModelAttribute @Validated SampleDetailSearch request) {
		return CommonSuccessResponse.ok(sampleQueryService.getSampleDetail(request));
	}

	@PreAuthorize("hasRole('USER')")
	@PostMapping("/update")
	@Override
	public ResponseEntity<CommonSuccessResponse<SampleUpdate>> updateSample(
			@Validated @RequestBody SampleUpdateRequest request) {
		return CommonSuccessResponse.ok(sampleCommandService.updateSample(request));
	}

	@PreAuthorize("hasRole('USER')")
	@PostMapping("/delete/{sampleNo}")
	@Override
	public ResponseEntity<CommonSuccessResponse<Void>> deleteSampleBySampleNo(@PathVariable Long sampleNo) {
		sampleCommandService.deleteSample(
				SampleDeleteRequest.builder()
						.sampleNo(sampleNo)
						.build()
		);
		return CommonSuccessResponse.ok(null);
	}

	@PreAuthorize("hasRole('USER')")
	@PostMapping("/delete")
	@Override
	public ResponseEntity<CommonSuccessResponse<Void>> deleteSample(@RequestBody SampleDeleteRequest request) {
		sampleCommandService.deleteSample(request);
		return CommonSuccessResponse.ok(null);
	}

	@PreAuthorize("hasRole('USER')")
	@GetMapping("/excel")
	public ResponseEntity<CommonSuccessResponse<PageListResponse<SampleExcelList>>> getSampleExcelList(
			@ModelAttribute @Validated SampleListSearch request) {
		return CommonSuccessResponse.ok(sampleQueryService.getSampleExcelList(request));
	}
}
