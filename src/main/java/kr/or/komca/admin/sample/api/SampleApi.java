package kr.or.komca.admin.sample.api;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import kr.or.komca.common.exception.response.error.CommonErrorResponse;
import kr.or.komca.common.exception.response.success.CommonSuccessResponse;
import kr.or.komca.admin.sample.dto.command.request.SampleCreateRequest;
import kr.or.komca.admin.sample.dto.command.request.SampleDeleteRequest;
import kr.or.komca.admin.sample.dto.command.request.SampleUpdateRequest;
import kr.or.komca.admin.sample.dto.command.response.SampleCreate;
import kr.or.komca.admin.sample.dto.command.response.SampleUpdate;
import kr.or.komca.admin.sample.dto.query.condition.SampleDetailSearch;
import kr.or.komca.admin.sample.dto.query.condition.SampleListSearch;
import kr.or.komca.admin.sample.dto.query.response.SampleDetail;
import kr.or.komca.admin.sample.dto.query.response.SampleList;
import kr.or.komca.common.utils.core.dto.response.PageListResponse;
import org.springframework.http.ResponseEntity;

@Tag(name = "Sample", description = "샘플 데이터 관리 API")
public interface SampleApi {

    @Operation(
            summary = "샘플 데이터 생성",
            description = "새로운 샘플 데이터를 생성합니다."
    )
    @ApiResponses({
            @ApiResponse(
                    responseCode = "201",
                    description = "생성 성공"
            ),
            @ApiResponse(
                    responseCode = "400",
                    description = "잘못된 요청",
                    content = @Content(
                            schema = @Schema(implementation = CommonErrorResponse.class))
            ),
            @ApiResponse(
                    responseCode = "403",
                    description = "권한 없음",
                    content = @Content(
                            schema = @Schema(implementation = CommonErrorResponse.class))
            )
    })
    ResponseEntity<CommonSuccessResponse<SampleCreate>> createSample(
            @Parameter(description = "샘플 생성 요청 데이터", required = true)
            SampleCreateRequest request
    );

    @Operation(
            summary = "샘플 데이터 목록 조회",
            description = "검색 조건에 맞는 샘플 데이터 목록을 조회합니다."
    )
    @ApiResponses({
            @ApiResponse(
                    responseCode = "200",
                    description = "조회 성공"
            ),
            @ApiResponse(
                    responseCode = "400",
                    description = "잘못된 요청",
                    content = @Content(schema = @Schema(implementation = CommonErrorResponse.class))
            ),
            @ApiResponse(
                    responseCode = "403",
                    description = "권한 없음",
                    content = @Content(schema = @Schema(implementation = CommonErrorResponse.class))
            )
    })
    ResponseEntity<CommonSuccessResponse<PageListResponse<SampleList>>> getSampleList(
            @Parameter(description = "샘플 목록 검색 조건", required = true)
            SampleListSearch request
    );

    @Operation(
            summary = "샘플 데이터 상세 조회",
            description = "특정 샘플 데이터의 상세 정보를 조회합니다."
    )
    @ApiResponses({
            @ApiResponse(
                    responseCode = "200",
                    description = "조회 성공"
            ),
            @ApiResponse(
                    responseCode = "400",
                    description = "잘못된 요청",
                    content = @Content(schema = @Schema(implementation = CommonErrorResponse.class))
            ),
            @ApiResponse(
                    responseCode = "403",
                    description = "권한 없음",
                    content = @Content(schema = @Schema(implementation = CommonErrorResponse.class))
            ),
            @ApiResponse(
                    responseCode = "404",
                    description = "리소스 없음",
                    content = @Content(schema = @Schema(implementation = CommonErrorResponse.class))
            )
    })
    ResponseEntity<CommonSuccessResponse<SampleDetail>> getSampleDetail(
            @Parameter(description = "샘플 상세 검색 조건", required = true)
            SampleDetailSearch request
    );

    @Operation(
            summary = "샘플 데이터 수정",
            description = "기존 샘플 데이터를 수정합니다."
    )
    @ApiResponses({
            @ApiResponse(
                    responseCode = "200",
                    description = "수정 성공"
            ),
            @ApiResponse(
                    responseCode = "400",
                    description = "잘못된 요청",
                    content = @Content(schema = @Schema(implementation = CommonErrorResponse.class))
            ),
            @ApiResponse(
                    responseCode = "403",
                    description = "권한 없음",
                    content = @Content(schema = @Schema(implementation = CommonErrorResponse.class))
            ),
            @ApiResponse(
                    responseCode = "404",
                    description = "리소스 없음",
                    content = @Content(schema = @Schema(implementation = CommonErrorResponse.class))
            )
    })
    ResponseEntity<CommonSuccessResponse<SampleUpdate>> updateSample(
            @Parameter(description = "샘플 수정 요청 데이터", required = true)
            SampleUpdateRequest request
    );

    @Operation(
            summary = "샘플 데이터 삭제 (Path Variable)",
            description = "샘플 번호를 통해 특정 샘플 데이터를 삭제합니다."
    )
    @ApiResponses({
            @ApiResponse(
                    responseCode = "200",
                    description = "삭제 성공"
            ),
            @ApiResponse(
                    responseCode = "400",
                    description = "잘못된 요청",
                    content = @Content(schema = @Schema(implementation = CommonErrorResponse.class))
            ),
            @ApiResponse(
                    responseCode = "403",
                    description = "권한 없음",
                    content = @Content(schema = @Schema(implementation = CommonErrorResponse.class))
            ),
            @ApiResponse(
                    responseCode = "404",
                    description = "리소스 없음",
                    content = @Content(schema = @Schema(implementation = CommonErrorResponse.class))
            )
    })
    ResponseEntity<CommonSuccessResponse<Void>> deleteSampleBySampleNo(
            @Parameter(description = "삭제할 샘플 번호", required = true)
            Long sampleNo
    );

    @Operation(
            summary = "샘플 데이터 삭제 (Request Body)",
            description = "요청 본문을 통해 특정 샘플 데이터를 삭제합니다."
    )
    @ApiResponses({
            @ApiResponse(
                    responseCode = "200",
                    description = "삭제 성공"
            ),
            @ApiResponse(
                    responseCode = "400",
                    description = "잘못된 요청",
                    content = @Content(schema = @Schema(implementation = CommonErrorResponse.class))
            ),
            @ApiResponse(
                    responseCode = "403",
                    description = "권한 없음",
                    content = @Content(schema = @Schema(implementation = CommonErrorResponse.class))
            ),
            @ApiResponse(
                    responseCode = "404",
                    description = "리소스 없음",
                    content = @Content(schema = @Schema(implementation = CommonErrorResponse.class))
            )
    })
    ResponseEntity<CommonSuccessResponse<Void>> deleteSample(
            @Parameter(description = "샘플 삭제 요청 데이터", required = true)
            SampleDeleteRequest request
    );
}