package kr.or.komca.admin.sample.enums.errorcode;

import kr.or.komca.common.interfaces.response.code.ErrorCode;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpStatus;

/**
 * 각 메뉴별 프로젝트의 개별 ErrorEnum 처리
 * 1. 상수명: 대문자 스네이크 케이스로 작성 (예: INVALID_SAMPLE_STATUS)
 * 2. code: 상수명과 동일하게 문자열로 작성
 * 3. status: 적절한 HTTP 상태 코드 설정 (HttpStatus enum 사용)
 */
@Getter
@RequiredArgsConstructor
public enum SampleErrorCode implements ErrorCode {

    /**
     * 샘플의 상태가 유효하지 않은 경우 발생하는 예외
     * 발생 상황:
     * - 이미 삭제된 샘플에 접근할 때
     * - 처리 불가능한 상태의 샘플을 수정하려 할 때
     */
    INVALID_SAMPLE_STATUS("INVALID_SAMPLE_STATUS", HttpStatus.BAD_REQUEST);

    private final String code;
    private final HttpStatus status;
}
