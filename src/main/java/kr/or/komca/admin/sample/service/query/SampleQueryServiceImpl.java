package kr.or.komca.admin.sample.service.query;

import kr.or.komca.common.auth.domain.common.KomcaStaff;
import kr.or.komca.common.auth.domain.common.UserContext;
import kr.or.komca.common.auth.support.utils.context.UserContextHolder;
import kr.or.komca.common.exception.core.CommonException;
import kr.or.komca.common.exception.response.error.code.CommonErrorCode;
import kr.or.komca.admin.common.utils.page.core.PageUtils;
import kr.or.komca.admin.sample.dto.query.condition.SampleDetailSearch;
import kr.or.komca.admin.sample.dto.query.condition.SampleListSearch;
import kr.or.komca.admin.sample.dto.query.response.SampleDetail;
import kr.or.komca.admin.sample.dto.query.response.SampleExcelList;
import kr.or.komca.admin.sample.dto.query.response.SampleList;
import kr.or.komca.admin.sample.mapper.query.SampleExcelMapper;
import kr.or.komca.admin.sample.mapper.query.SampleQueryMapper;
import kr.or.komca.common.utils.core.dto.response.PageListResponse;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
@RequiredArgsConstructor
public class SampleQueryServiceImpl implements SampleQueryService {

    private final SampleQueryMapper sampleQueryMapper;
    private final SampleExcelMapper sampleExcelMapper;

    @Override
    @Transactional(readOnly = true)
    public PageListResponse<SampleList> getSampleList(SampleListSearch request) {
        long totalElements = sampleQueryMapper.getSampleCount(request);
        int totalPages = PageUtils.calculateTotalPages((int) totalElements, request.getPageSizeOrDefault());
        UserContext userContext = UserContextHolder.getContext();
        KomcaStaff staff = userContext.getStaff();

        return PageListResponse.<SampleList>builder()
                .contents(sampleQueryMapper.getSampleList(request))
                .totalElements(totalElements)
                .totalPages(totalPages)
                .page(request.getPageOrDefault())
                .pageSize(request.getPageSizeOrDefault())
                .build();
    }

    @Override
    @Transactional(readOnly = true)
    public SampleDetail getSampleDetail(SampleDetailSearch request) {
        return sampleQueryMapper.getSampleDetail(request)
                .orElseThrow(() -> new CommonException(CommonErrorCode.NOT_FOUND));
    }

    @Override
    public PageListResponse<SampleExcelList> getSampleExcelList(SampleListSearch request) {
        long totalElements = sampleExcelMapper.getSampleExcelCount(request);
        int totalPages = PageUtils.calculateTotalPages((int) totalElements, request.getPageSizeOrDefault());

        return PageListResponse.<SampleExcelList>builder()
                .contents(sampleExcelMapper.getSampleExcelList(request))
                .totalElements(totalElements)
                .totalPages(totalPages)
                .page(request.getPageOrDefault())
                .pageSize(request.getPageSizeOrDefault())
                .build();
    }


}

