package kr.or.komca.admin.sample.service.command;

import kr.or.komca.common.exception.core.CommonException;
import kr.or.komca.common.exception.response.error.code.CommonErrorCode;
import kr.or.komca.admin.sample.dto.command.request.SampleCreateRequest;
import kr.or.komca.admin.sample.dto.command.request.SampleDeleteRequest;
import kr.or.komca.admin.sample.dto.command.request.SampleUpdateRequest;
import kr.or.komca.admin.sample.dto.command.response.SampleCreate;
import kr.or.komca.admin.sample.dto.command.response.SampleUpdate;
import kr.or.komca.admin.sample.mapper.command.SampleCommandMapper;
import kr.or.komca.admin.sample.mapper.query.SampleQueryMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
@RequiredArgsConstructor
@Slf4j
public class SampleCommandServiceImpl implements SampleCommandService {

    private final SampleCommandMapper sampleCommandMapper;
    private final SampleQueryMapper sampleQueryMapper;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public SampleCreate createSample(SampleCreateRequest request, Long userNo) {
        sampleCommandMapper.createSample(request, userNo);

        return SampleCreate.builder()
                .request(request)
                .userNo(userNo)
                .build();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public SampleUpdate updateSample(SampleUpdateRequest request) {
        if (sampleQueryMapper.sampleNotExists(request.getSampleNo())) {
            throw new CommonException(CommonErrorCode.NOT_FOUND);
        }

        int updateCount = sampleCommandMapper.updateSample(request);

        return SampleUpdate.builder()
                .request(request)
                .updateCount(updateCount)
                .build();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteSample(SampleDeleteRequest request) {
        if (sampleQueryMapper.sampleNotExists(request.getSampleNo())) {
            throw new CommonException(CommonErrorCode.NOT_FOUND);
        }

        sampleCommandMapper.deleteSample(request);
    }
}
