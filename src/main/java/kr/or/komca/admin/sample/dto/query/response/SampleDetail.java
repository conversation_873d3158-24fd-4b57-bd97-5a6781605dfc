package kr.or.komca.admin.sample.dto.query.response;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Builder;
import lombok.Getter;
import lombok.ToString;

import java.time.LocalDateTime;

/**
 * 상세 응답 DTO
 */
@Schema(description = "샘플 상세 응답")
@Getter
@Builder
@ToString
public class SampleDetail {
    /** 샘플 번호 (PK) - IDENTITY 컬럼으로 자동 증가 */
    @Schema(description = "샘플 번호 (PK) - IDENTITY 컬럼으로 자동 증가", example = "1")
    private Long sampleNo;

    /** 사용자 번호 (FK) */
    @Schema(description = "사용자 번호 (FK)", example = "1")
    private Long userNo;

    /** 제목 (최대 100자) */
    @Schema(description = "제목 (최대 100자)", example = "샘플 제목")
    private String title;

    /** 내용 (최대 4000자) */
    @Schema(description = "내용 (최대 4000자)", example = "샘플 내용입니다.")
    private String content;

    /** 수량 */
    @Schema(description = "수량", example = "100")
    private Integer quantity;

    /** 연락처 (형식: 000-0000-0000) */
    @Schema(description = "연락처 (형식: 000-0000-0000)", example = "010-1234-5678")
    private String phoneNumber;

    /** 이메일 주소 */
    @Schema(description = "이메일 주소", example = "<EMAIL>")
    private String email;

    /** 생성일시 - 기본값: SYSTIMESTAMP */
    @Schema(description = "생성일시 - 기본값: SYSTIMESTAMP", example = "2024-02-20T17:53:00")
    private LocalDateTime createdAt;

    /** 수정일시 - 기본값: SYSTIMESTAMP */
    @Schema(description = "수정일시 - 기본값: SYSTIMESTAMP", example = "2024-02-20T17:53:00")
    private LocalDateTime updatedAt;
}
