package kr.or.komca.admin.sample.domain;

import kr.or.komca.admin.sample.dto.command.request.SampleCreateRequest;
import lombok.Builder;
import lombok.Getter;

import java.time.LocalDateTime;

/**
 * 업무로직 복잡도로 DTO 정보 대비 DB 에서 읽어 오는 정보가 너무 많이 필요할 경우 Domain 클래스 사용
 * 가능하면 DTO 만 사용 - 추가 정보는 주석으로 명시해야 함
 */
@Getter
@Builder
public class SampleCreateDomain {
    private Long id;
    private String title;
    private String content;
    private Integer quantity;
    private String phoneNumber;
    private String email;
    private LocalDateTime createdAt;
    private LocalDateTime updatedAt;
    private String createdBy;
    private String address;

    // 도메인 생성 필요시 예제와 같이 만들어 진행
    public static SampleCreateDomain from(SampleCreateRequest request) {
        return SampleCreateDomain.builder()
                .title(request.getTitle())
                .content(request.getContent())
                .quantity(request.getQuantity())
                .phoneNumber(request.getPhoneNumber())
                .email(request.getEmail())
                .createdAt(LocalDateTime.now())
                .updatedAt(LocalDateTime.now())
//                .createdBy()
//                .address()
                .build();
    }
}
