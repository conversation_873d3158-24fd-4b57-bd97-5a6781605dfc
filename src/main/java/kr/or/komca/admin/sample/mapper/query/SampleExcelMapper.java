package kr.or.komca.admin.sample.mapper.query;


import kr.or.komca.admin.sample.dto.query.condition.SampleListSearch;
import kr.or.komca.admin.sample.dto.query.response.SampleExcelList;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 샘플 데이터에 대한 엑셀 리스트를 처리하는 Mapper 인터페이스
 */
@Mapper
public interface SampleExcelMapper {
    /**
     * 검색 조건에 맞는 리스트 조회
     *
     * @param search 리스트 검색 조건 DTO
     * @return 엑셀 리스트 DTO
     */
    List<SampleExcelList> getSampleExcelList(SampleListSearch search);

    /**
     * 리스트 카운트 조회
     *
     * @param search 리스트 검색 조건 DTO
     * @return 리스트 카운트
     */
    long getSampleExcelCount(SampleListSearch search);
}
