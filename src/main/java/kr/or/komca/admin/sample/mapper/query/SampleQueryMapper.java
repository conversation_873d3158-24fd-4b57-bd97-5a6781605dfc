package kr.or.komca.admin.sample.mapper.query;

import kr.or.komca.admin.sample.dto.query.condition.SampleDetailSearch;
import kr.or.komca.admin.sample.dto.query.condition.SampleListSearch;
import kr.or.komca.admin.sample.dto.query.response.SampleDetail;
import kr.or.komca.admin.sample.dto.query.response.SampleList;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Optional;

/**
 * 샘플 데이터에 대한 Query 를 처리하는 Mapper 인터페이스
 */
@Mapper
public interface SampleQueryMapper {

    /**
     * 상세 조회
     *
     * @param request 상세 조회 조건 DTO
     * @return 상세 응답 DTO
     */
    Optional<SampleDetail> getSampleDetail(SampleDetailSearch request);

    /**
     * 존재 여부 체크
     *
     * @param sampleNo 조회 요청 No
     * @return boolean
     */
    boolean sampleNotExists(@Param("sampleNo") Long sampleNo);

    /**
     * 검색 조건에 맞는 리스트 조회
     *
     * @param request 리스트 검색 조건 DTO
     * @return 페이징 처리된 샘플 목록
     */
    List<SampleList> getSampleList(SampleListSearch request);

    /**
     * 리스트 카운트 조회
     * 
     * @param request 리스트 검색 조건 DTO
     * @return 리스트 카운트
     */
    long getSampleCount(SampleListSearch request);
}
