package kr.or.komca.admin.sample.dto.query.condition;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.*;
import kr.or.komca.admin.sample.util.SampleSortColumnValidator;
import lombok.Builder;
import lombok.Getter;

/**
 * 리스트 검색 조건 DTO
 */
@Getter
@Builder
@Schema(description = "샘플 목록 검색 조건 DTO")
public class SampleListSearch {
    @Schema(description = "사용자 번호", example = "1")
    private Long userNo;

    @Schema(description = "사용자 이름 (최대 100자)", example = "홍길동")
    @Size(max = 100, message = "USER_NAME_SIZE")
    private String userName;

    @Schema(description = "제목 검색어 (최대 100자)", example = "검색어")
    @Size(max = 100, message = "TITLE_SIZE")
    private String title;

    @Schema(description = "이메일 주소", example = "<EMAIL>")
    @Email(message = "EMAIL_FORMAT")
    @Size(max = 100, message = "EMAIL_SIZE")
    private String email;

    @Schema(description = "수량 (0-999999)", example = "100")
    @Min(value = 0, message = "QUANTITY_MIN")
    @Max(value = 999999, message = "QUANTITY_MAX")
    private Integer quantity;

    @Schema(description = "검색 시작일 (YYYYMMDD)", example = "20240101")
    @Pattern(regexp = "^\\d{8}$|^$", message = "START_DATE_FORMAT")
    private String startDate;

    @Schema(description = "검색 종료일 (YYYYMMDD)", example = "20241231")
    @Pattern(regexp = "^\\d{8}$|^$", message = "END_DATE_FORMAT")
    private String endDate;

    @Schema(description = "페이지 번호 (1 이상)", example = "1", defaultValue = "1")
    @Min(value = 1, message = "PAGE_MIN")
    private Integer page;

    @Schema(description = "페이지 크기 (1-100)", example = "10", defaultValue = "10")
    @Min(value = 1, message = "SIZE_MIN")
    @Max(value = 100, message = "PAGE_SIZE_MAX")
    private Integer pageSize;

    /** 정렬할 컬럼 **/
    @Schema(description = "정렬할 컬럼", example = "userNo")
    private String sortColumn;

    /** 정렬 순서 1: asc. 2: desc  */
    @Schema(description = "정렬 순서 (1: 오름차순, 2: 내림차순)", example = "1")
    private String sortOrder;

    @SuppressWarnings("unused")
    public String getSortColumn() {
        return SampleSortColumnValidator.validate(this.sortColumn);
    }

    @SuppressWarnings("unused")
    public String getSortOrder() {
        if ("1".equals(this.sortOrder)) {
            return "asc";
        } else if ("2".equals(this.sortOrder)) {
            return "desc";
        }
        return "";
    }

    // 추가: 기본값을 반환하는 편의 메서드
    public Integer getPageOrDefault() {
        return page != null ? page : 1;
    }

    public Integer getPageSizeOrDefault() {
        return pageSize != null ? pageSize : 10;
    }

}