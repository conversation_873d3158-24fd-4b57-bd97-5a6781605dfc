package kr.or.komca.admin.sample.dto.command.response;

import io.swagger.v3.oas.annotations.media.Schema;
import kr.or.komca.admin.sample.dto.command.request.SampleCreateRequest;
import lombok.Builder;
import lombok.Getter;

/**
 * 생성 응답
 */
@Schema(description = "샘플 생성 응답 DTO")
@Builder
@Getter
public class SampleCreate {

    /** 생성 요청 참고 */
    @Schema(description = "생성 요청 참고",
            implementation = SampleCreateRequest.class)
    private SampleCreateRequest request;

    /** 사용자 번호 */
    @Schema(description = "생성한 사용자 번호",
            example = "12345",
            minimum = "1")
    private Long userNo;
}
