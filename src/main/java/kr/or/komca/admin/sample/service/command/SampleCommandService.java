package kr.or.komca.admin.sample.service.command;

import kr.or.komca.admin.sample.dto.command.request.SampleCreateRequest;
import kr.or.komca.admin.sample.dto.command.request.SampleDeleteRequest;
import kr.or.komca.admin.sample.dto.command.request.SampleUpdateRequest;
import kr.or.komca.admin.sample.dto.command.response.SampleCreate;
import kr.or.komca.admin.sample.dto.command.response.SampleUpdate;

/**
 *  샘플 생성, 수정, 삭제 관련 서비스 인터페이스
 */
public interface SampleCommandService {

    /**
     * 생성
     *
     * @param request 생성 요청 DTO
     * @return 생성 요청 정보
     */
    SampleCreate createSample(SampleCreateRequest request, Long userNo);

    /**
     * 수정
     * 
     * @param request 수정 요청 DTO
     * @return 수정 요청 정보
     */
    SampleUpdate updateSample(SampleUpdateRequest request);

    /**
     * 삭제
     * 
     * @param request 삭제 요청 DTO
     */
    void deleteSample(SampleDeleteRequest request);
}
