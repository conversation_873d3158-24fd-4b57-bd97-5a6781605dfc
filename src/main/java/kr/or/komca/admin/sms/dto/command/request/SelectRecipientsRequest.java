package kr.or.komca.admin.sms.dto.command.request;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotEmpty;
import kr.or.komca.admin.sms.dto.command.request.sub.SelectedRecipient;
import lombok.*;

import java.util.List;

/**
 * SB_2에서 선택된 수신자 목록 전달 요청 DTO
 */
@Getter
@NoArgsConstructor
@AllArgsConstructor
@Builder
@ToString
@Schema(description = "선택된 수신자 목록 전달 요청")
public class SelectRecipientsRequest {

    /** 선택된 수신자 목록 */
    @NotEmpty
    @Valid
    @Schema(description = "선택된 수신자 목록")
    private List<SelectedRecipient> selectedRecipients;

    /** 세션 ID (임시 저장용) */
    @Schema(description = "세션 ID", example = "SESSION_12345")
    private String sessionId;

    /** 선택 모드 */
    @Schema(description = "선택 모드", example = "REPLACE", allowableValues = {"REPLACE", "ADD"})
    private String selectMode = "REPLACE"; // REPLACE: 기존 선택 대체, ADD: 기존 선택에 추가
}