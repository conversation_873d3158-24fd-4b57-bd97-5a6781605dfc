package kr.or.komca.admin.sms.service.command;

import kr.or.komca.admin.sms.dto.command.request.SelectRecipientsRequest;
import kr.or.komca.admin.sms.dto.command.request.SendSmsRequest;
import kr.or.komca.admin.sms.dto.command.request.sub.SelectedRecipient;
import kr.or.komca.admin.sms.dto.command.request.sub.SmsRecipient;
import kr.or.komca.admin.sms.dto.command.response.DeleteSelectedRecipient;
import kr.or.komca.admin.sms.dto.command.response.SelectRecipients;
import kr.or.komca.admin.sms.dto.command.response.SendSms;
import kr.or.komca.admin.sms.enums.errorcode.SmsErrorCode;
import kr.or.komca.admin.sms.mapper.command.SmsCommandMapper;
import kr.or.komca.admin.sms.service.query.SmsQueryService;
import kr.or.komca.common.exception.core.CommonException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * SMS Command 서비스 구현체
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class SmsCommandServiceImpl implements SmsCommandService {

    private final SmsCommandMapper smsCommandMapper;
    private final SmsQueryService smsQueryService;

    @Override
    @Transactional
    public SendSms sendSms(SendSmsRequest command, String inspersId) {
        log.info("SMS 발송 요청 - inspersId: {}, 수신자 수: {}", inspersId, command.getRecipients().size());

        validateSmsRequest(command);
        return processSmsMessage(command, inspersId, "S");
    }

    @Override
    @Transactional
    public SendSms sendKakao(SendSmsRequest command, String inspersId) {
        log.info("카카오 알림톡 발송 요청 - inspersId: {}, 수신자 수: {}", inspersId, command.getRecipients().size());

        validateKakaoRequest(command);
        return processSmsMessage(command, inspersId, "A");
    }

    @Override
    @Transactional
    public SelectRecipients selectRecipients(SelectRecipientsRequest command, String inspersId) {
        log.info("수신자 선택 처리 시작 - inspersId: {}, 선택 수: {}", inspersId, command.getSelectedRecipients().size());

        if ("REPLACE".equals(command.getSelectMode()) || command.getSelectMode() == null) {
            smsCommandMapper.deleteSelectedRecipients(inspersId);
            log.debug("기존 선택 목록 삭제 완료 - inspersId: {}", inspersId);
        }

        int savedCount = 0;
        for (SelectedRecipient recipient : command.getSelectedRecipients()) {
            try {
                int affectedRows = smsCommandMapper.insertSelectedRecipient(inspersId, recipient);
                if (affectedRows > 0) {
                    savedCount++;
                }
            } catch (Exception e) {
                log.error("수신자 선택 저장 중 오류 - recipient: {}", recipient.getRecipientId(), e);
            }
        }

        int totalCount = smsCommandMapper.countSelectedRecipients(inspersId);

        log.info("수신자 선택 처리 완료 - 저장: {}건, 전체: {}건", savedCount, totalCount);

        return SelectRecipients.builder()
                .selectedCount(savedCount)
                .totalCount(totalCount)
                .build();
    }

    @Override
    @Transactional
    public DeleteSelectedRecipient removeSelectedRecipient(String recipientId, String recipientType, String delpersId) {
        log.info("선택된 수신자 제거 - recipientId: {}, recipientType: {}, delpersId: {}", 
                recipientId, recipientType, delpersId);

        int affectedRows = smsCommandMapper.deleteSelectedRecipient(delpersId, recipientId, recipientType);

        if (affectedRows <= 0) {
            throw new CommonException(SmsErrorCode.SELECTED_RECIPIENT_NOT_FOUND, 
                    String.format("선택된 수신자를 찾을 수 없습니다. recipientId: %s, recipientType: %s", recipientId, recipientType));
        }

        log.info("선택된 수신자 제거 완료 - recipientId: {}, affectedRows: {}", recipientId, affectedRows);

        return DeleteSelectedRecipient.builder()
                .recipientId(recipientId)
                .recipientType(recipientType)
                .affectedRows(affectedRows)
                .build();
    }

    // ================== Private Methods ==================

    private void validateSmsRequest(SendSmsRequest command) {
        log.debug("SMS 요청 검증 시작");

        if (!smsQueryService.isSmsEnabled()) {
            log.warn("SMS 발송이 비활성화됨");
            throw new CommonException(SmsErrorCode.SMS_SEND_DISABLED, "SMS 발송이 비활성화되어 있습니다.");
        }

        smsQueryService.validateSenderNumber(command.getCallback(), command.getSenderType());
        validateMessageContent(command.getMessage(), "S");

        log.debug("SMS 요청 검증 완료");
    }

    private void validateKakaoRequest(SendSmsRequest command) {
        log.debug("카카오 알림톡 요청 검증 시작");

        if (!smsQueryService.isKakaoEnabled()) {
            log.warn("카카오 알림톡 발송이 비활성화됨");
            throw new CommonException(SmsErrorCode.SMS_SEND_DISABLED, "카카오 알림톡 발송이 비활성화되어 있습니다.");
        }

        smsQueryService.validateTemplateCode(command.getTemplateCode());
        validateMessageContent(command.getMessage(), "A");

        log.debug("카카오 알림톡 요청 검증 완료");
    }

    private void validateMessageContent(String message, String channel) {
        if (message == null || message.trim().isEmpty()) {
            throw new CommonException(SmsErrorCode.INVALID_MESSAGE_CONTENT, "메시지 내용이 비어있습니다.");
        }

        int maxLength = "S".equals(channel) ? 90 : 1000;
        if (message.getBytes().length > maxLength) {
            throw new CommonException(SmsErrorCode.INVALID_MESSAGE_CONTENT,
                    String.format("메시지 길이가 제한을 초과했습니다. 최대: %d바이트", maxLength));
        }
    }

    private SendSms processSmsMessage(SendSmsRequest command, String inspersId, String channel) {
        log.info("메시지 발송 처리 시작 - channel: {}, 수신자 수: {}", channel, command.getRecipients().size());

        int successCount = 0;
        int failCount = 0;
        int fallbackSmsCount = 0;

        for (SmsRecipient recipient : command.getRecipients()) {
            try {
                int affectedRows = smsCommandMapper.insertSmsMessage(inspersId, command, recipient, channel);
                
                if (affectedRows > 0) {
                    boolean sendResult = sendToGateway(command, recipient, channel);
                    
                    if (sendResult) {
                        successCount++;
                        log.debug("발송 성공 - recipient: {}, channel: {}", recipient.getPhoneNumber(), channel);
                    } else {
                        if ("A".equals(channel)) {
                            boolean fallbackResult = sendToGateway(command, recipient, "S");
                            if (fallbackResult) {
                                fallbackSmsCount++;
                                log.info("SMS 대체발송 성공 - recipient: {}", recipient.getPhoneNumber());
                            } else {
                                failCount++;
                                log.warn("SMS 대체발송도 실패 - recipient: {}", recipient.getPhoneNumber());
                            }
                        } else {
                            failCount++;
                        }
                    }
                } else {
                    failCount++;
                    log.warn("DB 저장 실패 - recipient: {}", recipient.getPhoneNumber());
                }
            } catch (Exception e) {
                log.error("메시지 발송 중 오류 발생 - recipient: {}", recipient.getPhoneNumber(), e);
                failCount++;
            }
        }

        log.info("메시지 발송 처리 완료 - 성공: {}건, 실패: {}건, SMS대체: {}건", 
                successCount, failCount, fallbackSmsCount);

        return SendSms.builder()
                .sentCount(successCount)
                .failedCount(failCount)
                .totalRecipients(command.getRecipients().size())
                .fallbackSmsCount(fallbackSmsCount)
                .build();
    }

    private boolean sendToGateway(SendSmsRequest command, SmsRecipient recipient, String channel) {
        try {
            if ("S".equals(channel)) {
                log.info("Mock SMS 발송 - to: {}, message: {}", recipient.getPhoneNumber(), command.getMessage());
                return true; // Mock에서는 항상 성공
            } else {
                log.info("Mock 카카오 알림톡 발송 - to: {}, template: {}", 
                        recipient.getPhoneNumber(), command.getTemplateCode());
                
                // Mock에서는 20% 확률로 실패 (SMS 대체발송 테스트용)
                return Math.random() > 0.2;
            }
        } catch (Exception e) {
            log.error("게이트웨이 호출 중 오류 발생", e);
            return false;
        }
    }
}