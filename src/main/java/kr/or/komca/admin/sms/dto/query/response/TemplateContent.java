package kr.or.komca.admin.sms.dto.query.response;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Builder;
import lombok.Getter;
import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 템플릿 내용 DTO
 */
@Getter
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Schema(description = "템플릿 내용")
public class TemplateContent {
    
    /** 템플릿 코드 */
    @Schema(description = "템플릿 코드", example = "MEM_24")
    private String tmplCd;

    /** 템플릿명 */
    @Schema(description = "템플릿명", example = "신탁입금안내")
    private String cdNm;

    /** 템플릿 내용 */
    @Schema(description = "템플릿 내용", example = "[한국음악저작권협회]\\n-입금요청-\\n수협은행 1010-2313-7868\\n#{신탁종류} #{금액(사유)}")
    private String tmplContent;

    /** 템플릿 변수 목록 */
    @Schema(description = "템플릿 변수 목록", example = "[\"#{신탁종류}\", \"#{금액(사유)}\"]")
    private String tmplVariables;

    /** 카테고리 */
    @Schema(description = "카테고리", example = "MEM")
    private String category;

    /** 사용여부 */
    @Schema(description = "사용여부", example = "Y")
    private String useYn;
}