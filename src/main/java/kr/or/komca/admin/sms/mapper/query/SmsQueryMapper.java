package kr.or.komca.admin.sms.mapper.query;

import kr.or.komca.admin.sms.dto.query.condition.RecipientSearchCondition;
import kr.or.komca.admin.sms.dto.query.response.RecipientSearchResult;
import kr.or.komca.admin.sms.dto.query.response.SelectedRecipientList;
import kr.or.komca.admin.sms.dto.query.response.SenderNumber;
import kr.or.komca.admin.sms.dto.query.response.TemplateInfo;
import kr.or.komca.admin.sms.dto.query.response.TemplateContent;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * SMS Query Mapper 인터페이스
 */
@Mapper
public interface SmsQueryMapper {

    /**
     * 발신번호 목록 조회
     */
    List<SenderNumber> getSenderNumbers();

    /**
     * 템플릿 목록 조회 (공통코드 기반)
     */
    List<TemplateInfo> getTemplates();

    /**
     * 템플릿 내용 조회 (DB 기반)
     */
    TemplateContent getTemplateContent(@Param("templateCode") String templateCode);

    /**
     * SMS 발송 활성화 플래그 조회
     */
    String getSmsEnabledFlag();

    /**
     * 카카오 발송 활성화 플래그 조회
     */
    String getKakaoEnabledFlag();

    /**
     * 수신자 검색
     */
    List<RecipientSearchResult> searchRecipients(@Param("condition") RecipientSearchCondition condition);

    /**
     * 발신번호 존재 여부 확인
     */
    boolean existsSenderNumber(@Param("phoneNumber") String phoneNumber, @Param("senderType") String senderType);

    /**
     * 템플릿 코드 존재 여부 확인
     */
    boolean existsTemplateCode(@Param("templateCode") String templateCode);

    /**
     * 선택된 수신자 목록 조회 (SB_1용)
     */
    List<SelectedRecipientList.SelectedRecipientInfo> getSelectedRecipients(
            @Param("userId") String userId
    );
}