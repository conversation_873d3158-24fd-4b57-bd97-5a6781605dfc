package kr.or.komca.admin.sms.dto.command.response;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;

/**
 * SMS 발송 응답 DTO
 */
@NoArgsConstructor
@AllArgsConstructor
@Getter
@Builder
@Schema(description = "SMS 발송 응답")
public class SendSms {

    /** 발송 성공 건수 */
    @Schema(description = "발송 성공 건수", example = "5")
    private int sentCount;

    /** 발송 실패 건수 */
    @Schema(description = "발송 실패 건수", example = "1")
    private int failedCount;

    /** 총 수신자 수 */
    @Schema(description = "총 수신자 수", example = "6")
    private int totalRecipients;

    /** SMS 대체발송 건수 */
    @Schema(description = "SMS 대체발송 건수", example = "2")
    private int fallbackSmsCount;
}