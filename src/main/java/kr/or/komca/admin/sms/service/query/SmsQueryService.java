package kr.or.komca.admin.sms.service.query;

import kr.or.komca.admin.sms.dto.query.condition.RecipientSearchCondition;
import kr.or.komca.admin.sms.dto.query.response.RecipientSearchResult;
import kr.or.komca.admin.sms.dto.query.response.SelectedRecipientList;
import kr.or.komca.admin.sms.dto.query.response.SenderNumber;
import kr.or.komca.admin.sms.dto.query.response.TemplateInfo;
import kr.or.komca.admin.sms.dto.query.response.TemplateContent;
import kr.or.komca.common.utils.core.dto.response.ListResponse;
import kr.or.komca.common.utils.core.dto.response.PageListResponse;

/**
 * SMS Query 서비스 인터페이스
 */
public interface SmsQueryService {

    /**
     * 발신번호 목록 조회
     */
    ListResponse<SenderNumber> getSenderNumbers();

    /**
     * 템플릿 목록 조회 (공통코드 기반)
     */
    ListResponse<TemplateInfo> getTemplates();

    /**
     * 템플릿 내용 조회 (DB 기반)
     */
    TemplateContent getTemplateContent(String templateCode);

    /**
     * SMS 수신자 검색
     */
    PageListResponse<RecipientSearchResult> searchRecipients(RecipientSearchCondition condition);

    /**
     * SMS 발송 가능 여부 확인
     */
    boolean isSmsEnabled();

    /**
     * 카카오 알림톡 발송 가능 여부 확인
     */
    boolean isKakaoEnabled();

    /**
     * 발신번호 유효성 검증
     */
    void validateSenderNumber(String phoneNumber, String senderType);

    /**
     * 템플릿 코드 유효성 검증
     */
    void validateTemplateCode(String templateCode);

    /**
     * 선택된 수신자 목록 조회 (SB_1용)
     */
    SelectedRecipientList getSelectedRecipients(String userId);
}