package kr.or.komca.admin.sms.dto.query.response;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Builder;
import lombok.Getter;
import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;

/**
 * 템플릿 목록 정보 DTO
 */
@Getter
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Schema(description = "템플릿 목록 정보")
public class TemplateInfo {
    
    /** 템플릿 코드 */
    @Schema(description = "템플릿 코드", example = "MEM_24")
    private String templateCode;
    
    /** 템플릿명 */
    @Schema(description = "템플릿명", example = "신탁입금안내")
    private String templateName;
    
    /** 카테고리 */
    @Schema(description = "카테고리", example = "MEM")
    private String category;
    
    /** 사용여부 */
    @Schema(description = "사용여부", example = "Y")
    private String useYn;
}