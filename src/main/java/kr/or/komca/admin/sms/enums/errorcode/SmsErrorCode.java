package kr.or.komca.admin.sms.enums.errorcode;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpStatus;
import kr.or.komca.common.interfaces.response.code.ErrorCode;

/**
 * SMS 관리에 대한 ErrorEnum 처리
 * 1. 상수명: 대문자 스네이크 케이스로 작성 (예: SMS_SEND_DISABLED)
 * 2. code: 상수명과 동일하게 문자열로 작성
 * 3. status: 적절한 HTTP 상태 코드 설정 (HttpStatus enum 사용)
 */
@Getter
@RequiredArgsConstructor
public enum SmsErrorCode implements ErrorCode {

    /**
     * SMS 발송이 비활성화된 경우 발생하는 예외
     * 발생 상황:
     * - TENV_FLAG에서 SMS_SEND_ENABLED가 'N'인 경우
     * - 시스템 점검 등으로 SMS 기능이 일시 중지된 경우
     */
    @Schema(description = "SMS 발송이 비활성화됨")
    SMS_SEND_DISABLED("SMS_SEND_DISABLED", HttpStatus.BAD_REQUEST),

    /**
     * 유효하지 않은 전화번호 형식인 경우 발생하는 예외
     * 발생 상황:
     * - 전화번호 형식이 올바르지 않은 경우
     * - 허용되지 않는 번호 체계인 경우
     */
    @Schema(description = "유효하지 않은 전화번호")
    INVALID_PHONE_NUMBER("INVALID_PHONE_NUMBER", HttpStatus.BAD_REQUEST),

    /**
     * SMS 발송이 실패한 경우 발생하는 예외
     * 발생 상황:
     * - SMS 게이트웨이 호출 실패
     * - 네트워크 오류 또는 외부 서비스 장애
     */
    @Schema(description = "SMS 발송 실패")
    SMS_SEND_FAILED("SMS_SEND_FAILED", HttpStatus.INTERNAL_SERVER_ERROR),

    /**
     * 템플릿을 찾을 수 없는 경우 발생하는 예외
     * 발생 상황:
     * - 존재하지 않는 템플릿 코드 요청
     * - 비활성화된 템플릿 접근
     */
    @Schema(description = "템플릿을 찾을 수 없음")
    TEMPLATE_NOT_FOUND("TEMPLATE_NOT_FOUND", HttpStatus.NOT_FOUND),

    /**
     * 수신자를 찾을 수 없는 경우 발생하는 예외
     * 발생 상황:
     * - 거래처/회원 정보가 존재하지 않는 경우
     * - 전화번호 정보가 없는 경우
     */
    @Schema(description = "수신자를 찾을 수 없음")
    RECIPIENT_NOT_FOUND("RECIPIENT_NOT_FOUND", HttpStatus.NOT_FOUND),

    /**
     * SMS 발송 권한이 없는 경우 발생하는 예외
     * 발생 상황:
     * - 사용자에게 SMS 발송 권한이 없는 경우
     * - 메뉴 권한 검사 실패
     */
    @Schema(description = "SMS 발송 권한 없음")
    SMS_PERMISSION_DENIED("SMS_PERMISSION_DENIED", HttpStatus.FORBIDDEN),

    /**
     * 카카오 알림톡 발송이 실패한 경우 발생하는 예외
     * 발생 상황:
     * - 카카오 API 호출 실패
     * - 템플릿 코드 오류 또는 발신자키 문제
     */
    @Schema(description = "카카오 알림톡 발송 실패")
    KAKAO_SEND_FAILED("KAKAO_SEND_FAILED", HttpStatus.INTERNAL_SERVER_ERROR),

    /**
     * 유효하지 않은 템플릿 코드인 경우 발생하는 예외
     * 발생 상황:
     * - 존재하지 않는 템플릿 코드
     * - 승인되지 않은 템플릿 사용 시도
     */
    @Schema(description = "유효하지 않은 템플릿 코드")
    INVALID_TEMPLATE_CODE("INVALID_TEMPLATE_CODE", HttpStatus.BAD_REQUEST),

    /**
     * 발신번호가 유효하지 않은 경우 발생하는 예외
     * 발생 상황:
     * - 등록되지 않은 발신번호 사용
     * - TENV_CODE에 존재하지 않는 발신번호
     */
    @Schema(description = "유효하지 않은 발신번호")
    INVALID_SENDER_NUMBER("INVALID_SENDER_NUMBER", HttpStatus.BAD_REQUEST),

    /**
     * 메시지 내용이 유효하지 않은 경우 발생하는 예외
     * 발생 상황:
     * - 메시지 길이 초과 (SMS: 90바이트, LMS: 2000바이트)
     * - 금지 단어 포함 등
     */
    @Schema(description = "유효하지 않은 메시지 내용")
    INVALID_MESSAGE_CONTENT("INVALID_MESSAGE_CONTENT", HttpStatus.BAD_REQUEST),

    /**
     * 선택된 수신자를 찾을 수 없는 경우 발생하는 예외
     * 발생 상황:
     * - 삭제하려는 선택된 수신자가 존재하지 않는 경우
     * - 이미 삭제된 수신자를 다시 삭제하려는 경우
     */
    @Schema(description = "선택된 수신자를 찾을 수 없음")
    SELECTED_RECIPIENT_NOT_FOUND("SELECTED_RECIPIENT_NOT_FOUND", HttpStatus.NOT_FOUND);

    private final String code;
    private final HttpStatus status;

}