package kr.or.komca.admin.sms.dto.query.response;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Builder;
import lombok.Getter;
import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;

/**
 * 발신번호 정보 DTO
 */
@Getter
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Schema(description = "발신번호 정보")
public class SenderNumber {
    
    /** 발신번호구분 */
    @Schema(description = "발신번호구분", example = "01")
    private String senderType;
    
    /** 발신번호구분명 */
    @Schema(description = "발신번호구분명", example = "대표번호")
    private String senderTypeName;
    
    /** 발신번호 */
    @Schema(description = "발신번호", example = "02-2660-0400")
    private String phoneNumber;
    
    /** 사용여부 */
    @Schema(description = "사용여부", example = "Y")
    private String useYn;
}