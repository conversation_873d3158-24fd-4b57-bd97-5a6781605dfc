package kr.or.komca.admin.sms.service.command;

import kr.or.komca.admin.sms.dto.command.request.SelectRecipientsRequest;
import kr.or.komca.admin.sms.dto.command.request.SendSmsRequest;
import kr.or.komca.admin.sms.dto.command.response.DeleteSelectedRecipient;
import kr.or.komca.admin.sms.dto.command.response.SelectRecipients;
import kr.or.komca.admin.sms.dto.command.response.SendSms;

/**
 * SMS Command 서비스 인터페이스
 */
public interface SmsCommandService {

    /**
     * SMS 발송
     */
    SendSms sendSms(SendSmsRequest command, String inspersId);

    /**
     * 카카오 알림톡 발송
     */
    SendSms sendKakao(SendSmsRequest command, String inspersId);

    /**
     * SB_2에서 선택된 수신자 목록 저장
     */
    SelectRecipients selectRecipients(SelectRecipientsRequest command, String inspersId);

    /**
     * 선택된 수신자 목록에서 특정 수신자 제거
     */
    DeleteSelectedRecipient removeSelectedRecipient(String recipientId, String recipientType, String delpersId);
}