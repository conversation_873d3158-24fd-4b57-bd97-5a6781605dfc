package kr.or.komca.admin.sms.api;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import kr.or.komca.admin.sms.dto.command.request.SelectRecipientsRequest;
import kr.or.komca.admin.sms.dto.command.request.SendSmsRequest;
import kr.or.komca.admin.sms.dto.command.response.DeleteSelectedRecipient;
import kr.or.komca.admin.sms.dto.command.response.SelectRecipients;
import kr.or.komca.admin.sms.dto.command.response.SendSms;
import kr.or.komca.admin.sms.dto.query.condition.RecipientSearchCondition;
import kr.or.komca.admin.sms.dto.query.response.RecipientSearchResult;
import kr.or.komca.admin.sms.dto.query.response.SelectedRecipientList;
import kr.or.komca.admin.sms.dto.query.response.SenderNumber;
import kr.or.komca.admin.sms.dto.query.response.TemplateInfo;
import kr.or.komca.admin.sms.dto.query.response.TemplateContent;
import kr.or.komca.common.utils.core.dto.response.ListResponse;
import kr.or.komca.common.exception.response.error.CommonErrorResponse;
import kr.or.komca.common.exception.response.success.CommonSuccessResponse;
import kr.or.komca.common.utils.core.dto.response.PageListResponse;
import org.springframework.http.ResponseEntity;

/**
 * SMS API 인터페이스
 */
@Tag(name = "SMS", description = "SMS/알림톡 발송 관리 API")
public interface SmsApi {

    /** ======== Query API ======== */

    @Operation(
            summary = "발신번호 목록 조회",
            description = "SMS 발송 시 사용할 수 있는 발신번호 목록을 조회합니다."
    )
    @ApiResponses({
            @ApiResponse(responseCode = "200", description = "조회 성공"),
            @ApiResponse(responseCode = "400", description = "잘못된 요청",
                    content = @Content(schema = @Schema(implementation = CommonErrorResponse.class)))
    })
    ResponseEntity<CommonSuccessResponse<ListResponse<SenderNumber>>> getSenderNumbers();

    @Operation(
            summary = "템플릿 목록 조회",
            description = "사용 가능한 SMS/알림톡 템플릿 목록을 조회합니다."
    )
    @ApiResponses({
            @ApiResponse(responseCode = "200", description = "조회 성공"),
            @ApiResponse(responseCode = "400", description = "잘못된 요청",
                    content = @Content(schema = @Schema(implementation = CommonErrorResponse.class)))
    })
    ResponseEntity<CommonSuccessResponse<ListResponse<TemplateInfo>>> getTemplates();

    @Operation(
            summary = "템플릿 내용 조회",
            description = "특정 템플릿의 상세 내용을 조회합니다."
    )
    @ApiResponses({
            @ApiResponse(responseCode = "200", description = "조회 성공"),
            @ApiResponse(responseCode = "400", description = "잘못된 요청",
                    content = @Content(schema = @Schema(implementation = CommonErrorResponse.class))),
            @ApiResponse(responseCode = "404", description = "템플릿 없음",
                    content = @Content(schema = @Schema(implementation = CommonErrorResponse.class)))
    })
    ResponseEntity<CommonSuccessResponse<TemplateContent>> getTemplateContent(
            @Parameter(description = "템플릿 코드") String templateCode
    );

    @Operation(
            summary = "SMS 수신자 검색",
            description = "SMS 발송 대상자를 검색합니다. 거래처/회원 통합 검색을 지원합니다."
    )
    @ApiResponses({
            @ApiResponse(responseCode = "200", description = "조회 성공"),
            @ApiResponse(responseCode = "400", description = "잘못된 요청",
                    content = @Content(schema = @Schema(implementation = CommonErrorResponse.class)))
    })
    ResponseEntity<CommonSuccessResponse<PageListResponse<RecipientSearchResult>>> searchRecipients(
            @Parameter(description = "수신자 검색 조건") RecipientSearchCondition condition
    );

    @Operation(
            summary = "선택된 수신자 목록 조회",
            description = "SB_1 화면에서 표시할 선택된 수신자 목록을 조회합니다."
    )
    @ApiResponses({
            @ApiResponse(responseCode = "200", description = "조회 성공"),
            @ApiResponse(responseCode = "400", description = "잘못된 요청",
                    content = @Content(schema = @Schema(implementation = CommonErrorResponse.class)))
    })
    ResponseEntity<CommonSuccessResponse<SelectedRecipientList>> getSelectedRecipients();

    /** ======== Command API ======== */

    @Operation(
            summary = "SMS 발송",
            description = "SMS 메시지를 발송합니다. 발송 채널에 따라 SMS 또는 알림톡으로 처리됩니다."
    )
    @ApiResponses({
            @ApiResponse(responseCode = "200", description = "발송 성공"),
            @ApiResponse(responseCode = "400", description = "잘못된 요청",
                    content = @Content(schema = @Schema(implementation = CommonErrorResponse.class))),
            @ApiResponse(responseCode = "403", description = "발송 권한 없음",
                    content = @Content(schema = @Schema(implementation = CommonErrorResponse.class))),
            @ApiResponse(responseCode = "500", description = "발송 실패",
                    content = @Content(schema = @Schema(implementation = CommonErrorResponse.class)))
    })
    ResponseEntity<CommonSuccessResponse<SendSms>> sendSms(
            @Parameter(description = "SMS 발송 요청") SendSmsRequest command
    );

    @Operation(
            summary = "수신자 선택 결과 저장",
            description = "SB_2에서 선택된 수신자 목록을 임시 저장합니다."
    )
    @ApiResponses({
            @ApiResponse(responseCode = "200", description = "저장 성공"),
            @ApiResponse(responseCode = "400", description = "잘못된 요청",
                    content = @Content(schema = @Schema(implementation = CommonErrorResponse.class)))
    })
    ResponseEntity<CommonSuccessResponse<SelectRecipients>> selectRecipients(
            @Parameter(description = "선택된 수신자 목록") SelectRecipientsRequest command
    );

    @Operation(
            summary = "선택된 수신자 제거",
            description = "선택된 수신자 목록에서 특정 수신자를 제거합니다."
    )
    @ApiResponses({
            @ApiResponse(responseCode = "200", description = "제거 성공"),
            @ApiResponse(responseCode = "400", description = "잘못된 요청",
                    content = @Content(schema = @Schema(implementation = CommonErrorResponse.class)))
    })
    ResponseEntity<CommonSuccessResponse<DeleteSelectedRecipient>> removeSelectedRecipient(
            @Parameter(description = "수신자 ID") String recipientId,
            @Parameter(description = "수신자 타입") String recipientType
    );
}