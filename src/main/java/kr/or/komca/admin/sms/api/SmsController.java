package kr.or.komca.admin.sms.api;

import jakarta.validation.Valid;
import kr.or.komca.admin.sms.dto.command.request.SelectRecipientsRequest;
import kr.or.komca.admin.sms.dto.command.request.SendSmsRequest;
import kr.or.komca.admin.sms.dto.command.response.DeleteSelectedRecipient;
import kr.or.komca.admin.sms.dto.command.response.SelectRecipients;
import kr.or.komca.admin.sms.dto.command.response.SendSms;
import kr.or.komca.admin.sms.dto.query.condition.RecipientSearchCondition;
import kr.or.komca.admin.sms.dto.query.response.RecipientSearchResult;
import kr.or.komca.admin.sms.dto.query.response.SelectedRecipientList;
import kr.or.komca.admin.sms.dto.query.response.SenderNumber;
import kr.or.komca.admin.sms.dto.query.response.TemplateInfo;
import kr.or.komca.admin.sms.dto.query.response.TemplateContent;
import kr.or.komca.common.utils.core.dto.response.ListResponse;
import kr.or.komca.admin.sms.service.command.SmsCommandService;
import kr.or.komca.admin.sms.service.query.SmsQueryService;
import kr.or.komca.common.auth.support.utils.context.UserContextHolder;
import kr.or.komca.common.exception.response.success.CommonSuccessResponse;
import kr.or.komca.common.utils.core.dto.response.PageListResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

/**
 * SMS 발송 관리 REST API Controller
 */
@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("/api/v1/sms")
public class SmsController implements SmsApi {

    private final SmsQueryService smsQueryService;
    private final SmsCommandService smsCommandService;

    /** ======== Query API ======== */

    @Override
    @GetMapping("/sender-numbers")
    public ResponseEntity<CommonSuccessResponse<ListResponse<SenderNumber>>> getSenderNumbers() {
        log.info("발신번호 목록 조회 요청");
        
        ListResponse<SenderNumber> result = smsQueryService.getSenderNumbers();
        return CommonSuccessResponse.ok(result);
    }

    @Override
    @GetMapping("/templates")
    public ResponseEntity<CommonSuccessResponse<ListResponse<TemplateInfo>>> getTemplates() {
        log.info("템플릿 목록 조회 요청");
        
        ListResponse<TemplateInfo> result = smsQueryService.getTemplates();
        return CommonSuccessResponse.ok(result);
    }

    @Override
    @GetMapping("/templates/{templateCode}")
    public ResponseEntity<CommonSuccessResponse<TemplateContent>> getTemplateContent(
            @PathVariable String templateCode
    ) {
        log.info("템플릿 내용 조회 요청 - templateCode: {}", templateCode);
        
        TemplateContent result = smsQueryService.getTemplateContent(templateCode);
        return CommonSuccessResponse.ok(result);
    }

    @Override
    @GetMapping("/recipients")
    public ResponseEntity<CommonSuccessResponse<PageListResponse<RecipientSearchResult>>> searchRecipients(
            @ModelAttribute @Valid RecipientSearchCondition condition
    ) {
        log.info("SMS 수신자 검색 요청: {}", condition);
        
        PageListResponse<RecipientSearchResult> result = smsQueryService.searchRecipients(condition);
        return CommonSuccessResponse.ok(result);
    }

    @Override
    @GetMapping("/selected")
    public ResponseEntity<CommonSuccessResponse<SelectedRecipientList>> getSelectedRecipients() {
        log.info("선택된 수신자 목록 조회 요청");
        
        String userId = UserContextHolder.getContext().getUserId();
        
        SelectedRecipientList result = smsQueryService.getSelectedRecipients(userId);
        return CommonSuccessResponse.ok(result);
    }

    /** ======== Command API ======== */

    @Override
    @PostMapping("/send")
    public ResponseEntity<CommonSuccessResponse<SendSms>> sendSms(
            @Valid @RequestBody SendSmsRequest command
    ) {
        log.info("SMS 발송 요청: {}", command);

        String inspersId = UserContextHolder.getContext().getUserId();
        
        SendSms result;
        if ("A".equals(command.getChannel())) {
            // 알림톡 발송
            result = smsCommandService.sendKakao(command, inspersId);
        } else {
            // SMS 발송
            result = smsCommandService.sendSms(command, inspersId);
        }
        
        return CommonSuccessResponse.ok(result);
    }

    @Override
    @PostMapping("/select")
    public ResponseEntity<CommonSuccessResponse<SelectRecipients>> selectRecipients(
            @Valid @RequestBody SelectRecipientsRequest command
    ) {
        log.info("수신자 선택 결과 저장 요청: {}", command);

        String inspersId = UserContextHolder.getContext().getUserId();
        SelectRecipients result = smsCommandService.selectRecipients(command, inspersId);
        
        return CommonSuccessResponse.ok(result);
    }

    @Override
    @PostMapping("/selected/{recipientId}/{recipientType}/delete")
    public ResponseEntity<CommonSuccessResponse<DeleteSelectedRecipient>> removeSelectedRecipient(
            @PathVariable String recipientId,
            @PathVariable String recipientType
    ) {
        log.info("선택된 수신자 제거 요청 - recipientId: {}, recipientType: {}", 
                recipientId, recipientType);

        String delpersId = UserContextHolder.getContext().getUserId();
        DeleteSelectedRecipient result = smsCommandService.removeSelectedRecipient(recipientId, recipientType, delpersId);
        
        return CommonSuccessResponse.ok(result);
    }
}