package kr.or.komca.admin.sms.dto.query.response;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Builder;
import lombok.Getter;
import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;

/**
 * SMS 수신자 검색 결과 응답 DTO
 */
@Getter
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Schema(description = "SMS 수신자 검색 결과 응답 DTO")
public class RecipientSearchResult {

    /** 수신자 ID */
    @Schema(description = "수신자 ID", example = "BSCON001")
    private String bsconCd;

    /** 수신자 타입 */
    @Schema(description = "수신자 타입", example = "BSCON", allowableValues = {"BSCON", "MEMBER"})
    private String recipientType;

    /** 이름/상호 */
    @Schema(description = "이름/상호", example = "한국음악저작권협회")
    private String bsconhanNm;

    /** 대표자명 */
    @Schema(description = "대표자명", example = "홍길동")
    private String reppresNm;

    /** 전화번호1 */
    @Schema(description = "전화번호1", example = "02-2660-0400")
    private String phoneNumber1;

    /** 전화번호2 */
    @Schema(description = "전화번호2", example = "02-2660-0401")
    private String phoneNumber2;

    /** SMS 수신동의 */
    @Schema(description = "SMS 수신동의", example = "Y", allowableValues = {"Y", "N"})
    private String smsReceiveYn;

    /** 업종 코드 */
    @Schema(description = "업종 코드", example = "001")
    private String mstrMdmCd;
}