package kr.or.komca.admin.sms.dto.query.response;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Builder;
import lombok.Getter;
import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * SB_1에서 표시할 선택된 수신자 목록 응답 DTO
 */
@Getter
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Schema(description = "선택된 수신자 목록 응답")
public class SelectedRecipientList {

    /** 선택된 수신자 목록 */
    @Schema(description = "선택된 수신자 목록")
    private List<SelectedRecipientInfo> recipients;

    /** 총 선택된 수신자 수 */
    @Schema(description = "총 선택된 수신자 수", example = "5")
    private int totalCount;

    /** 세션 ID */
    @Schema(description = "세션 ID", example = "SESSION_12345")
    private String sessionId;

    /**
     * 선택된 수신자 정보
     */
    @Getter
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    @Schema(description = "선택된 수신자 정보")
    public static class SelectedRecipientInfo {
        
        /** 수신자 ID */
        @Schema(description = "수신자 ID", example = "BSCON001")
        private String recipientId;
        
        /** 수신자 타입 */
        @Schema(description = "수신자 타입", example = "BSCON")
        private String recipientType;
        
        /** 수신자명 */
        @Schema(description = "수신자명", example = "한국음악저작권협회")
        private String recipientName;
        
        /** 선택된 전화번호 */
        @Schema(description = "선택된 전화번호", example = "02-2660-0400")
        private String selectedPhoneNumber;
        
        /** 전화번호 타입 */
        @Schema(description = "전화번호 타입", example = "PHONE1")
        private String phoneNumberType;
        
        /** 선택 일시 */
        @Schema(description = "선택 일시", example = "2024-01-15 10:30:25")
        private String selectedDate;
    }
}