package kr.or.komca.admin.sms.util;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

/**
 * SMS 수신자 검색 정렬 컬럼 검증기
 */
@Slf4j
@Component
public class RecipientSortColumnValidator {

	/**
	 * 허용된 정렬 컬럼 매핑 정보
	 * Key: 프론트엔드 요청 파라미터명
	 * Value: 실제 DB 테이블의 컬럼명
	 */
	private static final Map<String, String> ALLOWED_SORT_COLUMNS_MAP = new HashMap<>() {{
		put("recipientId", "recipient_id");                         // 수신자ID
		put("recipientType", "recipient_type");                     // 수신자타입
		put("name", "name");                                        // 이름/상호
		put("representativeName", "representative_name");           // 대표자명
		put("phoneNumber1", "phone_number1");                       // 전화번호1
		put("phoneNumber2", "phone_number2");                       // 전화번호2
		put("smsReceiveYn", "sms_receive_yn");                      // SMS수신동의
		put("businessCode", "business_code");                       // 업종코드
	}};

	/**
	 * 정렬 컬럼 유효성 검사 및 매핑
	 */
	public static String validate(String sortColumn) {
		return ALLOWED_SORT_COLUMNS_MAP.get(sortColumn);
	}
}