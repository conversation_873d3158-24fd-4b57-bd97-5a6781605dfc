package kr.or.komca.admin.sms.service.query;

import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import kr.or.komca.admin.sms.dto.query.condition.RecipientSearchCondition;
import kr.or.komca.admin.sms.dto.query.response.RecipientSearchResult;
import kr.or.komca.admin.sms.dto.query.response.SelectedRecipientList;
import kr.or.komca.admin.sms.dto.query.response.SenderNumber;
import kr.or.komca.admin.sms.dto.query.response.TemplateInfo;
import kr.or.komca.admin.sms.dto.query.response.TemplateContent;
import kr.or.komca.common.utils.core.dto.response.ListResponse;
import kr.or.komca.admin.sms.enums.errorcode.SmsErrorCode;
import kr.or.komca.admin.sms.mapper.query.SmsQueryMapper;
import kr.or.komca.common.exception.core.CommonException;
import kr.or.komca.common.utils.core.dto.response.PageListResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * SMS Query 서비스 구현체
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class SmsQueryServiceImpl implements SmsQueryService {

    private final SmsQueryMapper smsQueryMapper;

    /**
     * 발신번호 목록 조회
     * 
     * TODO: 실제 데이터 연동 시 필요한 작업
     * - TENV_CODE 테이블에 실제 발신번호 등록 필요 (HIGH_CD='SMS_SENDER')
     */
    @Override
    @Transactional(readOnly = true)
    public ListResponse<SenderNumber> getSenderNumbers() {
        log.info("발신번호 목록 조회 요청");

        List<SenderNumber> senderNumbers = smsQueryMapper.getSenderNumbers();
        return ListResponse.<SenderNumber>builder()
                .contents(senderNumbers)
                .totalElements((long) senderNumbers.size())
                .build();
    }

    /**
     * 템플릿 목록 조회 (공통코드 기반)
     * 
     * TODO: 실제 데이터 연동 시 필요한 작업
     * - TENV_CODE 테이블에 SMS_TEMPLATE 코드 등록 필요
     */
    @Override
    @Transactional(readOnly = true)
    public ListResponse<TemplateInfo> getTemplates() {
        log.info("템플릿 목록 조회 요청");

        List<TemplateInfo> templates = smsQueryMapper.getTemplates();
        return ListResponse.<TemplateInfo>builder()
                .contents(templates)
                .totalElements((long) templates.size())
                .build();
    }

    /**
     * 템플릿 내용 조회 (DB 기반)
     * 
     * TODO: 실제 데이터 연동 시 필요한 작업
     * - TENV_KAKAO_TEMPLATE 테이블 생성 및 템플릿 내용 등록 필요
     */
    @Override
    @Transactional(readOnly = true)
    public TemplateContent getTemplateContent(String templateCode) {
        log.info("템플릿 내용 조회 요청 - templateCode: {}", templateCode);

        TemplateContent templateContent = smsQueryMapper.getTemplateContent(templateCode);
        if (templateContent == null) {
            log.warn("템플릿을 찾을 수 없습니다 - templateCode: {}", templateCode);
            throw new CommonException(SmsErrorCode.TEMPLATE_NOT_FOUND);
        }

        return templateContent;
    }

    /**
     * SMS 수신자 검색
     * 
     * TODO: 실제 데이터 연동 시 필요한 작업들
     * 1. TLEV_BSCON 테이블 - 거래처 전화번호 데이터 정제 (TRANSLATE 함수 사용)
     * 2. TMEM_MB 테이블 - 회원 SMS 수신동의 여부 확인 (SMS_RECV_YN 컬럼)
     * 3. 성능 최적화 - 인덱스 생성 (이름, 전화번호 검색용)
     * 4. 데이터 보안 - 개인정보 마스킹 처리 필요
     */
    @Override
    @Transactional(readOnly = true)
    public PageListResponse<RecipientSearchResult> searchRecipients(RecipientSearchCondition condition) {
        log.info("수신자 검색 요청 - page: {}, pageSize: {}, keyword: {}", 
                condition.getPage(), condition.getPageSize(), condition.getSearchKeyword());

        // TODO: 검색 조건 보안 검증 - SQL Injection 방지
        // SortColumnValidator를 통해 정렬 컬럼 화이트리스트 검증 구현됨
        try (Page<RecipientSearchResult> page = PageHelper.startPage(condition.getPage(), condition.getPageSize())) {
            // TODO: 실제 거래처/회원 데이터 조회 - 개인정보 보호법 준수
            List<RecipientSearchResult> recipients = smsQueryMapper.searchRecipients(condition);

            PageInfo<RecipientSearchResult> pageInfo = new PageInfo<>(recipients);

            PageListResponse<RecipientSearchResult> result = PageListResponse.<RecipientSearchResult>builder()
                    .contents(pageInfo.getList())
                    .totalElements(pageInfo.getTotal())
                    .page(pageInfo.getPageNum())
                    .totalPages(pageInfo.getPages())
                    .pageSize(pageInfo.getPageSize())
                    .build();

            log.info("수신자 검색 완료 - 총 {}건, 현재 페이지: {}/{}", 
                    pageInfo.getTotal(), pageInfo.getPageNum(), pageInfo.getPages());

            return result;
        }
    }

    /**
     * SMS 발송 가능 여부 확인
     */
    @Override
    @Transactional(readOnly = true)
    public boolean isSmsEnabled() {
        String enabledFlag = smsQueryMapper.getSmsEnabledFlag();
        return "Y".equals(enabledFlag);
    }

    /**
     * 카카오 알림톡 발송 가능 여부 확인
     */
    @Override
    @Transactional(readOnly = true)
    public boolean isKakaoEnabled() {
        String enabledFlag = smsQueryMapper.getKakaoEnabledFlag();
        return "Y".equals(enabledFlag);
    }

    /**
     * 발신번호 유효성 검증
     * 
     * TODO: 실제 데이터 연동 시 필요한 작업들
     * 1. 통신사 등록된 발신번호 확인 - KT/SKT/LG 발신번호 등록 상태 확인
     * 2. 사용권한 확인 - 사용자별 발신번호 사용권한 체크
     * 3. 발신번호 변조 방지 - 허용된 번호만 사용 가능
     */
    @Override
    @Transactional(readOnly = true)
    public void validateSenderNumber(String phoneNumber, String senderType) {
        log.debug("발신번호 유효성 검증 - phoneNumber: {}, senderType: {}", phoneNumber, senderType);

        // TODO: 실제 통신사 발신번호 등록 상태 확인 API 연동
        boolean exists = smsQueryMapper.existsSenderNumber(phoneNumber, senderType);
        
        if (!exists) {
            log.warn("유효하지 않은 발신번호 - phoneNumber: {}, senderType: {}", phoneNumber, senderType);
            throw new CommonException(SmsErrorCode.INVALID_SENDER_NUMBER,
                    String.format("유효하지 않은 발신번호입니다. phoneNumber: %s, senderType: %s", phoneNumber, senderType));
        }

        log.debug("발신번호 유효성 검증 완료 - phoneNumber: {}", phoneNumber);
    }

    /**
     * 템플릿 코드 유효성 검증
     * 
     * TODO: 실제 데이터 연동 시 필요한 작업들
     * 1. 카카오 비즈 API - 템플릿 승인상태 실시간 확인
     * 2. 템플릿 관리 테이블 - 템플릿 정보 저장 및 관리
     * 3. 템플릿 변수 검증 - 메시지 내 변수 매칭 확인
     */
    @Override
    @Transactional(readOnly = true)
    public void validateTemplateCode(String templateCode) {
        log.debug("템플릿 코드 유효성 검증 - templateCode: {}", templateCode);

        if (templateCode == null || templateCode.trim().isEmpty()) {
            log.debug("템플릿 코드가 없어 검증 생략");
            return;
        }

        // TODO: 카카오 비즈 API로 템플릿 승인상태 실시간 확인 필요
        boolean exists = smsQueryMapper.existsTemplateCode(templateCode);
        
        if (!exists) {
            log.warn("유효하지 않은 템플릿 코드 - templateCode: {}", templateCode);
            throw new CommonException(SmsErrorCode.INVALID_TEMPLATE_CODE,
                    String.format("유효하지 않은 템플릿 코드입니다. templateCode: %s", templateCode));
        }

        log.debug("템플릿 코드 유효성 검증 완료 - templateCode: {}", templateCode);
    }

    /**
     * 선택된 수신자 목록 조회 (SB_1용)
     * 
     * TODO: 실제 데이터 연동 시 필요한 작업들
     * 1. TST_SMS_SELECTED_RECIPIENT 테이블 - DDL 실행 후 활성화
     * 2. 세션 관리 - Redis 또는 DB 기반 세션 타임아웃 처리
     * 3. 중복 선택 방지 - 동일 수신자 중복 선택 체크
     * 4. 대량 선택 제한 - 한번에 선택 가능한 수신자 수 제한
     */
    @Override
    @Transactional(readOnly = true)
    public SelectedRecipientList getSelectedRecipients(String userId) {
        log.info("선택된 수신자 목록 조회 요청 - userId: {}", userId);

        // TODO: TST_SMS_SELECTED_RECIPIENT 테이블 생성 후 실제 데이터 조회
        // DDL 스크립트 실행 필요: /resources/sql/sms_ddl.sql
        List<SelectedRecipientList.SelectedRecipientInfo> recipients = smsQueryMapper.getSelectedRecipients(userId);

        SelectedRecipientList result = SelectedRecipientList.builder()
                .recipients(recipients)
                .totalCount(recipients.size())
                .build();

        log.info("선택된 수신자 목록 조회 완료 - 총 {}건", recipients.size());

        return result;
    }
}