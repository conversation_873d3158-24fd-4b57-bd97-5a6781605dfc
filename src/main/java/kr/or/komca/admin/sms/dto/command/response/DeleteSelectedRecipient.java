package kr.or.komca.admin.sms.dto.command.response;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.ToString;

/**
 * 선택된 수신자 삭제 응답 DTO
 */
@Builder(toBuilder = true)
@AllArgsConstructor
@ToString
@Getter
@Schema(description = "선택된 수신자 삭제 응답")
public class DeleteSelectedRecipient {

    @Schema(description = "수신자 ID", example = "BSCON001")
    private String recipientId;

    @Schema(description = "수신자 타입", example = "BSCON")
    private String recipientType;

    @Schema(description = "영향 받은 행 수", example = "1")
    private int affectedRows;
}