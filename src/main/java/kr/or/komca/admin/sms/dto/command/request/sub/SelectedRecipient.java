package kr.or.komca.admin.sms.dto.command.request.sub;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Pattern;
import lombok.*;

/**
 * SB_2에서 선택된 수신자 정보 DTO
 */
@Getter
@NoArgsConstructor
@AllArgsConstructor
@Builder
@ToString
@Schema(description = "선택된 수신자 정보")
public class SelectedRecipient {

    /** 수신자 ID */
    @NotBlank
    @Schema(description = "수신자 ID", example = "BSCON001")
    private String recipientId;

    /** 수신자 타입 */
    @NotBlank
    @Schema(description = "수신자 타입", example = "BSCON", allowableValues = {"BSCON", "MEMBER"})
    private String recipientType;

    /** 수신자명 */
    @Schema(description = "수신자명", example = "한국음악저작권협회")
    private String recipientName;

    /** 선택된 전화번호 */
    @NotBlank
    @Pattern(regexp = "^[0-9]{2,3}-[0-9]{3,4}-[0-9]{4}$")
    @Schema(description = "선택된 전화번호", example = "02-2660-0400")
    private String selectedPhoneNumber;

    /** 전화번호 타입 */
    @Schema(description = "전화번호 타입", example = "PHONE1", allowableValues = {"PHONE1", "PHONE2"})
    private String phoneNumberType = "PHONE1";
}