package kr.or.komca.admin.sms.dto.query.condition;

import io.swagger.v3.oas.annotations.media.Schema;
import kr.or.komca.admin.sms.util.RecipientSortColumnValidator;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.ToString;

/**
 * SMS 수신자 검색 조건 DTO
 */
@ToString
@Getter
@AllArgsConstructor
@Builder(toBuilder = true)
@Schema(description = "SMS 수신자 검색 조건")
public class RecipientSearchCondition {

    /** 페이지 번호 */
    @Schema(description = "페이지 번호", example = "1")
    private int page;

    /** 페이지 크기 */
    @Schema(description = "페이지 크기", example = "20")
    private int pageSize;

    /** 정렬할 컬럼 **/
    @Schema(description = "정렬할 컬럼", example = "name")
    private String sortColumn;

    /** 정렬 순서 1: asc. 2: desc  */
    @Schema(description = "정렬 순서 (1: 오름차순, 2: 내림차순)", example = "1", allowableValues = {"1", "2"})
    private String sortOrder;

    /** 검색어 (이름/상호) */
    @Schema(description = "검색어 (이름/상호)")
    private String searchKeyword;

    /** 수신자 타입 */
    @Schema(description = "수신자 타입", allowableValues = {"ALL", "BSCON", "MEMBER"})
    private String recipientType = "ALL";

    /** 업종 코드 */
    @Schema(description = "업종 코드")
    private String businessType;

    /** SMS 수신동의 여부 */
    @Schema(description = "SMS 수신동의 여부", allowableValues = {"Y", "N"})
    private String smsReceiveYn;

    public String getSortColumn() {
        return RecipientSortColumnValidator.validate(this.sortColumn);
    }

    public String getSortOrder() {
        if ("1".equals(this.sortOrder)) return "ASC";
        if ("2".equals(this.sortOrder)) return "DESC";
        return "";
    }
}