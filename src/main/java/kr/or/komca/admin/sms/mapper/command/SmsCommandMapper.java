package kr.or.komca.admin.sms.mapper.command;

import kr.or.komca.admin.sms.dto.command.request.SendSmsRequest;
import kr.or.komca.admin.sms.dto.command.request.sub.SelectedRecipient;
import kr.or.komca.admin.sms.dto.command.request.sub.SmsRecipient;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * SMS Command Mapper 인터페이스
 */
@Mapper
public interface SmsCommandMapper {

    /**
     * SMS 발송 이력 저장
     */
    int insertSmsMessage(
            @Param("inspersId") String inspersId,
            @Param("command") SendSmsRequest command,
            @Param("recipient") SmsRecipient recipient,
            @Param("channel") String channel
    );

    /**
     * SMS 발송 상태 업데이트
     */
    int updateSmsStatus(
            @Param("msgId") String msgId,
            @Param("status") String status,
            @Param("resultMessage") String resultMessage
    );

    /**
     * 선택된 수신자 임시 저장
     */
    int insertSelectedRecipient(
            @Param("userId") String userId,
            @Param("recipient") SelectedRecipient recipient
    );

    /**
     * 기존 선택된 수신자 목록 삭제
     */
    int deleteSelectedRecipients(
            @Param("userId") String userId
    );

    /**
     * 특정 선택된 수신자 삭제
     */
    int deleteSelectedRecipient(
            @Param("userId") String userId,
            @Param("recipientId") String recipientId,
            @Param("recipientType") String recipientType
    );

    /**
     * 선택된 수신자 수 조회
     */
    int countSelectedRecipients(
            @Param("userId") String userId
    );
}