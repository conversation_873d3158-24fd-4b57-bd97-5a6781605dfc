package kr.or.komca.admin.sms.dto.query.response;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Builder;
import lombok.Getter;
import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Map;

/**
 * SMS 화면 초기화 데이터 응답 DTO
 */
@Getter
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Schema(description = "SMS 화면 초기화 데이터 응답")
public class SmsInitData {

    /** 발신번호 목록 */
    @Schema(description = "발신번호 목록")
    private List<SenderNumber> senderNumbers;

    /** 카카오 템플릿 목록 */
    @Schema(description = "카카오 템플릿 목록")
    private List<KakaoTemplate> kakaoTemplates;

    /** SMS 발송 활성화 여부 */
    @Schema(description = "SMS 발송 활성화 여부", example = "Y")
    private String smsEnabled;

    /** 카카오 발송 활성화 여부 */
    @Schema(description = "카카오 발송 활성화 여부", example = "Y")
    private String kakaoEnabled;

    /**
     * 발신번호 정보
     */
    @Getter
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    @Schema(description = "발신번호 정보")
    public static class SenderNumber {
        
        /** 발신번호구분 */
        @Schema(description = "발신번호구분", example = "01")
        private String senderType;
        
        /** 발신번호구분명 */
        @Schema(description = "발신번호구분명", example = "대표번호")
        private String senderTypeName;
        
        /** 발신번호 */
        @Schema(description = "발신번호", example = "02-2660-0400")
        private String phoneNumber;
        
        /** 사용여부 */
        @Schema(description = "사용여부", example = "Y")
        private String useYn;
    }

    /**
     * 카카오 템플릿 정보
     */
    @Getter
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    @Schema(description = "카카오 템플릿 정보")
    public static class KakaoTemplate {
        
        /** 템플릿 코드 */
        @Schema(description = "템플릿 코드", example = "TEST001")
        private String templateCode;
        
        /** 템플릿명 */
        @Schema(description = "템플릿명", example = "테스트 템플릿")
        private String templateName;
        
        /** 템플릿 내용 */
        @Schema(description = "템플릿 내용", example = "안녕하세요. KOMCA입니다.")
        private String templateContent;
        
        /** 승인상태 */
        @Schema(description = "승인상태", example = "APPROVED")
        private String approvalStatus;
        
        /** 사용여부 */
        @Schema(description = "사용여부", example = "Y")
        private String useYn;
    }
}