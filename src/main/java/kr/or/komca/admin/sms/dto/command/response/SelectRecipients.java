package kr.or.komca.admin.sms.dto.command.response;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;

/**
 * 수신자 선택 결과 응답 DTO
 */
@NoArgsConstructor
@AllArgsConstructor
@Getter
@Builder
@Schema(description = "수신자 선택 결과 응답")
public class SelectRecipients {

    /** 선택된 수신자 수 */
    @Schema(description = "선택된 수신자 수", example = "5")
    private int selectedCount;

    /** 총 저장된 수신자 수 */
    @Schema(description = "총 저장된 수신자 수", example = "8")
    private int totalCount;

    /** 세션 ID */
    @Schema(description = "세션 ID", example = "SESSION_12345")
    private String sessionId;

    /** 선택 모드 */
    @Schema(description = "선택 모드", example = "REPLACE")
    private String selectMode;
}