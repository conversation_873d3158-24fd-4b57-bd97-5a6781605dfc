package kr.or.komca.admin.sms.dto.command.request;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.Valid;
import jakarta.validation.constraints.*;
import kr.or.komca.admin.sms.dto.command.request.sub.SmsRecipient;
import lombok.*;

import java.util.List;

/**
 * SMS 발송 요청 DTO
 */
@Getter
@NoArgsConstructor
@AllArgsConstructor
@Builder
@ToString
@Schema(description = "SMS 발송 요청")
public class SendSmsRequest {

    /** SMS 메시지 */
    @NotBlank
    @Size(max = 2000)
    @Schema(description = "SMS 메시지", example = "안녕하세요. KOMCA입니다.")
    private String message;

    /** 제목 */
    @Size(max = 100)
    @Schema(description = "제목", example = "KOMCA 알림")
    private String subject;

    /** 발신번호 */
    @NotBlank
    @Pattern(regexp = "^[0-9]{2,3}-[0-9]{3,4}-[0-9]{4}$")
    @Schema(description = "발신번호", example = "02-2660-0400")
    private String callback;

    /** 수신자 목록 */
    @NotEmpty
    @Valid
    @Schema(description = "수신자 목록")
    private List<SmsRecipient> recipients;

    /** 즉시 발송 여부 */
    @Pattern(regexp = "^[YN]$")
    @Schema(description = "즉시 발송 여부", example = "Y", allowableValues = {"Y", "N"})
    private String immediateYn = "Y";

    /** 발송 채널 */
    @Pattern(regexp = "^[SA]$")
    @Schema(description = "발송 채널", example = "S", allowableValues = {"S", "A"})
    private String channel = "S"; // S=SMS, A=알림톡

    /** 발신번호구분 */
    @Schema(description = "발신번호구분", example = "01", allowableValues = {"01", "02"})
    private String senderType = "01"; // 01=대표번호, 02=일반번호

    /** 템플릿 코드 (알림톡용) */
    @Schema(description = "템플릿 코드 (알림톡용)", example = "TEST001")
    private String templateCode;
}