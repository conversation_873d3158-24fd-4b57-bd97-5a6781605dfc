package kr.or.komca.admin.sms.dto.command.request.sub;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.*;
import lombok.*;

/**
 * SMS 수신자 정보 DTO
 */
@Getter
@NoArgsConstructor
@AllArgsConstructor
@Builder
@ToString
@Schema(description = "SMS 수신자 정보")
public class SmsRecipient {

    /** 수신자 ID */
    @Schema(description = "수신자 ID", example = "BSCON001")
    private String recipientId;

    /** 수신자 타입 */
    @Schema(description = "수신자 타입", example = "BSCON", allowableValues = {"BSCON", "MEMBER", "DIRECT"})
    private String recipientType;

    /** 전화번호 */
    @NotBlank
    @Pattern(regexp = "^[0-9]{2,3}-[0-9]{3,4}-[0-9]{4}$")
    @Schema(description = "전화번호", example = "010-1234-5678")
    private String phoneNumber;

    /** 수신자명 */
    @Schema(description = "수신자명", example = "홍길동")
    private String recipientName;
}